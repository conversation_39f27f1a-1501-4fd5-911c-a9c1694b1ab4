import React from 'react';
import { render, screen } from '@testing-library/react';
import Chat<PERSON><PERSON> from '../ChatArea';
import { ChatContext } from '@/providers/ChatProvider';
import { AppContext } from '@/providers/AppProvider';
import '@testing-library/jest-dom';
import { MessageNode } from '@/lib/supabase/types';
import { ChatSession } from '@/providers/ChatProvider';
void React;

// Update usePartialMessages mock to prevent issues
jest.mock('../hooks/usePartialMessages', () => ({
  usePartialMessages: jest.fn(),
}));

jest.mock('../hooks/useChatHistory', () => ({
  useChatHistory: () => ({
    isLoading: false,
  }),
}));

jest.mock('@/providers/SubscriptionProvider', () => ({
  useSubscription: () => ({
    accessibleModelIds: ['gpt-4', 'claude-3-sonnet'],
    canSendMessage: true,
    messagesRemaining: 100,
    canAccessModelTier: () => true,
  }),
}));

jest.mock('../hooks/useChatModels', () => ({
  useChatModels: () => ({
    canSearch: true,
    canUpload: true,
  }),
}));

// Create a mock array that will always be returned
const mockMessagesArray: MessageNode[] = [];

// Mock the useMessageTree hook with a more reliable implementation
jest.mock('../hooks/useMessageTree', () => ({
  useMessageTree: () => ({
    getFlattenedMessages: jest.fn().mockImplementation(() => mockMessagesArray),
    handleBranchChange: jest.fn(),
  }),
}));

jest.mock('../hooks/useSendMessage', () => ({
  useSendMessage: () => ({
    handleSendMessage: jest.fn(),
    handleRetryMessage: jest.fn(),
    handleUpload: jest.fn(),
    isUploading: false,
    processingModels: [],
    editMessage: jest.fn(),
    handleRemoveFile: jest.fn(),
  }),
}));

// Mock ChatContent and ChatInput components
jest.mock('../ChatContent', () => ({
  __esModule: true,
  default: jest.fn(() => <div data-testid='chat-content'>Chat Content</div>),
}));

jest.mock('../ChatInput', () => ({
  __esModule: true,
  default: jest.fn(() => <div data-testid='chat-input'>Chat Input</div>),
}));

describe('ChatArea - Partial Message Integration', () => {
  // Test data for partial messages
  const mockPartialMessages = new Map();

  // Create a sample message tree
  const createMessageTree = (): MessageNode => {
    const assistantMessage: MessageNode = {
      id: 'assistant-id',
      content: 'Regular content',
      role: 'assistant',
      created_at: new Date().toISOString(),
      children: [],
      conversation_id: null,
      metadata: {},
      model_id: null,
      parent_message_id: null,
      provider_id: null,
      attachments: [],
      tokens_used: null,
      updated_at: null,
      modelData: null,
      annotations: [],
    };

    const userMessage: MessageNode = {
      id: 'user-id',
      content: 'User question',
      role: 'user',
      created_at: new Date().toISOString(),
      children: [assistantMessage],
      conversation_id: null,
      metadata: {},
      model_id: null,
      parent_message_id: null,
      provider_id: null,
      attachments: [],
      tokens_used: null,
      updated_at: null,
      modelData: null,
      annotations: [],
    };

    return {
      id: 'root-id',
      content: '',
      role: 'system',
      created_at: new Date().toISOString(),
      children: [userMessage],
      conversation_id: null,
      metadata: {},
      model_id: null,
      parent_message_id: null,
      provider_id: null,
      attachments: [],
      tokens_used: null,
      updated_at: null,
      modelData: null,
      annotations: [],
    };
  };

  beforeEach(() => {
    mockPartialMessages.clear();

    // Update our mockMessagesArray with tree nodes for this test
    const messageTree = createMessageTree();
    mockMessagesArray.length = 0; // Clear the array
    mockMessagesArray.push(
      messageTree,
      messageTree.children[0],
      messageTree.children[0].children[0]
    );
  });

  test('should use partial message content when rendering messages', () => {
    // Set up mock data
    const messageTree = createMessageTree();
    const session: ChatSession = {
      id: 'session-id',
      model: {
        id: 'gpt-4',
        display_name: 'GPT-4',
        provider_id: 'provider-id',
        is_active: true,
        is_visible_by_default: true,
        allows_file_upload: true,
        allows_search: true,
        allows_tool_usage: false,
        config: {},
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        max_tokens: 1000,
        name: 'gpt-4',
        openrouter_name: 'gpt-4',
        priority: 0,
        tier: 'free',
        context_length: 8192,
        description: 'GPT-4 model for testing',
        last_synced: new Date().toISOString(),
        capabilities: {
          file_upload: true,
          web_search: true,
          visible_by_default: true,
          image_generation: false,
          code_generation: true,
          function_calling: false,
          reasoning: false,
          structured_output: false,
        },
        supported_parameters: ['temperature', 'max_tokens'],
        architecture: {
          modality: 'text',
          input_modalities: ['text'],
          output_modalities: ['text'],
          tokenizer: 'gpt-4',
          instruct_type: null,
        },
        pricing: {
          prompt: '0.03',
          completion: '0.06',
        },
      },
      conversationId: 'conv-id',
      conversationState: 'healthy',
      parentMessageNode: messageTree,
    };

    // Add a partial message with more content
    mockPartialMessages.set('assistant-id', {
      content: 'This is partial message content that should be used',
      annotations: [],
    });

    // Render the component with necessary providers
    render(
      <AppContext.Provider
        value={{
          isTestMode: false,
          isMobile: false,
          setIsTestMode: jest.fn(),
          isSidebarOpen: false,
          viewportHeight: 1000,
          toggleSidebar: jest.fn(),
        }}
      >
        <ChatContext.Provider
          value={{
            providers: [],
            setProviders: jest.fn(),
            chatSessions: [session],
            setChatSessions: jest.fn(),
            isSidebarOpen: true,
            toggleSidebar: jest.fn(),
            selectedConversation: null,
            isChatLoading: false,
            isConversationListLoading: false,
            isBackgroundRefreshing: false,
            hasInitiallyLoaded: true,
            setIsChatLoading: jest.fn(),
            loadedConversations: [],
            fetchUserConversations: jest.fn(),
            fetchMoreConversations: jest.fn(),
            hasMoreConversations: false,
            isFetchingMoreConversations: false,
            setSelectedConversation: jest.fn(),
            initializeNewChat: jest.fn(),
            setUserConversations: jest.fn(),
            performSearch: jest.fn(),
            fetchRecentConversations: jest.fn(),
            subscribeToStream: jest.fn(),
            unsubscribeFromStream: jest.fn(),
            partialMessages: mockPartialMessages,
            getPartialMessage: (id) => mockPartialMessages.get(id),
            clearPartialMessage: jest.fn(),
            sseConnections: new Map(),
            appDefaultModel: {
              id: 'gpt-4',
              display_name: 'GPT-4',
              provider_id: 'provider-id',
              allows_file_upload: true,
              allows_search: true,
              allows_tool_usage: false,
              config: {},
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
              max_tokens: 1000,
              name: 'gpt-4',
              openrouter_name: 'gpt-4',
              priority: 0,
              tier: 'free',
              is_active: true,
              is_visible_by_default: true,
              context_length: 8192,
              description: 'GPT-4 model for testing',
              last_synced: new Date().toISOString(),
              capabilities: {
                file_upload: true,
                web_search: true,
                visible_by_default: true,
                image_generation: false,
                code_generation: true,
                function_calling: false,
                reasoning: false,
                structured_output: false,
              },
              supported_parameters: ['temperature', 'max_tokens'],
              architecture: {
                modality: 'text',
                input_modalities: ['text'],
                output_modalities: ['text'],
                tokenizer: 'gpt-4',
                instruct_type: null,
              },
              pricing: {
                prompt: '0.03',
                completion: '0.06',
              },
            },
            lastModelSelection: null,
            getModelSelectionReason: () => null,
            updateSessionModelId: jest.fn(),
          }}
        >
          <ChatArea
            groupConversationId='group-id'
            isTemporary={false}
            setIsTemporary={jest.fn()}
            chatState='chat'
          />
        </ChatContext.Provider>
      </AppContext.Provider>
    );

    // Verify the test renders successfully
    expect(screen.getByTestId('chat-content')).toBeInTheDocument();
  });
});
