import { Component, ReactNode } from 'react';
import { AppError } from '@/lib/error';
import { logger } from '@/lib/logger';

interface Props {
  children: ReactNode;
  fallback: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Log the error
    logger.error({
      error: error instanceof AppError ? error.toJSON() : error,
      componentStack: errorInfo.componentStack
    }, 'React component error');

    // Report to Bugsnag with React-specific context
    this.reportToBugsnag(error, errorInfo);

    // Report to error monitoring services (handles both Sentry and Bugsnag)
    // This will only report in production environment
    if (!(error instanceof AppError)) {
      // For non-AppError instances, use handleError which will report to monitoring services
      try {
        // eslint-disable-next-line @typescript-eslint/no-require-imports
        const { handleError } = require('@/lib/error');
        handleError(error);
      } catch (importError) {
        console.error('Failed to import handleError:', importError);
      }
    }
    // AppError instances are already reported in their constructor
  }

  private reportToBugsnag = (error: Error, errorInfo: React.ErrorInfo) => {
    // Only report to Bugsnag in production and browser environment
    if (process.env.NODE_ENV === 'production' && typeof window !== 'undefined') {
      // Dynamic import to avoid SSR issues
      import('@bugsnag/js')
        .then((BugsnagModule) => {
          const Bugsnag = BugsnagModule.default;
          if (Bugsnag && typeof Bugsnag.notify === 'function') {
            Bugsnag.notify(error, (event: {
              addMetadata: ((section: string, key: string, value: unknown) => void) &
                          ((section: string, metadata: Record<string, unknown>) => void);
              setUser?: (id: string, email?: string, name?: string) => void;
              context?: string;
            }) => {
              // Add React-specific metadata
              event.addMetadata('react', {
                componentStack: errorInfo.componentStack,
                errorBoundary: 'ErrorBoundary',
                timestamp: new Date().toISOString(),
              });

              // Add error context
              event.addMetadata('error', {
                name: error.name,
                message: error.message,
                stack: error.stack,
                isAppError: error instanceof AppError,
              });

              // Set context for better categorization
              event.context = 'React Error Boundary';

              // Add user context if available
              try {
                const userId = localStorage.getItem('userId');
                if (userId && event.setUser) {
                  event.setUser(userId);
                }
              } catch (storageError) {
                // localStorage might not be available
                console.warn('Could not access localStorage for user context:', storageError);
              }

              // Add component hierarchy if available
              if (errorInfo.componentStack) {
                const componentNames = errorInfo.componentStack
                  .split('\n')
                  .filter(line => line.trim().startsWith('at '))
                  .map(line => line.trim().replace('at ', ''))
                  .slice(0, 5); // Limit to top 5 components

                event.addMetadata('react', 'componentHierarchy', componentNames);
              }

              return true;
            });
          }
        })
        .catch((bugsnagError) => {
          console.warn('Bugsnag not available for ErrorBoundary:', bugsnagError);
        });
    }
  };

  render() {
    if (this.state.hasError) {
      return this.props.fallback;
    }
    return this.props.children;
  }
}