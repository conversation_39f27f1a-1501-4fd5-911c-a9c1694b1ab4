import * as Sentry from '@sentry/nextjs';

export async function register() {
  if (process.env.NEXT_RUNTIME === 'nodejs') {
    // Initialize error monitoring services
    await import('../sentry.server.config');
    await import('../bugsnag.server.config');
    await import('pino');

    const opentelemetry = await import('@opentelemetry/sdk-node');
    const { OTLPTraceExporter } = await import(
      '@opentelemetry/exporter-trace-otlp-http'
    );
    const { getNodeAutoInstrumentations } = await import(
      '@opentelemetry/auto-instrumentations-node'
    );


    const sdk = new opentelemetry.NodeSDK({
      serviceName: 'sabi-chat-backend',
      traceExporter: new OTLPTraceExporter({
        url: process.env.HONEYCOMB_API_ENDPOINT || 'https://api.honeycomb.io/v1/traces',
        headers: {
          'x-honeycomb-team': process.env.HONEYCOMB_API_KEY || '',
        },
      }),
      instrumentations: [
        getNodeAutoInstrumentations({
          '@opentelemetry/instrumentation-fs': {
            enabled: false,
          },
        }),
      ],
    });
    try {
      sdk.start();
    } catch (e) {
      console.error(e);
    }
  }

  if (process.env.NEXT_RUNTIME === 'edge') {
    // Initialize error monitoring services for edge runtime
    // Note: Only Sentry is compatible with edge runtime
    await import('../sentry.edge.config');
  }
}

export const onRequestError = Sentry.captureRequestError;
