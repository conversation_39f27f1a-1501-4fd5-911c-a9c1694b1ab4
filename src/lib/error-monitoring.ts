/**
 * Client-side error monitoring initialization
 * This file handles the initialization of both Sentry and Bugsnag on the client side
 */

'use client';

// Initialize client-side error monitoring
export function initializeClientErrorMonitoring() {
  // Only initialize in production and on the client side
  if (process.env.NODE_ENV === 'production' && typeof window !== 'undefined') {
    // Initialize Sentry client config
    try {
      // eslint-disable-next-line @typescript-eslint/no-require-imports
      require('../../sentry.client.config');
    } catch (error) {
      console.warn('Failed to initialize Sentry client:', error);
    }

    // Initialize Bugsnag client config
    try {
      // eslint-disable-next-line @typescript-eslint/no-require-imports
      require('../../bugsnag.client.config');
    } catch (error) {
      console.warn('Failed to initialize Bugsnag client:', error);
    }
  }
}

// Only auto-initialize when this module is imported on the client
// and avoid any server-side execution
if (typeof window !== 'undefined' && typeof document !== 'undefined') {
  // Initialize immediately to capture early errors
  initializeClientErrorMonitoring();
}
