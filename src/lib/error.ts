import { logger } from '@/lib/logger';
import * as Sen<PERSON> from '@sentry/nextjs';

// Conditional Bugsnag import - only in production and not in edge runtime
let Bugsnag: { notify: (error: Error, callback?: (event: unknown) => void) => void } | null = null;
if (process.env.NODE_ENV === 'production' && process.env.NEXT_RUNTIME !== 'edge') {
  try {
    // Use the appropriate Bugsnag package based on runtime
    if (typeof window !== 'undefined') {
      // Browser environment
      // eslint-disable-next-line @typescript-eslint/no-require-imports
      Bugsnag = require('@bugsnag/js');
    } else {
      // Node.js server environment
      // eslint-disable-next-line @typescript-eslint/no-require-imports
      Bugsnag = require('@bugsnag/node');
    }
  } catch (error) {
    // Bugsnag not available, continue without it
    console.warn('Bugsnag not available:', error);
  }
}

type ErrorMetadata = {
  timestamp?: string;
  [key: string]: string | number | boolean | undefined;
};

/**
 * Reports errors to both <PERSON>try and Bugsnag in production environment
 * In development, only logs to console to avoid noise
 */
function reportError(
  error: Error,
  metadata: {
    tags?: Record<string, string>;
    extra?: Record<string, unknown>;
    context?: string;
  } = {}
) {
  // Only report to external services in production
  if (process.env.NODE_ENV === 'production') {
    // Report to Sentry
    try {
      Sentry.captureException(error, {
        tags: metadata.tags,
        extra: metadata.extra,
        contexts: metadata.context ? { custom: { context: metadata.context } } : undefined,
      });
    } catch (sentryError) {
      console.error('Failed to report to Sentry:', sentryError);
    }

    // Report to Bugsnag if available
    if (Bugsnag) {
      try {
        Bugsnag.notify(error, (event: unknown) => {
          const bugsnagEvent = event as {
            addMetadata: (section: string, key: string, value: unknown) => void;
            context?: string;
          };
          if (metadata.tags) {
            Object.entries(metadata.tags).forEach(([key, value]) => {
              bugsnagEvent.addMetadata('tags', key, value);
            });
          }
          if (metadata.extra) {
            bugsnagEvent.addMetadata('extra', 'data', metadata.extra);
          }
          if (metadata.context) {
            bugsnagEvent.context = metadata.context;
          }
        });
      } catch (bugsnagError) {
        console.error('Failed to report to Bugsnag:', bugsnagError);
      }
    }
  }
}

// Base error class for all application errors
export class AppError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 500,
    public cause?: Error,
    public metadata: ErrorMetadata = {}
  ) {
    super(message, { cause });
    this.name = this.constructor.name;

    // Log error when created
    logger.error(
      {
        error: this.toJSON(),
        metadata,
        stack: this.stack,
      },
      `${this.name} occurred`
    );

    // Report to error monitoring services
    reportError(this, {
      tags: {
        errorCode: this.code,
        statusCode: this.statusCode.toString(),
        errorType: this.name,
      },
      extra: {
        ...metadata,
        cause: this.cause?.message,
        stack: this.stack,
      },
      context: `${this.name}: ${this.code}`,
    });
  }

  static from(
    error: Error,
    code: string = 'INTERNAL_ERROR',
    statusCode: number = 500
  ) {
    return new AppError(error.message, code, statusCode, error);
  }

  toJSON() {
    return {
      name: this.name,
      message: this.message,
      code: this.code,
      statusCode: this.statusCode,
      cause: this.cause?.message,
      metadata: this.metadata,
    };
  }
}

// AI/LLM related errors
export class AiError extends AppError {
  constructor(
    message: string,
    code: ErrorCode = ErrorCode.AI_COMPLETION,
    metadata: ErrorMetadata = {}
  ) {
    super(message, code, 500, undefined, metadata);
  }

  static streamingError(message: string, cause?: Error) {
    return new AiError(message, ErrorCode.AI_STREAMING, {
      streamingFailed: true,
      timestamp: new Date().toISOString(),
      cause: cause?.message,
    });
  }

  static contentBlocked(message: string, cause?: Error) {
    return new AiError(message, ErrorCode.AI_CONTENT_BLOCKED, {
      contentBlocked: true,
      timestamp: new Date().toISOString(),
      cause: cause?.message,
    });
  }

  static rateLimitError(message: string, retryAfter?: number) {
    return new AiError(message, ErrorCode.AI_RATE_LIMIT, {
      retryAfter,
      timestamp: new Date().toISOString(),
    });
  }
}

// Database related errors
export class DatabaseError extends AppError {
  constructor(message: string, code: string = 'DATABASE_ERROR', cause?: Error) {
    super(message, code, 500, cause);
  }

  static from(error: Error) {
    return new DatabaseError(error.message, 'DATABASE_ERROR', error);
  }
}

// API related errors
export class ApiError extends AppError {
  constructor(
    message: string,
    code: string = 'API_ERROR',
    statusCode: number = 500,
    cause?: Error
  ) {
    super(message, code, statusCode, cause);
  }

  static override from(
    error: Error,
    code: string = 'API_ERROR',
    statusCode: number = 500
  ) {
    return new ApiError(error.message, code, statusCode, error);
  }
}

// Authentication related errors
export class AuthError extends AppError {
  constructor(message: string, code: string = 'AUTH_ERROR', cause?: Error) {
    super(message, code, 401, cause);
  }

  static from(error: Error) {
    return new AuthError(error.message, 'AUTH_ERROR', error);
  }
}

// Validation related errors
export class ValidationError extends AppError {
  constructor(
    message: string,
    code: string = 'VALIDATION_ERROR',
    cause?: Error
  ) {
    super(message, code, 400, cause);
  }

  static from(error: Error) {
    return new ValidationError(error.message, 'VALIDATION_ERROR', error);
  }
}

// Rate limiting errors
export class RateLimitError extends AppError {
  constructor(
    message: string,
    code: string = 'RATE_LIMIT_ERROR',
    cause?: Error
  ) {
    super(message, code, 429, cause);
  }

  static from(error: Error) {
    return new RateLimitError(error.message, 'RATE_LIMIT_ERROR', error);
  }
}

// Network related errors
export class NetworkError extends AppError {
  constructor(message: string, code: string = 'NETWORK_ERROR', cause?: Error) {
    super(message, code, 503, cause);
  }

  static from(error: Error) {
    return new NetworkError(error.message, 'NETWORK_ERROR', error);
  }
}

// Error type guard
export function isAppError(error: unknown): error is AppError {
  return error instanceof AppError;
}

// Error handler utility
export function handleError(error: unknown): AppError {
  if (isAppError(error)) {
    return error;
  }

  if (error instanceof Error) {
    // Report non-AppError instances directly before wrapping
    reportError(error, {
      tags: {
        errorType: 'UnhandledError',
        originalType: error.constructor.name,
      },
      extra: {
        originalMessage: error.message,
        stack: error.stack,
      },
      context: 'handleError: Non-AppError instance',
    });
    return AppError.from(error);
  }

  // For unknown errors
  const appError = new AppError(
    typeof error === 'string' ? error : 'An unknown error occurred',
    'UNKNOWN_ERROR'
  );

  // Already captured in constructor
  return appError;
}

export enum ErrorCode {
  // Generic errors
  UNKNOWN = 'UNKNOWN_ERROR',
  INTERNAL = 'INTERNAL_ERROR',

  // AI/LLM errors
  AI_COMPLETION = 'AI_COMPLETION_ERROR',
  AI_STREAMING = 'AI_STREAMING_ERROR',
  AI_RATE_LIMIT = 'AI_RATE_LIMIT_ERROR',
  AI_CONTENT_BLOCKED = 'AI_CONTENT_BLOCKED',
  // Database errors
  DATABASE_ERROR = 'DATABASE_ERROR',

  // API errors
  API_VALIDATION = 'API_VALIDATION_ERROR',
  API_UNAUTHORIZED = 'API_UNAUTHORIZED',
  API_RESOURCE_EXHAUSTED = 'API_RESOURCE_EXHAUSTED',
  API_NOT_FOUND = 'API_NOT_FOUND',

  // Auth errors
  AUTH_INVALID_CREDENTIALS = 'AUTH_INVALID_CREDENTIALS',
  AUTH_TOKEN_EXPIRED = 'AUTH_TOKEN_EXPIRED',
  AUTH_INSUFFICIENT_PERMISSIONS = 'AUTH_INSUFFICIENT_PERMISSIONS',

  // Invalid request errors
  INVALID_REQUEST = 'INVALID_REQUEST',
}
