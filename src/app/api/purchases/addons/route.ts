import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { stripe, STRIPE_PRICE_IDS, createStripeCustomer } from '@/lib/stripe';
import { logger } from '@/lib/logger';
import { getUserSubscription, createSubscription, updateSubscription } from '@/lib/supabase/subscription';

const log = logger.child({ module: 'addon-purchases' });

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { addonType, quantity = 1 } = await request.json();

    if (!addonType || !['tokens', 'images'].includes(addonType)) {
      return NextResponse.json(
        { error: 'Invalid addon type' },
        { status: 400 }
      );
    }

    // Get user's subscription or create one if it doesn't exist
    let subscription = await getUserSubscription(user.id);
    console.log('subscription', subscription);

    if (!subscription) {
      log.info('Creating new subscription for user', { userId: user.id });
      subscription = await createSubscription(user.id);
    }

    let customerId = subscription.stripe_customer_id;

    // Create Stripe customer if one doesn't exist
    if (!customerId) {
      log.info('Creating Stripe customer for user', { userId: user.id });
      const customer = await createStripeCustomer(
        user.email!,
        user.user_metadata?.name
      );
      customerId = customer.id;

      // Update the subscription with the Stripe customer ID
      await updateSubscription(subscription.id, {
        stripe_customer_id: customerId,
      });

      log.info('Updated subscription with Stripe customer ID', {
        userId: user.id,
        customerId,
        subscriptionId: subscription.id
      });
    }

    const origin = request.headers.get('origin') || 'http://localhost:3000';
    let priceId: string;
    let metadata: Record<string, string>;

    if (addonType === 'tokens') {
      priceId = STRIPE_PRICE_IDS.token_pack_100k;
      metadata = {
        userId: user.id,
        addonType: 'tokens',
        tokens: (quantity * 100000).toString(),
      };
    } else {
      priceId = STRIPE_PRICE_IDS.image_pack_1000;
      metadata = {
        userId: user.id,
        addonType: 'images',
        credits: (quantity * 1000).toString(),
      };
    }

    // Create checkout session for one-time payment
    const session = await stripe.checkout.sessions.create({
      customer: customerId,
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: quantity,
        },
      ],
      mode: 'payment',
      success_url: `${origin}/settings?success=${addonType}&quantity=${quantity}`,
      cancel_url: `${origin}/settings?canceled=true`,
      metadata,
    });

    log.info('Created addon checkout session', {
      userId: user.id,
      addonType,
      quantity,
      customerId,
    });

    return NextResponse.json({ url: session.url });
  } catch (error) {
    console.error('Error creating addon checkout session:', error);
    log.error('Error creating addon checkout session:', error);
    return NextResponse.json(
      { error: 'Failed to create checkout session' },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get available add-on pricing info
    const addonPricing = {
      tokens: {
        name: 'Token Pack',
        description: '100,000 additional tokens',
        price: '$3.00',
        priceId: STRIPE_PRICE_IDS.token_pack_100k,
      },
      images: {
        name: 'Image Credits',
        description: '1,000 image generation credits',
        price: '$5.00',
        priceId: STRIPE_PRICE_IDS.image_pack_1000,
      },
    };

    return NextResponse.json(addonPricing);
  } catch (error) {
    log.error('Error fetching addon pricing:', error);
    return NextResponse.json(
      { error: 'Failed to fetch addon pricing' },
      { status: 500 }
    );
  }
}
