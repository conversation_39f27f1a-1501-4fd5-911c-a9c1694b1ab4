'use client';

import * as Sen<PERSON> from '@sentry/nextjs';
import NextError from 'next/error';
import { useEffect } from 'react';

export default function GlobalError({
  error,
}: {
  error: Error & { digest?: string };
}) {
  useEffect(() => {
    // Only report to external services in production
    if (process.env.NODE_ENV === 'production') {
      // Report to Sentry
      try {
        Sentry.captureException(error, {
          tags: {
            errorBoundary: 'global',
            digest: error.digest,
          },
          extra: {
            digest: error.digest,
            timestamp: new Date().toISOString(),
          },
        });
      } catch (sentryError) {
        console.error('Failed to report to Sentry:', sentryError);
      }

      // Report to Bugsnag if available (dynamic import to avoid SSR issues)
      if (typeof window !== 'undefined') {
        import('@bugsnag/js')
          .then((BugsnagModule) => {
            const Bugsnag = BugsnagModule.default;
            if (Bugsnag && typeof Bugsnag.notify === 'function') {
              Bugsnag.notify(error, (event: unknown) => {
                const bugsnagEvent = event as {
                  addMetadata: (
                    section: string,
                    key: string,
                    value: unknown
                  ) => void;
                  context?: string;
                };
                bugsnagEvent.addMetadata('errorBoundary', 'type', 'global');
                bugsnagEvent.addMetadata(
                  'errorBoundary',
                  'digest',
                  error.digest
                );
                bugsnagEvent.addMetadata(
                  'errorBoundary',
                  'timestamp',
                  new Date().toISOString()
                );
                bugsnagEvent.context = 'Global Error Boundary';
              });
            }
          })
          .catch((bugsnagError) => {
            console.warn('Bugsnag not available:', bugsnagError);
          });
      }
    } else {
      // In development, just log to console
      console.error('Global error caught:', error);
    }
  }, [error]);

  return (
    <html>
      <body>
        {/* `NextError` is the default Next.js error page component. Its type
        definition requires a `statusCode` prop. However, since the App Router
        does not expose status codes for errors, we simply pass 0 to render a
        generic error message. */}
        <NextError statusCode={0} />
      </body>
    </html>
  );
}
