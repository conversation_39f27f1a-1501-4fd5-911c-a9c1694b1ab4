# Error Monitoring Setup

This document describes the error monitoring configuration for the Sabi Chat application, which uses both Sentry and Bugsnag for comprehensive error tracking.

## Overview

The application implements a dual error monitoring strategy:
- **Sentry**: Primary error monitoring service
- **Bugsnag**: Secondary error monitoring service for redundancy

Both services are configured to:
- Only operate in production environment
- Avoid noise in development
- Work harmoniously without conflicts
- Provide comprehensive error context and metadata

## Environment Variables

Add the following environment variables to your `.env.local` file:

```bash
# Error Monitoring
BUGSNAG_API_KEY=your_bugsnag_api_key
NEXT_PUBLIC_BUGSNAG_API_KEY=your_bugsnag_api_key

# Optional: App version for release tracking
APP_VERSION=1.0.0
NEXT_PUBLIC_APP_VERSION=1.0.0
```

## Configuration Files

### Sentry Configuration
- `sentry.client.config.ts` - Client-side Sentry configuration
- `sentry.server.config.ts` - Server-side Sentry configuration
- `sentry.edge.config.ts` - Edge runtime Sentry configuration

### Bugsnag Configuration
- `bugsnag.client.config.ts` - Client-side Bugsnag configuration
- `bugsnag.server.config.ts` - Server-side Bugsnag configuration
- `bugsnag.edge.config.ts` - Edge runtime Bugsnag configuration

## Key Features

### Environment-Based Activation
- **Production**: Both Sentry and Bugsnag are active
- **Development**: Only console logging, no external reporting

### Error Reporting Strategy
1. **AppError instances**: Automatically reported via constructor
2. **Unhandled errors**: Captured via `handleError()` utility
3. **React errors**: Captured via Error Boundaries
4. **Global errors**: Captured via Global Error Boundary

### Error Context Enhancement
Both services receive rich context including:
- Error codes and types
- User information (when available)
- Request context
- Server/client environment details
- Custom metadata and tags

## Usage Examples

### Custom Error Reporting
```typescript
import { AppError, handleError } from '@/lib/error';

// Using AppError (automatically reported)
throw new AppError('Something went wrong', 'CUSTOM_ERROR', 500);

// Handling unknown errors
try {
  // risky operation
} catch (error) {
  const appError = handleError(error);
  // Error is now reported to both services
}
```

### Manual Error Reporting
```typescript
import { reportError } from '@/lib/error';

reportError(new Error('Manual error'), {
  tags: { component: 'UserProfile' },
  extra: { userId: '123', action: 'update' },
  context: 'User profile update failed'
});
```

## Error Boundaries

### Global Error Boundary
Located at `src/app/global-error.tsx`, catches unhandled errors in the app router.

### Component Error Boundary
Located at `src/components/ErrorBoundary.tsx`, provides granular error catching for specific components.

## Monitoring Dashboard Access

### Sentry
- Organization: vertex-d7
- Project: sabi-chat
- Dashboard: [Sentry Dashboard URL]

### Bugsnag
- Project: Sabi Chat
- Dashboard: [Bugsnag Dashboard URL]

## Best Practices

1. **Use AppError for known error conditions**
2. **Include relevant context in error metadata**
3. **Test error reporting in staging environment**
4. **Monitor error rates and patterns regularly**
5. **Set up alerts for critical error thresholds**

## Troubleshooting

### Common Issues

1. **Errors not appearing in dashboards**
   - Verify environment variables are set correctly
   - Check that NODE_ENV is set to 'production'
   - Ensure API keys are valid

2. **"navigator is not defined" error in production**
   - This has been fixed by adding proper browser/server environment checks
   - Client-side configurations only run in browser context
   - Server-side configurations are properly isolated

3. **Duplicate error reports**
   - This is expected behavior - both services should receive the same errors
   - Use different services for different purposes (e.g., Sentry for development team, Bugsnag for operations)

4. **Missing context in error reports**
   - Verify metadata is being passed correctly
   - Check that user context is being set properly

### Debug Mode

To enable debug logging for error monitoring services, set:
```bash
# In development only
SENTRY_DEBUG=true
BUGSNAG_DEBUG=true
```

## Performance Considerations

- Error reporting is asynchronous and non-blocking
- Failed error reports are logged to console but don't affect application functionality
- Both services use efficient batching and sampling strategies
- Network failures in error reporting are handled gracefully

## Security

- User IP collection is disabled for privacy
- Sensitive data is filtered from error reports
- API keys are properly scoped with minimal required permissions
- Error reports include only necessary debugging information
