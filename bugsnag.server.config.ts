// This file configures the initialization of Bugsnag on the server.
// The config you add here will be used whenever the server handles a request.
// https://docs.bugsnag.com/platforms/javascript/

import Bugsnag from '@bugsnag/node';

// Only initialize Bugsnag in production environment
if (process.env.NODE_ENV === 'production' && process.env.BUGSNAG_API_KEY) {
  Bugsnag.start({
    apiKey: process.env.BUGSNAG_API_KEY,

    // Configure which release stages to report errors for
    enabledReleaseStages: ['production'],
    releaseStage: process.env.NODE_ENV,

    // App version for tracking releases
    appVersion: process.env.APP_VERSION || '1.0.0',

    // Configure which errors to capture
    autoDetectErrors: true,
    autoTrackSessions: true,

    // Configure breadcrumbs (server-appropriate types only)
    enabledBreadcrumbTypes: [
      'request',
      'process',
      'log',
      'error',
      'manual',
    ],

    // Configure metadata
    metadata: {
      app: {
        name: '<PERSON><PERSON><PERSON>',
        type: 'server',
      },
      server: {
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
      },
    },

    // Configure error filtering and enhancement
    onError: (event) => {
      // Add server context
      event.addMetadata('server', {
        timestamp: new Date().toISOString(),
        nodeEnv: process.env.NODE_ENV,
        memoryUsage: process.memoryUsage(),
        uptime: process.uptime(),
      });

      // Add request context if available
      if (event.request) {
        event.addMetadata('request', {
          method: event.request.method,
          url: event.request.url,
          headers: event.request.headers,
          timestamp: new Date().toISOString(),
        });
      }

      return true;
    },
  });
}

export default Bugsnag;
