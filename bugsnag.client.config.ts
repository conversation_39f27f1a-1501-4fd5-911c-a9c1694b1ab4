// This file configures the initialization of Bugsnag on the client.
// The config you add here will be used whenever a user loads a page in their browser.
// https://docs.bugsnag.com/platforms/javascript/

// Guard against server-side execution
if (typeof window !== 'undefined') {
  // Only import and initialize Bugsnag in browser context
  // eslint-disable-next-line @typescript-eslint/no-require-imports
  const Bugsnag = require('@bugsnag/js');

  // Only initialize Bugsnag in production environment and in browser context
  if (
    process.env.NODE_ENV === 'production' &&
    process.env.NEXT_PUBLIC_BUGSNAG_API_KEY
  ) {
    Bugsnag.start({
      apiKey: process.env.NEXT_PUBLIC_BUGSNAG_API_KEY,

      // Configure which release stages to report errors for
      enabledReleaseStages: ['production'],
      releaseStage: process.env.NODE_ENV,

      // App version for tracking releases
      appVersion: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',

      // Configure user context
      collectUserIp: false, // Privacy-friendly setting

      // Configure which errors to capture
      autoDetectErrors: true,
      autoTrackSessions: true,

      // Configure breadcrumbs
      enabledBreadcrumbTypes: [
        'navigation',
        'request',
        'process',
        'log',
        'user',
        'state',
        'error',
        'manual',
      ],

      // Configure metadata
      metadata: {
        app: {
          name: 'Sabi Chat',
          type: 'web-client',
        },
      },

      // Configure error filtering
      onError: (event: {
        addMetadata: (section: string, key: string, value: unknown) => void;
        setUser: (id: string, email?: string, name?: string) => void;
        context?: string;
      }) => {
        // Filter out development-related errors
        if (
          event.context?.includes('localhost') ||
          event.context?.includes('127.0.0.1')
        ) {
          return false;
        }

        // Add user context if available (with safety checks)
        try {
          const userId = localStorage.getItem('userId');
          if (userId) {
            event.setUser(userId);
          }
        } catch (error) {
          // localStorage might not be available
          console.warn('Could not access localStorage:', error);
        }

        // Add custom metadata (with safety checks)
        try {
          event.addMetadata(
            'client',
            'userAgent',
            typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown'
          );
          event.addMetadata(
            'client',
            'url',
            typeof window !== 'undefined' ? window.location.href : 'unknown'
          );
          event.addMetadata('client', 'timestamp', new Date().toISOString());
        } catch (error) {
          console.warn('Could not add client metadata:', error);
        }

        return true;
      },
    });
  }
}

// Export a default to satisfy module requirements
export default null;
