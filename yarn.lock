# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@adobe/css-tools@^4.4.0":
  version "4.4.2"
  resolved "https://registry.npmjs.org/@adobe/css-tools/-/css-tools-4.4.2.tgz"
  integrity sha512-baYZExFpsdkBNuvGKTKWCwKH57HRZLVtycZS05WTQNVOiXVSeAki3nU35zlRbToeMW8aHlJfyS+1C4BOv27q0A==

"@ai-sdk/provider-utils@2.2.7":
  version "2.2.7"
  resolved "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-2.2.7.tgz"
  integrity sha512-kM0xS3GWg3aMChh9zfeM+80vEZfXzR3JEUBdycZLtbRZ2TRT8xOj3WodGHPb06sUK5yD7pAXC/P7ctsi2fvUGQ==
  dependencies:
    "@ai-sdk/provider" "1.1.3"
    nanoid "^3.3.8"
    secure-json-parse "^2.7.0"

"@ai-sdk/provider@1.1.3":
  version "1.1.3"
  resolved "https://registry.npmjs.org/@ai-sdk/provider/-/provider-1.1.3.tgz"
  integrity sha512-qZMxYJ0qqX/RfnuIaab+zp8UAeJn/ygXXAffR5I4N0n1IrvA6qBsjc8hXLmBiMV2zoXlifkacF7sEFnYnjBcqg==
  dependencies:
    json-schema "^0.4.0"

"@ai-sdk/react@1.2.11":
  version "1.2.11"
  resolved "https://registry.npmjs.org/@ai-sdk/react/-/react-1.2.11.tgz"
  integrity sha512-+kPqLkJ3TWP6czaJPV+vzAKSUcKQ1598BUrcLHt56sH99+LhmIIW3ylZp0OfC3O6TR3eO1Lt0Yzw4R0mK6g9Gw==
  dependencies:
    "@ai-sdk/provider-utils" "2.2.7"
    "@ai-sdk/ui-utils" "1.2.10"
    swr "^2.2.5"
    throttleit "2.1.0"

"@ai-sdk/ui-utils@1.2.10":
  version "1.2.10"
  resolved "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.2.10.tgz"
  integrity sha512-GUj+LBoAlRQF1dL/M49jtufGqtLOMApxTpCmVjoRpIPt/dFALVL9RfqfvxwztyIwbK+IxGzcYjSGRsrWrj+86g==
  dependencies:
    "@ai-sdk/provider" "1.1.3"
    "@ai-sdk/provider-utils" "2.2.7"
    zod-to-json-schema "^3.24.1"

"@alloc/quick-lru@^5.2.0":
  version "5.2.0"
  resolved "https://registry.npmjs.org/@alloc/quick-lru/-/quick-lru-5.2.0.tgz"
  integrity sha512-UrcABB+4bUrFABwbluTIBErXwvbsU/V7TZWfmbgJfbkwiBuziS9gxdODUyuiecfdGQ85jglMW6juS3+z5TsKLw==

"@ampproject/remapping@^2.2.0":
  version "2.3.0"
  resolved "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.3.0.tgz"
  integrity sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.24"

"@anthropic-ai/sdk@0.39.0":
  version "0.39.0"
  resolved "https://registry.npmjs.org/@anthropic-ai/sdk/-/sdk-0.39.0.tgz"
  integrity sha512-eMyDIPRZbt1CCLErRCi3exlAvNkBtRe+kW5vvJyef93PmNr/clstYgHhtvmkxN82nlKgzyGPCyGxrm0JQ1ZIdg==
  dependencies:
    "@types/node" "^18.11.18"
    "@types/node-fetch" "^2.6.4"
    abort-controller "^3.0.0"
    agentkeepalive "^4.2.1"
    form-data-encoder "1.7.2"
    formdata-node "^4.3.2"
    node-fetch "^2.6.7"

"@anthropic-ai/tokenizer@^0.0.4":
  version "0.0.4"
  resolved "https://registry.npmjs.org/@anthropic-ai/tokenizer/-/tokenizer-0.0.4.tgz"
  integrity sha512-EHRKbxlxlc8W4KCBEseByJ7YwyYCmgu9OyN59H9+IYIGPoKv8tXyQXinkeGDI+cI8Tiuz9wk2jZb/kK7AyvL7g==
  dependencies:
    "@types/node" "^18.11.18"
    tiktoken "^1.0.10"

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.10.4", "@babel/code-frame@^7.12.13", "@babel/code-frame@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.27.1.tgz"
  integrity sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg==
  dependencies:
    "@babel/helper-validator-identifier" "^7.27.1"
    js-tokens "^4.0.0"
    picocolors "^1.1.1"

"@babel/compat-data@^7.22.6", "@babel/compat-data@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.27.1.tgz"
  integrity sha512-Q+E+rd/yBzNQhXkG+zQnF58e4zoZfBedaxwzPmicKsiK3nt8iJYrSrDbjwFFDGC4f+rPafqRaPH6TsDoSvMf7A==

"@babel/core@^7.11.6", "@babel/core@^7.12.3", "@babel/core@^7.18.5", "@babel/core@^7.23.9", "@babel/core@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/core/-/core-7.27.1.tgz"
  integrity sha512-IaaGWsQqfsQWVLqMn9OB92MNN7zukfVA4s7KKAI0KfrrDsZ0yhi5uV4baBuLuN7n3vsZpwP8asPPcVwApxvjBQ==
  dependencies:
    "@ampproject/remapping" "^2.2.0"
    "@babel/code-frame" "^7.27.1"
    "@babel/generator" "^7.27.1"
    "@babel/helper-compilation-targets" "^7.27.1"
    "@babel/helper-module-transforms" "^7.27.1"
    "@babel/helpers" "^7.27.1"
    "@babel/parser" "^7.27.1"
    "@babel/template" "^7.27.1"
    "@babel/traverse" "^7.27.1"
    "@babel/types" "^7.27.1"
    convert-source-map "^2.0.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.2"
    json5 "^2.2.3"
    semver "^6.3.1"

"@babel/generator@^7.27.1", "@babel/generator@^7.7.2":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/generator/-/generator-7.27.1.tgz"
  integrity sha512-UnJfnIpc/+JO0/+KRVQNGU+y5taA5vCbwN8+azkX6beii/ZF+enZJSOKo11ZSzGJjlNfJHfQtmQT8H+9TXPG2w==
  dependencies:
    "@babel/parser" "^7.27.1"
    "@babel/types" "^7.27.1"
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.25"
    jsesc "^3.0.2"

"@babel/helper-annotate-as-pure@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.27.1.tgz"
  integrity sha512-WnuuDILl9oOBbKnb4L+DyODx7iC47XfzmNCpTttFsSp6hTG7XZxu60+4IO+2/hPfcGOoKbFiwoI/+zwARbNQow==
  dependencies:
    "@babel/types" "^7.27.1"

"@babel/helper-compilation-targets@^7.22.6", "@babel/helper-compilation-targets@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.1.tgz"
  integrity sha512-2YaDd/Rd9E598B5+WIc8wJPmWETiiJXFYVE60oX8FDohv7rAUU3CQj+A1MgeEmcsk2+dQuEjIe/GDvig0SqL4g==
  dependencies:
    "@babel/compat-data" "^7.27.1"
    "@babel/helper-validator-option" "^7.27.1"
    browserslist "^4.24.0"
    lru-cache "^5.1.1"
    semver "^6.3.1"

"@babel/helper-create-class-features-plugin@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.27.1.tgz"
  integrity sha512-QwGAmuvM17btKU5VqXfb+Giw4JcN0hjuufz3DYnpeVDvZLAObloM77bhMXiqry3Iio+Ai4phVRDwl6WU10+r5A==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.27.1"
    "@babel/helper-member-expression-to-functions" "^7.27.1"
    "@babel/helper-optimise-call-expression" "^7.27.1"
    "@babel/helper-replace-supers" "^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.27.1"
    "@babel/traverse" "^7.27.1"
    semver "^6.3.1"

"@babel/helper-create-regexp-features-plugin@^7.18.6", "@babel/helper-create-regexp-features-plugin@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.27.1.tgz"
  integrity sha512-uVDC72XVf8UbrH5qQTc18Agb8emwjTiZrQE11Nv3CuBEZmVvTwwE9CBUEvHku06gQCAyYf8Nv6ja1IN+6LMbxQ==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.27.1"
    regexpu-core "^6.2.0"
    semver "^6.3.1"

"@babel/helper-define-polyfill-provider@^0.6.3", "@babel/helper-define-polyfill-provider@^0.6.4":
  version "0.6.4"
  resolved "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.6.4.tgz"
  integrity sha512-jljfR1rGnXXNWnmQg2K3+bvhkxB51Rl32QRaOTuwwjviGrHzIbSc8+x9CpraDtbT7mfyjXObULP4w/adunNwAw==
  dependencies:
    "@babel/helper-compilation-targets" "^7.22.6"
    "@babel/helper-plugin-utils" "^7.22.5"
    debug "^4.1.1"
    lodash.debounce "^4.0.8"
    resolve "^1.14.2"

"@babel/helper-member-expression-to-functions@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.27.1.tgz"
  integrity sha512-E5chM8eWjTp/aNoVpcbfM7mLxu9XGLWYise2eBKGQomAk/Mb4XoxyqXTZbuTohbsl8EKqdlMhnDI2CCLfcs9wA==
  dependencies:
    "@babel/traverse" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/helper-module-imports@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz"
  integrity sha512-0gSFWUPNXNopqtIPQvlD5WgXYI5GY2kP2cCvoT8kczjbfcfuIljTbcWrulD1CIPIX2gt1wghbDy08yE1p+/r3w==
  dependencies:
    "@babel/traverse" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/helper-module-transforms@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.27.1.tgz"
  integrity sha512-9yHn519/8KvTU5BjTVEEeIM3w9/2yXNKoD82JifINImhpKkARMJKPP59kLo+BafpdN5zgNeIcS4jsGDmd3l58g==
  dependencies:
    "@babel/helper-module-imports" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"
    "@babel/traverse" "^7.27.1"

"@babel/helper-optimise-call-expression@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.27.1.tgz"
  integrity sha512-URMGH08NzYFhubNSGJrpUEphGKQwMQYBySzat5cAByY1/YgIRkULnIy3tAMeszlL/so2HbeilYloUmSpd7GdVw==
  dependencies:
    "@babel/types" "^7.27.1"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.10.4", "@babel/helper-plugin-utils@^7.12.13", "@babel/helper-plugin-utils@^7.14.5", "@babel/helper-plugin-utils@^7.18.6", "@babel/helper-plugin-utils@^7.22.5", "@babel/helper-plugin-utils@^7.27.1", "@babel/helper-plugin-utils@^7.8.0":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz"
  integrity sha512-1gn1Up5YXka3YYAHGKpbideQ5Yjf1tDa9qYcgysz+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw==

"@babel/helper-remap-async-to-generator@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.27.1.tgz"
  integrity sha512-7fiA521aVw8lSPeI4ZOD3vRFkoqkJcS+z4hFo82bFSH/2tNd6eJ5qCVMS5OzDmZh/kaHQeBaeyxK6wljcPtveA==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.27.1"
    "@babel/helper-wrap-function" "^7.27.1"
    "@babel/traverse" "^7.27.1"

"@babel/helper-replace-supers@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.27.1.tgz"
  integrity sha512-7EHz6qDZc8RYS5ElPoShMheWvEgERonFCs7IAonWLLUTXW59DP14bCZt89/GKyreYn8g3S83m21FelHKbeDCKA==
  dependencies:
    "@babel/helper-member-expression-to-functions" "^7.27.1"
    "@babel/helper-optimise-call-expression" "^7.27.1"
    "@babel/traverse" "^7.27.1"

"@babel/helper-skip-transparent-expression-wrappers@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.27.1.tgz"
  integrity sha512-Tub4ZKEXqbPjXgWLl2+3JpQAYBJ8+ikpQ2Ocj/q/r0LwE3UhENh7EUabyHjz2kCEsrRY83ew2DQdHluuiDQFzg==
  dependencies:
    "@babel/traverse" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/helper-string-parser@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz"
  integrity sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA==

"@babel/helper-validator-identifier@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz"
  integrity sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow==

"@babel/helper-validator-option@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz"
  integrity sha512-YvjJow9FxbhFFKDSuFnVCe2WxXk1zWc22fFePVNEaWJEu8IrZVlda6N0uHwzZrUM1il7NC9Mlp4MaJYbYd9JSg==

"@babel/helper-wrap-function@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.27.1.tgz"
  integrity sha512-NFJK2sHUvrjo8wAU/nQTWU890/zB2jj0qBcCbZbbf+005cAsv6tMjXz31fBign6M5ov1o0Bllu+9nbqkfsjjJQ==
  dependencies:
    "@babel/template" "^7.27.1"
    "@babel/traverse" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/helpers@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/helpers/-/helpers-7.27.1.tgz"
  integrity sha512-FCvFTm0sWV8Fxhpp2McP5/W53GPllQ9QeQ7SiqGWjMf/LVG07lFa5+pgK05IRhVwtvafT22KF+ZSnM9I545CvQ==
  dependencies:
    "@babel/template" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/parser@^7.1.0", "@babel/parser@^7.14.7", "@babel/parser@^7.20.7", "@babel/parser@^7.23.9", "@babel/parser@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/parser/-/parser-7.27.1.tgz"
  integrity sha512-I0dZ3ZpCrJ1c04OqlNsQcKiZlsrXf/kkE4FXzID9rIOYICsAbA8mMDzhW/luRNAHdCNt7os/u8wenklZDlUVUQ==
  dependencies:
    "@babel/types" "^7.27.1"

"@babel/plugin-bugfix-firefox-class-in-computed-class-key@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-bugfix-firefox-class-in-computed-class-key/-/plugin-bugfix-firefox-class-in-computed-class-key-7.27.1.tgz"
  integrity sha512-QPG3C9cCVRQLxAVwmefEmwdTanECuUBMQZ/ym5kiw3XKCGA7qkuQLcjWWHcrD/GKbn/WmJwaezfuuAOcyKlRPA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/traverse" "^7.27.1"

"@babel/plugin-bugfix-safari-class-field-initializer-scope@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-bugfix-safari-class-field-initializer-scope/-/plugin-bugfix-safari-class-field-initializer-scope-7.27.1.tgz"
  integrity sha512-qNeq3bCKnGgLkEXUuFry6dPlGfCdQNZbn7yUAPCInwAJHMU7THJfrBSozkcWq5sNM6RcF3S8XyQL2A52KNR9IA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.27.1.tgz"
  integrity sha512-g4L7OYun04N1WyqMNjldFwlfPCLVkgB54A/YCXICZYBsvJJE3kByKv9c9+R/nAfmIfjl2rKYLNyMHboYbZaWaA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/-/plugin-bugfix-v8-spread-parameters-in-optional-chaining-7.27.1.tgz"
  integrity sha512-oO02gcONcD5O1iTLi/6frMJBIwWEHceWGSGqrpCmEL8nogiS6J9PBlE48CaK20/Jx1LuRml9aDftLgdjXT8+Cw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.27.1"
    "@babel/plugin-transform-optional-chaining" "^7.27.1"

"@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly/-/plugin-bugfix-v8-static-class-fields-redefine-readonly-7.27.1.tgz"
  integrity sha512-6BpaYGDavZqkI6yT+KSPdpZFfpnd68UKXbcjI9pJ13pvHhPrCKWOOLp+ysvMeA+DxnhuPpgIaRpxRxo5A9t5jw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/traverse" "^7.27.1"

"@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2":
  version "7.21.0-placeholder-for-preset-env.2"
  resolved "https://registry.npmjs.org/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.21.0-placeholder-for-preset-env.2.tgz"
  integrity sha512-SOSkfJDddaM7mak6cPEpswyTRnuRltl429hMraQEglW+OkovnCzsiszTmsrlY//qLFjCpQDFRvjdm2wA5pPm9w==

"@babel/plugin-syntax-async-generators@^7.8.4":
  version "7.8.4"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz"
  integrity sha512-tycmZxkGfZaxhMRbXlPXuVFpdWlXpir2W4AMhSJgRKzk/eDlIXOhb2LHWoLpDF7TEHylV5zNhykX6KAgHJmTNw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-bigint@^7.8.3":
  version "7.8.3"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.8.3.tgz"
  integrity sha512-wnTnFlG+YxQm3vDxpGE57Pj0srRU4sHE/mDkt1qv2YJJSeUAec2ma4WLUnUPeKjyrfntVwe/N6dCXpU+zL3Npg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-class-properties@^7.12.13":
  version "7.12.13"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz"
  integrity sha512-fm4idjKla0YahUNgFNLCB0qySdsoPiZP3iQE3rky0mBUtMZ23yDJ9SJdg6dXTSDnulOVqiF3Hgr9nbXvXTQZYA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-syntax-class-static-block@^7.14.5":
  version "7.14.5"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.14.5.tgz"
  integrity sha512-b+YyPmr6ldyNnM6sqYeMWE+bgJcJpO6yS4QD7ymxgH34GBPNDM/THBh8iunyvKIZztiwLH4CJZ0RxTk9emgpjw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-import-assertions@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-7.27.1.tgz"
  integrity sha512-UT/Jrhw57xg4ILHLFnzFpPDlMbcdEicaAtjPQpbj9wa8T4r5KVWCimHcL/460g8Ht0DMxDyjsLgiWSkVjnwPFg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-syntax-import-attributes@^7.24.7", "@babel/plugin-syntax-import-attributes@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-7.27.1.tgz"
  integrity sha512-oFT0FrKHgF53f4vOsZGi2Hh3I35PfSmVs4IBFLFj4dnafP+hIWDLg3VyKmUHfLoLHlyxY4C7DGtmHuJgn+IGww==
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-syntax-import-meta@^7.10.4":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.10.4.tgz"
  integrity sha512-Yqfm+XDx0+Prh3VSeEQCPU81yC+JWZ2pDPFSS4ZdpfZhp4MkFMaDC1UqseovEKwSUpnIL7+vK+Clp7bfh0iD7g==
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-json-strings@^7.8.3":
  version "7.8.3"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz"
  integrity sha512-lY6kdGpWHvjoe2vk4WrAapEuBR69EMxZl+RoGRhrFGNYVK8mOPAW8VfbT/ZgrFbXlDNiiaxQnAtgVCZ6jv30EA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-jsx@^7.27.1", "@babel/plugin-syntax-jsx@^7.7.2":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.27.1.tgz"
  integrity sha512-y8YTNIeKoyhGd9O0Jiyzyyqk8gdjnumGTQPsz0xOZOQ2RmkVJeZ1vmmfIvFEKqucBG6axJGBZDE/7iI5suUI/w==
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-syntax-logical-assignment-operators@^7.10.4":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz"
  integrity sha512-d8waShlpFDinQ5MtvGU9xDAOzKH47+FFoney2baFIoMr952hKOLp1HR7VszoZvOsV/4+RRszNY7D17ba0te0ig==
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3":
  version "7.8.3"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz"
  integrity sha512-aSff4zPII1u2QD7y+F8oDsz19ew4IGEJg9SVW+bqwpwtfFleiQDMdzA/R+UlWDzfnHFCxxleFT0PMIrR36XLNQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-numeric-separator@^7.10.4":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz"
  integrity sha512-9H6YdfkcK/uOnY/K7/aA2xpzaAgkQn37yzWUMRK7OaPOqOpGS1+n0H5hxT9AUw9EsSjPW8SVyMJwYRtWs3X3ug==
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-object-rest-spread@^7.8.3":
  version "7.8.3"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz"
  integrity sha512-XoqMijGZb9y3y2XskN+P1wUGiVwWZ5JmoDRwx5+3GmEplNyVM2s2Dg8ILFQm8rWM48orGy5YpI5Bl8U1y7ydlA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-catch-binding@^7.8.3":
  version "7.8.3"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz"
  integrity sha512-6VPD0Pc1lpTqw0aKoeRTMiB+kWhAoT24PA+ksWSBrFtl5SIRVpZlwN3NNPQjehA2E/91FV3RjLWoVTglWcSV3Q==
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-chaining@^7.8.3":
  version "7.8.3"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz"
  integrity sha512-KoK9ErH1MBlCPxV0VANkXW2/dw4vlbGDrFgz8bmUsBGYkFRcbRwMh6cIJubdPrkxRwuGdtCk0v/wPTKbQgBjkg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-private-property-in-object@^7.14.5":
  version "7.14.5"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-private-property-in-object/-/plugin-syntax-private-property-in-object-7.14.5.tgz"
  integrity sha512-0wVnp9dxJ72ZUJDV27ZfbSj6iHLoytYZmh3rFcxNnvsJF3ktkzLDZPy/mA17HGsaQT3/DQsWYX1f1QGWkCoVUg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-top-level-await@^7.14.5":
  version "7.14.5"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz"
  integrity sha512-hx++upLv5U1rgYfwe1xBQUhRmU41NEvpUvrp8jkrSCdvGSnM5/qdRMtylJ6PG5OFkBaHkbTAKTnd3/YyESRHFw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-typescript@^7.27.1", "@babel/plugin-syntax-typescript@^7.7.2":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.27.1.tgz"
  integrity sha512-xfYCBMxveHrRMnAWl1ZlPXOZjzkN82THFvLhQhFXFt81Z5HnN+EtUkZhv/zcKpmT3fzmWZB0ywiBrbC3vogbwQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-syntax-unicode-sets-regex@^7.18.6":
  version "7.18.6"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-unicode-sets-regex/-/plugin-syntax-unicode-sets-regex-7.18.6.tgz"
  integrity sha512-727YkEAPwSIQTv5im8QHz3upqp92JTWhidIC81Tdx4VJYIte/VndKf1qKrfnnhPLiPghStWfvC/iFaMCQu7Nqg==
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-arrow-functions@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.27.1.tgz"
  integrity sha512-8Z4TGic6xW70FKThA5HYEKKyBpOOsucTOD1DjU3fZxDg+K3zBJcXMFnt/4yQiZnf5+MiOMSXQ9PaEK/Ilh1DeA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-async-generator-functions@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-7.27.1.tgz"
  integrity sha512-eST9RrwlpaoJBDHShc+DS2SG4ATTi2MYNb4OxYkf3n+7eb49LWpnS+HSpVfW4x927qQwgk8A2hGNVaajAEw0EA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-remap-async-to-generator" "^7.27.1"
    "@babel/traverse" "^7.27.1"

"@babel/plugin-transform-async-to-generator@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.27.1.tgz"
  integrity sha512-NREkZsZVJS4xmTr8qzE5y8AfIPqsdQfRuUiLRTEzb7Qii8iFWCyDKaUV2c0rCuh4ljDZ98ALHP/PetiBV2nddA==
  dependencies:
    "@babel/helper-module-imports" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-remap-async-to-generator" "^7.27.1"

"@babel/plugin-transform-block-scoped-functions@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-block-scoped-functions/-/plugin-transform-block-scoped-functions-7.27.1.tgz"
  integrity sha512-cnqkuOtZLapWYZUYM5rVIdv1nXYuFVIltZ6ZJ7nIj585QsjKM5dhL2Fu/lICXZ1OyIAFc7Qy+bvDAtTXqGrlhg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-block-scoping@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.27.1.tgz"
  integrity sha512-QEcFlMl9nGTgh1rn2nIeU5bkfb9BAjaQcWbiP4LvKxUot52ABcTkpcyJ7f2Q2U2RuQ84BNLgts3jRme2dTx6Fw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-class-properties@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-class-properties/-/plugin-transform-class-properties-7.27.1.tgz"
  integrity sha512-D0VcalChDMtuRvJIu3U/fwWjf8ZMykz5iZsg77Nuj821vCKI3zCyRLwRdWbsuJ/uRwZhZ002QtCqIkwC/ZkvbA==
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-class-static-block@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-class-static-block/-/plugin-transform-class-static-block-7.27.1.tgz"
  integrity sha512-s734HmYU78MVzZ++joYM+NkJusItbdRcbm+AGRgJCt3iA+yux0QpD9cBVdz3tKyrjVYWRl7j0mHSmv4lhV0aoA==
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-classes@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.27.1.tgz"
  integrity sha512-7iLhfFAubmpeJe/Wo2TVuDrykh/zlWXLzPNdL0Jqn/Xu8R3QQ8h9ff8FQoISZOsw74/HFqFI7NX63HN7QFIHKA==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.27.1"
    "@babel/helper-compilation-targets" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-replace-supers" "^7.27.1"
    "@babel/traverse" "^7.27.1"
    globals "^11.1.0"

"@babel/plugin-transform-computed-properties@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-computed-properties/-/plugin-transform-computed-properties-7.27.1.tgz"
  integrity sha512-lj9PGWvMTVksbWiDT2tW68zGS/cyo4AkZ/QTp0sQT0mjPopCmrSkzxeXkznjqBxzDI6TclZhOJbBmbBLjuOZUw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/template" "^7.27.1"

"@babel/plugin-transform-destructuring@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.27.1.tgz"
  integrity sha512-ttDCqhfvpE9emVkXbPD8vyxxh4TWYACVybGkDj+oReOGwnp066ITEivDlLwe0b1R0+evJ13IXQuLNB5w1fhC5Q==
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-dotall-regex@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.27.1.tgz"
  integrity sha512-gEbkDVGRvjj7+T1ivxrfgygpT7GUd4vmODtYpbs0gZATdkX8/iSnOtZSxiZnsgm1YjTgjI6VKBGSJJevkrclzw==
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-duplicate-keys@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.27.1.tgz"
  integrity sha512-MTyJk98sHvSs+cvZ4nOauwTTG1JeonDjSGvGGUNHreGQns+Mpt6WX/dVzWBHgg+dYZhkC4X+zTDfkTU+Vy9y7Q==
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-duplicate-named-capturing-groups-regex@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-duplicate-named-capturing-groups-regex/-/plugin-transform-duplicate-named-capturing-groups-regex-7.27.1.tgz"
  integrity sha512-hkGcueTEzuhB30B3eJCbCYeCaaEQOmQR0AdvzpD4LoN0GXMWzzGSuRrxR2xTnCrvNbVwK9N6/jQ92GSLfiZWoQ==
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-dynamic-import@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-dynamic-import/-/plugin-transform-dynamic-import-7.27.1.tgz"
  integrity sha512-MHzkWQcEmjzzVW9j2q8LGjwGWpG2mjwaaB0BNQwst3FIjqsg8Ct/mIZlvSPJvfi9y2AC8mi/ktxbFVL9pZ1I4A==
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-exponentiation-operator@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.27.1.tgz"
  integrity sha512-uspvXnhHvGKf2r4VVtBpeFnuDWsJLQ6MF6lGJLC89jBR1uoVeqM416AZtTuhTezOfgHicpJQmoD5YUakO/YmXQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-export-namespace-from@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-7.27.1.tgz"
  integrity sha512-tQvHWSZ3/jH2xuq/vZDy0jNn+ZdXJeM8gHvX4lnJmsc3+50yPlWdZXIc5ay+umX+2/tJIqHqiEqcJvxlmIvRvQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-for-of@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.27.1.tgz"
  integrity sha512-BfbWFFEJFQzLCQ5N8VocnCtA8J1CLkNTe2Ms2wocj75dd6VpiqS5Z5quTYcUoo4Yq+DN0rtikODccuv7RU81sw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.27.1"

"@babel/plugin-transform-function-name@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.27.1.tgz"
  integrity sha512-1bQeydJF9Nr1eBCMMbC+hdwmRlsv5XYOMu03YSWFwNs0HsAmtSxxF1fyuYPqemVldVyFmlCU7w8UE14LupUSZQ==
  dependencies:
    "@babel/helper-compilation-targets" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/traverse" "^7.27.1"

"@babel/plugin-transform-json-strings@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-json-strings/-/plugin-transform-json-strings-7.27.1.tgz"
  integrity sha512-6WVLVJiTjqcQauBhn1LkICsR2H+zm62I3h9faTDKt1qP4jn2o72tSvqMwtGFKGTpojce0gJs+76eZ2uCHRZh0Q==
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-literals@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.27.1.tgz"
  integrity sha512-0HCFSepIpLTkLcsi86GG3mTUzxV5jpmbv97hTETW3yzrAij8aqlD36toB1D0daVFJM8NK6GvKO0gslVQmm+zZA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-logical-assignment-operators@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-7.27.1.tgz"
  integrity sha512-SJvDs5dXxiae4FbSL1aBJlG4wvl594N6YEVVn9e3JGulwioy6z3oPjx/sQBO3Y4NwUu5HNix6KJ3wBZoewcdbw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-member-expression-literals@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.27.1.tgz"
  integrity sha512-hqoBX4dcZ1I33jCSWcXrP+1Ku7kdqXf1oeah7ooKOIiAdKQ+uqftgCFNOSzA5AMS2XIHEYeGFg4cKRCdpxzVOQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-modules-amd@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.27.1.tgz"
  integrity sha512-iCsytMg/N9/oFq6n+gFTvUYDZQOMK5kEdeYxmxt91fcJGycfxVP9CnrxoliM0oumFERba2i8ZtwRUCMhvP1LnA==
  dependencies:
    "@babel/helper-module-transforms" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-modules-commonjs@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.27.1.tgz"
  integrity sha512-OJguuwlTYlN0gBZFRPqwOGNWssZjfIUdS7HMYtN8c1KmwpwHFBwTeFZrg9XZa+DFTitWOW5iTAG7tyCUPsCCyw==
  dependencies:
    "@babel/helper-module-transforms" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-modules-systemjs@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.27.1.tgz"
  integrity sha512-w5N1XzsRbc0PQStASMksmUeqECuzKuTJer7kFagK8AXgpCMkeDMO5S+aaFb7A51ZYDF7XI34qsTX+fkHiIm5yA==
  dependencies:
    "@babel/helper-module-transforms" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"
    "@babel/traverse" "^7.27.1"

"@babel/plugin-transform-modules-umd@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.27.1.tgz"
  integrity sha512-iQBE/xC5BV1OxJbp6WG7jq9IWiD+xxlZhLrdwpPkTX3ydmXdvoCpyfJN7acaIBZaOqTfr76pgzqBJflNbeRK+w==
  dependencies:
    "@babel/helper-module-transforms" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-named-capturing-groups-regex@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.27.1.tgz"
  integrity sha512-SstR5JYy8ddZvD6MhV0tM/j16Qds4mIpJTOd1Yu9J9pJjH93bxHECF7pgtc28XvkzTD6Pxcm/0Z73Hvk7kb3Ng==
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-new-target@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.27.1.tgz"
  integrity sha512-f6PiYeqXQ05lYq3TIfIDu/MtliKUbNwkGApPUvyo6+tc7uaR4cPjPe7DFPr15Uyycg2lZU6btZ575CuQoYh7MQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-nullish-coalescing-operator@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-7.27.1.tgz"
  integrity sha512-aGZh6xMo6q9vq1JGcw58lZ1Z0+i0xB2x0XaauNIUXd6O1xXc3RwoWEBlsTQrY4KQ9Jf0s5rgD6SiNkaUdJegTA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-numeric-separator@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-7.27.1.tgz"
  integrity sha512-fdPKAcujuvEChxDBJ5c+0BTaS6revLV7CJL08e4m3de8qJfNIuCc2nc7XJYOjBoTMJeqSmwXJ0ypE14RCjLwaw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-object-rest-spread@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-7.27.1.tgz"
  integrity sha512-/sSliVc9gHE20/7D5qsdGlq7RG5NCDTWsAhyqzGuq174EtWJoGzIu1BQ7G56eDsTcy1jseBZwv50olSdXOlGuA==
  dependencies:
    "@babel/helper-compilation-targets" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/plugin-transform-parameters" "^7.27.1"

"@babel/plugin-transform-object-super@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.27.1.tgz"
  integrity sha512-SFy8S9plRPbIcxlJ8A6mT/CxFdJx/c04JEctz4jf8YZaVS2px34j7NXRrlGlHkN/M2gnpL37ZpGRGVFLd3l8Ng==
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-replace-supers" "^7.27.1"

"@babel/plugin-transform-optional-catch-binding@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-optional-catch-binding/-/plugin-transform-optional-catch-binding-7.27.1.tgz"
  integrity sha512-txEAEKzYrHEX4xSZN4kJ+OfKXFVSWKB2ZxM9dpcE3wT7smwkNmXo5ORRlVzMVdJbD+Q8ILTgSD7959uj+3Dm3Q==
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-optional-chaining@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-7.27.1.tgz"
  integrity sha512-BQmKPPIuc8EkZgNKsv0X4bPmOoayeu4F1YCwx2/CfmDSXDbp7GnzlUH+/ul5VGfRg1AoFPsrIThlEBj2xb4CAg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.27.1"

"@babel/plugin-transform-parameters@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.27.1.tgz"
  integrity sha512-018KRk76HWKeZ5l4oTj2zPpSh+NbGdt0st5S6x0pga6HgrjBOJb24mMDHorFopOOd6YHkLgOZ+zaCjZGPO4aKg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-private-methods@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-private-methods/-/plugin-transform-private-methods-7.27.1.tgz"
  integrity sha512-10FVt+X55AjRAYI9BrdISN9/AQWHqldOeZDUoLyif1Kn05a56xVBXb8ZouL8pZ9jem8QpXaOt8TS7RHUIS+GPA==
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-private-property-in-object@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-7.27.1.tgz"
  integrity sha512-5J+IhqTi1XPa0DXF83jYOaARrX+41gOewWbkPyjMNRDqgOCqdffGh8L3f/Ek5utaEBZExjSAzcyjmV9SSAWObQ==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.27.1"
    "@babel/helper-create-class-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-property-literals@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.27.1.tgz"
  integrity sha512-oThy3BCuCha8kDZ8ZkgOg2exvPYUlprMukKQXI1r1pJ47NCvxfkEy8vK+r/hT9nF0Aa4H1WUPZZjHTFtAhGfmQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-react-display-name@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-react-display-name/-/plugin-transform-react-display-name-7.27.1.tgz"
  integrity sha512-p9+Vl3yuHPmkirRrg021XiP+EETmPMQTLr6Ayjj85RLNEbb3Eya/4VI0vAdzQG9SEAl2Lnt7fy5lZyMzjYoZQQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-react-jsx-development@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-development/-/plugin-transform-react-jsx-development-7.27.1.tgz"
  integrity sha512-ykDdF5yI4f1WrAolLqeF3hmYU12j9ntLQl/AOG1HAS21jxyg1Q0/J/tpREuYLfatGdGmXp/3yS0ZA76kOlVq9Q==
  dependencies:
    "@babel/plugin-transform-react-jsx" "^7.27.1"

"@babel/plugin-transform-react-jsx@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.27.1.tgz"
  integrity sha512-2KH4LWGSrJIkVf5tSiBFYuXDAoWRq2MMwgivCf+93dd0GQi8RXLjKA/0EvRnVV5G0hrHczsquXuD01L8s6dmBw==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.27.1"
    "@babel/helper-module-imports" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/plugin-syntax-jsx" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/plugin-transform-react-pure-annotations@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-7.27.1.tgz"
  integrity sha512-JfuinvDOsD9FVMTHpzA/pBLisxpv1aSf+OIV8lgH3MuWrks19R27e6a6DipIg4aX1Zm9Wpb04p8wljfKrVSnPA==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-regenerator@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.27.1.tgz"
  integrity sha512-B19lbbL7PMrKr52BNPjCqg1IyNUIjTcxKj8uX9zHO+PmWN93s19NDr/f69mIkEp2x9nmDJ08a7lgHaTTzvW7mw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-regexp-modifiers@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-regexp-modifiers/-/plugin-transform-regexp-modifiers-7.27.1.tgz"
  integrity sha512-TtEciroaiODtXvLZv4rmfMhkCv8jx3wgKpL68PuiPh2M4fvz5jhsA7697N1gMvkvr/JTF13DrFYyEbY9U7cVPA==
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-reserved-words@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.27.1.tgz"
  integrity sha512-V2ABPHIJX4kC7HegLkYoDpfg9PVmuWy/i6vUM5eGK22bx4YVFD3M5F0QQnWQoDs6AGsUWTVOopBiMFQgHaSkVw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-shorthand-properties@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.27.1.tgz"
  integrity sha512-N/wH1vcn4oYawbJ13Y/FxcQrWk63jhfNa7jef0ih7PHSIHX2LB7GWE1rkPrOnka9kwMxb6hMl19p7lidA+EHmQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-spread@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.27.1.tgz"
  integrity sha512-kpb3HUqaILBJcRFVhFUs6Trdd4mkrzcGXss+6/mxUd273PfbWqSDHRzMT2234gIg2QYfAjvXLSquP1xECSg09Q==
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.27.1"

"@babel/plugin-transform-sticky-regex@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.27.1.tgz"
  integrity sha512-lhInBO5bi/Kowe2/aLdBAawijx+q1pQzicSgnkB6dUPc1+RC8QmJHKf2OjvU+NZWitguJHEaEmbV6VWEouT58g==
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-template-literals@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-template-literals/-/plugin-transform-template-literals-7.27.1.tgz"
  integrity sha512-fBJKiV7F2DxZUkg5EtHKXQdbsbURW3DZKQUWphDum0uRP6eHGGa/He9mc0mypL680pb+e/lDIthRohlv8NCHkg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-typeof-symbol@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.27.1.tgz"
  integrity sha512-RiSILC+nRJM7FY5srIyc4/fGIwUhyDuuBSdWn4y6yT6gm652DpCHZjIipgn6B7MQ1ITOUnAKWixEUjQRIBIcLw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-typescript@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.27.1.tgz"
  integrity sha512-Q5sT5+O4QUebHdbwKedFBEwRLb02zJ7r4A5Gg2hUoLuU3FjdMcyqcywqUrLCaDsFCxzokf7u9kuy7qz51YUuAg==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.27.1"
    "@babel/helper-create-class-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.27.1"
    "@babel/plugin-syntax-typescript" "^7.27.1"

"@babel/plugin-transform-unicode-escapes@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.27.1.tgz"
  integrity sha512-Ysg4v6AmF26k9vpfFuTZg8HRfVWzsh1kVfowA23y9j/Gu6dOuahdUVhkLqpObp3JIv27MLSii6noRnuKN8H0Mg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-unicode-property-regex@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-unicode-property-regex/-/plugin-transform-unicode-property-regex-7.27.1.tgz"
  integrity sha512-uW20S39PnaTImxp39O5qFlHLS9LJEmANjMG7SxIhap8rCHqu0Ik+tLEPX5DKmHn6CsWQ7j3lix2tFOa5YtL12Q==
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-unicode-regex@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.27.1.tgz"
  integrity sha512-xvINq24TRojDuyt6JGtHmkVkrfVV3FPT16uytxImLeBZqW3/H52yN+kM1MGuyPkIQxrzKwPHs5U/MP3qKyzkGw==
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-unicode-sets-regex@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-unicode-sets-regex/-/plugin-transform-unicode-sets-regex-7.27.1.tgz"
  integrity sha512-EtkOujbc4cgvb0mlpQefi4NTPBzhSIevblFevACNLUspmrALgmEBdL/XfnyyITfd8fKBZrZys92zOWcik7j9Tw==
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/preset-env@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/preset-env/-/preset-env-7.27.1.tgz"
  integrity sha512-TZ5USxFpLgKDpdEt8YWBR7p6g+bZo6sHaXLqP2BY/U0acaoI8FTVflcYCr/v94twM1C5IWFdZ/hscq9WjUeLXA==
  dependencies:
    "@babel/compat-data" "^7.27.1"
    "@babel/helper-compilation-targets" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-validator-option" "^7.27.1"
    "@babel/plugin-bugfix-firefox-class-in-computed-class-key" "^7.27.1"
    "@babel/plugin-bugfix-safari-class-field-initializer-scope" "^7.27.1"
    "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression" "^7.27.1"
    "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining" "^7.27.1"
    "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly" "^7.27.1"
    "@babel/plugin-proposal-private-property-in-object" "7.21.0-placeholder-for-preset-env.2"
    "@babel/plugin-syntax-import-assertions" "^7.27.1"
    "@babel/plugin-syntax-import-attributes" "^7.27.1"
    "@babel/plugin-syntax-unicode-sets-regex" "^7.18.6"
    "@babel/plugin-transform-arrow-functions" "^7.27.1"
    "@babel/plugin-transform-async-generator-functions" "^7.27.1"
    "@babel/plugin-transform-async-to-generator" "^7.27.1"
    "@babel/plugin-transform-block-scoped-functions" "^7.27.1"
    "@babel/plugin-transform-block-scoping" "^7.27.1"
    "@babel/plugin-transform-class-properties" "^7.27.1"
    "@babel/plugin-transform-class-static-block" "^7.27.1"
    "@babel/plugin-transform-classes" "^7.27.1"
    "@babel/plugin-transform-computed-properties" "^7.27.1"
    "@babel/plugin-transform-destructuring" "^7.27.1"
    "@babel/plugin-transform-dotall-regex" "^7.27.1"
    "@babel/plugin-transform-duplicate-keys" "^7.27.1"
    "@babel/plugin-transform-duplicate-named-capturing-groups-regex" "^7.27.1"
    "@babel/plugin-transform-dynamic-import" "^7.27.1"
    "@babel/plugin-transform-exponentiation-operator" "^7.27.1"
    "@babel/plugin-transform-export-namespace-from" "^7.27.1"
    "@babel/plugin-transform-for-of" "^7.27.1"
    "@babel/plugin-transform-function-name" "^7.27.1"
    "@babel/plugin-transform-json-strings" "^7.27.1"
    "@babel/plugin-transform-literals" "^7.27.1"
    "@babel/plugin-transform-logical-assignment-operators" "^7.27.1"
    "@babel/plugin-transform-member-expression-literals" "^7.27.1"
    "@babel/plugin-transform-modules-amd" "^7.27.1"
    "@babel/plugin-transform-modules-commonjs" "^7.27.1"
    "@babel/plugin-transform-modules-systemjs" "^7.27.1"
    "@babel/plugin-transform-modules-umd" "^7.27.1"
    "@babel/plugin-transform-named-capturing-groups-regex" "^7.27.1"
    "@babel/plugin-transform-new-target" "^7.27.1"
    "@babel/plugin-transform-nullish-coalescing-operator" "^7.27.1"
    "@babel/plugin-transform-numeric-separator" "^7.27.1"
    "@babel/plugin-transform-object-rest-spread" "^7.27.1"
    "@babel/plugin-transform-object-super" "^7.27.1"
    "@babel/plugin-transform-optional-catch-binding" "^7.27.1"
    "@babel/plugin-transform-optional-chaining" "^7.27.1"
    "@babel/plugin-transform-parameters" "^7.27.1"
    "@babel/plugin-transform-private-methods" "^7.27.1"
    "@babel/plugin-transform-private-property-in-object" "^7.27.1"
    "@babel/plugin-transform-property-literals" "^7.27.1"
    "@babel/plugin-transform-regenerator" "^7.27.1"
    "@babel/plugin-transform-regexp-modifiers" "^7.27.1"
    "@babel/plugin-transform-reserved-words" "^7.27.1"
    "@babel/plugin-transform-shorthand-properties" "^7.27.1"
    "@babel/plugin-transform-spread" "^7.27.1"
    "@babel/plugin-transform-sticky-regex" "^7.27.1"
    "@babel/plugin-transform-template-literals" "^7.27.1"
    "@babel/plugin-transform-typeof-symbol" "^7.27.1"
    "@babel/plugin-transform-unicode-escapes" "^7.27.1"
    "@babel/plugin-transform-unicode-property-regex" "^7.27.1"
    "@babel/plugin-transform-unicode-regex" "^7.27.1"
    "@babel/plugin-transform-unicode-sets-regex" "^7.27.1"
    "@babel/preset-modules" "0.1.6-no-external-plugins"
    babel-plugin-polyfill-corejs2 "^0.4.10"
    babel-plugin-polyfill-corejs3 "^0.11.0"
    babel-plugin-polyfill-regenerator "^0.6.1"
    core-js-compat "^3.40.0"
    semver "^6.3.1"

"@babel/preset-modules@0.1.6-no-external-plugins":
  version "0.1.6-no-external-plugins"
  resolved "https://registry.npmjs.org/@babel/preset-modules/-/preset-modules-0.1.6-no-external-plugins.tgz"
  integrity sha512-HrcgcIESLm9aIR842yhJ5RWan/gebQUJ6E/E5+rf0y9o6oj7w0Br+sWuL6kEQ/o/AdfvR1Je9jG18/gnpwjEyA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/types" "^7.4.4"
    esutils "^2.0.2"

"@babel/preset-react@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.27.1.tgz"
  integrity sha512-oJHWh2gLhU9dW9HHr42q0cI0/iHHXTLGe39qvpAZZzagHy0MzYLCnCVV0symeRvzmjHyVU7mw2K06E6u/JwbhA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-validator-option" "^7.27.1"
    "@babel/plugin-transform-react-display-name" "^7.27.1"
    "@babel/plugin-transform-react-jsx" "^7.27.1"
    "@babel/plugin-transform-react-jsx-development" "^7.27.1"
    "@babel/plugin-transform-react-pure-annotations" "^7.27.1"

"@babel/preset-typescript@^7.24.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.27.1.tgz"
  integrity sha512-l7WfQfX0WK4M0v2RudjuQK4u99BS6yLHYEmdtVPP7lKV013zr9DygFuWNlnbvQ9LR+LS0Egz/XAvGx5U9MX0fQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-validator-option" "^7.27.1"
    "@babel/plugin-syntax-jsx" "^7.27.1"
    "@babel/plugin-transform-modules-commonjs" "^7.27.1"
    "@babel/plugin-transform-typescript" "^7.27.1"

"@babel/runtime@^7.1.2", "@babel/runtime@^7.12.5", "@babel/runtime@^7.3.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/runtime/-/runtime-7.27.1.tgz"
  integrity sha512-1x3D2xEk2fRo3PAhwQwu5UubzgiVWSXTBfWpVd2Mx2AzRqJuDJCsgaDVZ7HB5iGzDW1Hl1sWN2mFyKjmR9uAog==

"@babel/runtime@^7.24.7":
  version "7.27.6"
  resolved "https://registry.npmjs.org/@babel/runtime/-/runtime-7.27.6.tgz"
  integrity sha512-vbavdySgbTTrmFE+EsiqUTzlOr5bzlnJtUv9PynGCAKvfQqjIXbvFdumPM/GxMDfyuGMJaJAU6TO4zc1Jf1i8Q==

"@babel/template@^7.27.1", "@babel/template@^7.3.3":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/template/-/template-7.27.1.tgz"
  integrity sha512-Fyo3ghWMqkHHpHQCoBs2VnYjR4iWFFjguTDEqA5WgZDOrFesVjMhMM2FSqTKSoUSDO1VQtavj8NFpdRBEvJTtg==
  dependencies:
    "@babel/code-frame" "^7.27.1"
    "@babel/parser" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/traverse@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/traverse/-/traverse-7.27.1.tgz"
  integrity sha512-ZCYtZciz1IWJB4U61UPu4KEaqyfj+r5T1Q5mqPo+IBpcG9kHv30Z0aD8LXPgC1trYa6rK0orRyAhqUgk4MjmEg==
  dependencies:
    "@babel/code-frame" "^7.27.1"
    "@babel/generator" "^7.27.1"
    "@babel/parser" "^7.27.1"
    "@babel/template" "^7.27.1"
    "@babel/types" "^7.27.1"
    debug "^4.3.1"
    globals "^11.1.0"

"@babel/types@^7.0.0", "@babel/types@^7.20.7", "@babel/types@^7.27.1", "@babel/types@^7.3.3", "@babel/types@^7.4.4":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/types/-/types-7.27.1.tgz"
  integrity sha512-+EzkxvLNfiUeKMgy/3luqfsCWFRXLb7U6wNQTk60tovuckwB15B191tJWvpp4HjiQWdJkCxO3Wbvc6jlk3Xb2Q==
  dependencies:
    "@babel/helper-string-parser" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"

"@bcoe/v8-coverage@^0.2.3":
  version "0.2.3"
  resolved "https://registry.npmjs.org/@bcoe/v8-coverage/-/v8-coverage-0.2.3.tgz"
  integrity sha512-0hYQ8SB4Db5zvZB4axdMHGwEaQjkZzFjQiN9LVYvIFB2nSUHW9tYpxWriPrWDASIxiaXax83REcLxuSdnGPZtw==

"@bugsnag/browser@^8.4.0":
  version "8.4.0"
  resolved "https://registry.npmjs.org/@bugsnag/browser/-/browser-8.4.0.tgz"
  integrity sha512-5ZzGZtCwvhQbrMCAPAH9ruQGjVmSzjiE6qNNP2mD/8q0Yi45TWBtG/0MdlUYpDwx2lxVVHaGHqI3GBeD7B4Hqg==
  dependencies:
    "@bugsnag/core" "^8.4.0"

"@bugsnag/core@^8.4.0":
  version "8.4.0"
  resolved "https://registry.npmjs.org/@bugsnag/core/-/core-8.4.0.tgz"
  integrity sha512-vmGNO5gQ2qP5CE6/RzIzO2X/5oJCQGrhdUc6OwpEf4CytPidpk9LNCkscvOx9iT9Ym3USKSo7DeZhtnhFP0KKQ==
  dependencies:
    "@bugsnag/cuid" "^3.0.0"
    "@bugsnag/safe-json-stringify" "^6.0.0"
    error-stack-parser "^2.0.3"
    iserror "^0.0.2"
    stack-generator "^2.0.3"

"@bugsnag/cuid@^3.0.0":
  version "3.2.1"
  resolved "https://registry.npmjs.org/@bugsnag/cuid/-/cuid-3.2.1.tgz"
  integrity sha512-zpvN8xQ5rdRWakMd/BcVkdn2F8HKlDSbM3l7duueK590WmI1T0ObTLc1V/1e55r14WNjPd5AJTYX4yPEAFVi+Q==

"@bugsnag/js@^8.4.0":
  version "8.4.0"
  resolved "https://registry.npmjs.org/@bugsnag/js/-/js-8.4.0.tgz"
  integrity sha512-r8M+kgNts3ebR7g9Kct2wuaipcxDEvrBwnBugJfHFRwelyysz5ZBkFAvpatSm71LyLTv/9FyvWKEVgXwV7dTBQ==
  dependencies:
    "@bugsnag/browser" "^8.4.0"
    "@bugsnag/node" "^8.4.0"

"@bugsnag/node@^8.4.0":
  version "8.4.0"
  resolved "https://registry.npmjs.org/@bugsnag/node/-/node-8.4.0.tgz"
  integrity sha512-yqlFTvJNRwnp8jQczfgBztAhSKFc5vr9CHTUDbB72gBgCzQVQ7q16MX0tIHZVeL1Mo1Zywr9rvcPG/HBAPTuOw==
  dependencies:
    "@bugsnag/core" "^8.4.0"
    byline "^5.0.0"
    error-stack-parser "^2.0.3"
    iserror "^0.0.2"
    pump "^3.0.0"
    stack-generator "^2.0.3"

"@bugsnag/safe-json-stringify@^6.0.0":
  version "6.0.0"
  resolved "https://registry.npmjs.org/@bugsnag/safe-json-stringify/-/safe-json-stringify-6.0.0.tgz"
  integrity sha512-htzFO1Zc57S8kgdRK9mLcPVTW1BY2ijfH7Dk2CeZmspTWKdKqSo1iwmqrq2WtRjFlo8aRZYgLX0wFrDXF/9DLA==

"@cspotcode/source-map-support@^0.8.0":
  version "0.8.1"
  resolved "https://registry.npmjs.org/@cspotcode/source-map-support/-/source-map-support-0.8.1.tgz"
  integrity sha512-IchNf6dN4tHoMFIn/7OE8LWZ19Y6q/67Bmf6vnGREv8RSbBVb9LPJxEcnwrcwX6ixSvaiGoomAUvu4YSxXrVgw==
  dependencies:
    "@jridgewell/trace-mapping" "0.3.9"

"@eslint-community/eslint-utils@^4.2.0", "@eslint-community/eslint-utils@^4.4.0":
  version "4.7.0"
  resolved "https://registry.npmjs.org/@eslint-community/eslint-utils/-/eslint-utils-4.7.0.tgz"
  integrity sha512-dyybb3AcajC7uha6CvhdVRJqaKyn7w2YKqKyAN37NKYgZT36w+iRb0Dymmc5qEJ549c/S31cMMSFd75bteCpCw==
  dependencies:
    eslint-visitor-keys "^3.4.3"

"@eslint-community/regexpp@^4.10.0", "@eslint-community/regexpp@^4.12.1":
  version "4.12.1"
  resolved "https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-4.12.1.tgz"
  integrity sha512-CCZCDJuduB9OUkFkY2IgppNZMi2lBQgD2qzwXkEia16cge2pijY/aXi96CJMquDMn3nJdlPV1A5KrJEXwfLNzQ==

"@eslint/config-array@^0.20.0":
  version "0.20.0"
  resolved "https://registry.npmjs.org/@eslint/config-array/-/config-array-0.20.0.tgz"
  integrity sha512-fxlS1kkIjx8+vy2SjuCB94q3htSNrufYTXubwiBFeaQHbH6Ipi43gFJq2zCMt6PHhImH3Xmr0NksKDvchWlpQQ==
  dependencies:
    "@eslint/object-schema" "^2.1.6"
    debug "^4.3.1"
    minimatch "^3.1.2"

"@eslint/config-helpers@^0.2.1":
  version "0.2.2"
  resolved "https://registry.npmjs.org/@eslint/config-helpers/-/config-helpers-0.2.2.tgz"
  integrity sha512-+GPzk8PlG0sPpzdU5ZvIRMPidzAnZDl/s9L+y13iodqvb8leL53bTannOrQ/Im7UkpsmFU5Ily5U60LWixnmLg==

"@eslint/core@^0.13.0":
  version "0.13.0"
  resolved "https://registry.npmjs.org/@eslint/core/-/core-0.13.0.tgz"
  integrity sha512-yfkgDw1KR66rkT5A8ci4irzDysN7FRpq3ttJolR88OqQikAWqwA8j5VZyas+vjyBNFIJ7MfybJ9plMILI2UrCw==
  dependencies:
    "@types/json-schema" "^7.0.15"

"@eslint/eslintrc@^3", "@eslint/eslintrc@^3.3.1":
  version "3.3.1"
  resolved "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-3.3.1.tgz"
  integrity sha512-gtF186CXhIl1p4pJNGZw8Yc6RlshoePRvE0X91oPGb3vZ8pM3qOS9W9NGPat9LziaBV7XrJWGylNQXkGcnM3IQ==
  dependencies:
    ajv "^6.12.4"
    debug "^4.3.2"
    espree "^10.0.1"
    globals "^14.0.0"
    ignore "^5.2.0"
    import-fresh "^3.2.1"
    js-yaml "^4.1.0"
    minimatch "^3.1.2"
    strip-json-comments "^3.1.1"

"@eslint/js@^9.22.0", "@eslint/js@9.26.0":
  version "9.26.0"
  resolved "https://registry.npmjs.org/@eslint/js/-/js-9.26.0.tgz"
  integrity sha512-I9XlJawFdSMvWjDt6wksMCrgns5ggLNfFwFvnShsleWruvXM514Qxk8V246efTw+eo9JABvVz+u3q2RiAowKxQ==

"@eslint/object-schema@^2.1.6":
  version "2.1.6"
  resolved "https://registry.npmjs.org/@eslint/object-schema/-/object-schema-2.1.6.tgz"
  integrity sha512-RBMg5FRL0I0gs51M/guSAj5/e14VQ4tpZnQNWwuDT66P14I43ItmPfIZRhO9fUVIPOAQXU47atlywZ/czoqFPA==

"@eslint/plugin-kit@^0.2.8":
  version "0.2.8"
  resolved "https://registry.npmjs.org/@eslint/plugin-kit/-/plugin-kit-0.2.8.tgz"
  integrity sha512-ZAoA40rNMPwSm+AeHpCq8STiNAwzWLJuP8Xv4CHIc9wv/PSuExjMrmjfYNj682vW0OOiZ1HKxzvjQr9XZIisQA==
  dependencies:
    "@eslint/core" "^0.13.0"
    levn "^0.4.1"

"@ferrucc-io/emoji-picker@^0.0.44":
  version "0.0.44"
  resolved "https://registry.npmjs.org/@ferrucc-io/emoji-picker/-/emoji-picker-0.0.44.tgz"
  integrity sha512-jMn4fSYQCc2Dlg5lE+X2LOLXDgzUDWXL/G5sBdv5auBDWNeGXtIpJYPhu/kItAeUTKfc+QWm8nwGRHWRiOn7ig==
  dependencies:
    "@tanstack/react-virtual" "^3.11.2"
    clsx "^2.1.1"
    jotai "^2.11.1"
    node-emoji "^2.1.3"
    tailwind-merge "^2.6.0"
    unicode-emoji-json "^0.8.0"

"@floating-ui/core@^1.7.0":
  version "1.7.0"
  resolved "https://registry.npmjs.org/@floating-ui/core/-/core-1.7.0.tgz"
  integrity sha512-FRdBLykrPPA6P76GGGqlex/e7fbe0F1ykgxHYNXQsH/iTEtjMj/f9bpY5oQqbjt5VgZvgz/uKXbGuROijh3VLA==
  dependencies:
    "@floating-ui/utils" "^0.2.9"

"@floating-ui/dom@^1.0.0":
  version "1.7.0"
  resolved "https://registry.npmjs.org/@floating-ui/dom/-/dom-1.7.0.tgz"
  integrity sha512-lGTor4VlXcesUMh1cupTUTDoCxMb0V6bm3CnxHzQcw8Eaf1jQbgQX4i02fYgT0vJ82tb5MZ4CZk1LRGkktJCzg==
  dependencies:
    "@floating-ui/core" "^1.7.0"
    "@floating-ui/utils" "^0.2.9"

"@floating-ui/react-dom@^2.0.0":
  version "2.1.2"
  resolved "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-2.1.2.tgz"
  integrity sha512-06okr5cgPzMNBy+Ycse2A6udMi4bqwW/zgBF/rwjcNqWkyr82Mcg8b0vjX8OJpZFy/FKjJmw6wV7t44kK6kW7A==
  dependencies:
    "@floating-ui/dom" "^1.0.0"

"@floating-ui/utils@^0.2.9":
  version "0.2.9"
  resolved "https://registry.npmjs.org/@floating-ui/utils/-/utils-0.2.9.tgz"
  integrity sha512-MDWhGtE+eHw5JW7lq4qhc5yRLS11ERl1c7Z6Xd0a58DozHES6EnNNwUWbMiG4J9Cgj053Bhk8zvlhFYKVhULwg==

"@google/generative-ai@^0.24.0":
  version "0.24.1"
  resolved "https://registry.npmjs.org/@google/generative-ai/-/generative-ai-0.24.1.tgz"
  integrity sha512-MqO+MLfM6kjxcKoy0p1wRzG3b4ZZXtPI+z2IE26UogS2Cm/XHO+7gGRBh6gcJsOiIVoH93UwKvW4HdgiOZCy9Q==

"@grpc/grpc-js@^1.7.1":
  version "1.13.4"
  resolved "https://registry.npmjs.org/@grpc/grpc-js/-/grpc-js-1.13.4.tgz"
  integrity sha512-GsFaMXCkMqkKIvwCQjCrwH+GHbPKBjhwo/8ZuUkWHqbI73Kky9I+pQltrlT0+MWpedCoosda53lgjYfyEPgxBg==
  dependencies:
    "@grpc/proto-loader" "^0.7.13"
    "@js-sdsl/ordered-map" "^4.4.2"

"@grpc/proto-loader@^0.7.13":
  version "0.7.15"
  resolved "https://registry.npmjs.org/@grpc/proto-loader/-/proto-loader-0.7.15.tgz"
  integrity sha512-tMXdRCfYVixjuFK+Hk0Q1s38gV9zDiDJfWL3h1rv4Qc39oILCu1TRTDt7+fGUI8K4G1Fj125Hx/ru3azECWTyQ==
  dependencies:
    lodash.camelcase "^4.3.0"
    long "^5.0.0"
    protobufjs "^7.2.5"
    yargs "^17.7.2"

"@honeycombio/opentelemetry-web@^0.20.0":
  version "0.20.0"
  resolved "https://registry.npmjs.org/@honeycombio/opentelemetry-web/-/opentelemetry-web-0.20.0.tgz"
  integrity sha512-RKVWricI3ltohaUkSR0dOwFzbHs+G7AiBPmN+8gxQ/pKerGwb3x4rtXsVUOwvaZ4F2Y2oPYeeTx60rdemMo54A==
  dependencies:
    "@babel/runtime" "^7.24.7"
    "@opentelemetry/api" "~1.9.0"
    "@opentelemetry/auto-instrumentations-web" "^0.48.0"
    "@opentelemetry/core" "^2.0.0"
    "@opentelemetry/exporter-logs-otlp-http" "^0.202.0"
    "@opentelemetry/exporter-metrics-otlp-http" "^0.202.0"
    "@opentelemetry/exporter-trace-otlp-http" "~0.202.0"
    "@opentelemetry/instrumentation" "~0.202.0"
    "@opentelemetry/opentelemetry-browser-detector" "~0.202.0"
    "@opentelemetry/resources" "^2.0.0"
    "@opentelemetry/sdk-trace-base" "^2.0.0"
    "@opentelemetry/sdk-trace-web" "^2.0.0"
    "@opentelemetry/semantic-conventions" "^1.30.0"
    "@opentelemetry/web-common" "~0.202.0"
    shimmer "^1.2.1"
    tracekit "^0.4.7"
    ua-parser-js "^1.0.37"
    web-vitals "^5.0.0"

"@hookform/resolvers@^5.0.1":
  version "5.0.1"
  resolved "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-5.0.1.tgz"
  integrity sha512-u/+Jp83luQNx9AdyW2fIPGY6Y7NG68eN2ZW8FOJYL+M0i4s49+refdJdOp/A9n9HFQtQs3HIDHQvX3ZET2o7YA==
  dependencies:
    "@standard-schema/utils" "^0.3.0"

"@humanfs/core@^0.19.1":
  version "0.19.1"
  resolved "https://registry.npmjs.org/@humanfs/core/-/core-0.19.1.tgz"
  integrity sha512-5DyQ4+1JEUzejeK1JGICcideyfUbGixgS9jNgex5nqkW+cY7WZhxBigmieN5Qnw9ZosSNVC9KQKyb+GUaGyKUA==

"@humanfs/node@^0.16.6":
  version "0.16.6"
  resolved "https://registry.npmjs.org/@humanfs/node/-/node-0.16.6.tgz"
  integrity sha512-YuI2ZHQL78Q5HbhDiBA1X4LmYdXCKCMQIfw0pw7piHJwyREFebJUvrQN4cMssyES6x+vfUbx1CIpaQUKYdQZOw==
  dependencies:
    "@humanfs/core" "^0.19.1"
    "@humanwhocodes/retry" "^0.3.0"

"@humanwhocodes/module-importer@^1.0.1":
  version "1.0.1"
  resolved "https://registry.npmjs.org/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz"
  integrity sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA==

"@humanwhocodes/retry@^0.3.0":
  version "0.3.1"
  resolved "https://registry.npmjs.org/@humanwhocodes/retry/-/retry-0.3.1.tgz"
  integrity sha512-JBxkERygn7Bv/GbN5Rv8Ul6LVknS+5Bp6RgDC/O8gEBU/yeH5Ui5C/OlWrTb6qct7LjjfT6Re2NxB0ln0yYybA==

"@humanwhocodes/retry@^0.4.2":
  version "0.4.2"
  resolved "https://registry.npmjs.org/@humanwhocodes/retry/-/retry-0.4.2.tgz"
  integrity sha512-xeO57FpIu4p1Ri3Jq/EXq4ClRm86dVF2z/+kvFnyqVYRavTZmaFaUBbWCOuuTh0o/g7DSsk6kc2vrS4Vl5oPOQ==

"@img/sharp-darwin-arm64@0.33.5":
  version "0.33.5"
  resolved "https://registry.npmjs.org/@img/sharp-darwin-arm64/-/sharp-darwin-arm64-0.33.5.tgz"
  integrity sha512-UT4p+iz/2H4twwAoLCqfA9UH5pI6DggwKEGuaPy7nCVQ8ZsiY5PIcrRvD1DzuY3qYL07NtIQcWnBSY/heikIFQ==
  optionalDependencies:
    "@img/sharp-libvips-darwin-arm64" "1.0.4"

"@img/sharp-libvips-darwin-arm64@1.0.4":
  version "1.0.4"
  resolved "https://registry.npmjs.org/@img/sharp-libvips-darwin-arm64/-/sharp-libvips-darwin-arm64-1.0.4.tgz"
  integrity sha512-XblONe153h0O2zuFfTAbQYAX2JhYmDHeWikp1LM9Hul9gVPjFY427k6dFEcOL72O01QxQsWi761svJ/ev9xEDg==

"@isaacs/cliui@^8.0.2":
  version "8.0.2"
  resolved "https://registry.npmjs.org/@isaacs/cliui/-/cliui-8.0.2.tgz"
  integrity sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA==
  dependencies:
    string-width "^5.1.2"
    string-width-cjs "npm:string-width@^4.2.0"
    strip-ansi "^7.0.1"
    strip-ansi-cjs "npm:strip-ansi@^6.0.1"
    wrap-ansi "^8.1.0"
    wrap-ansi-cjs "npm:wrap-ansi@^7.0.0"

"@isaacs/fs-minipass@^4.0.0":
  version "4.0.1"
  resolved "https://registry.npmjs.org/@isaacs/fs-minipass/-/fs-minipass-4.0.1.tgz"
  integrity sha512-wgm9Ehl2jpeqP3zw/7mo3kRHFp5MEDhqAdwy1fTGkHAwnkGOVsgpvQhL8B5n1qlb01jV3n/bI0ZfZp5lWA1k4w==
  dependencies:
    minipass "^7.0.4"

"@istanbuljs/load-nyc-config@^1.0.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@istanbuljs/load-nyc-config/-/load-nyc-config-1.1.0.tgz"
  integrity sha512-VjeHSlIzpv/NyD3N0YuHfXOPDIixcA1q2ZV98wsMqcYlPmv2n3Yb2lYP9XMElnaFVXg5A7YLTeLu6V84uQDjmQ==
  dependencies:
    camelcase "^5.3.1"
    find-up "^4.1.0"
    get-package-type "^0.1.0"
    js-yaml "^3.13.1"
    resolve-from "^5.0.0"

"@istanbuljs/schema@^0.1.2", "@istanbuljs/schema@^0.1.3":
  version "0.1.3"
  resolved "https://registry.npmjs.org/@istanbuljs/schema/-/schema-0.1.3.tgz"
  integrity sha512-ZXRY4jNvVgSVQ8DL3LTcakaAtXwTVUxE81hslsyD2AtoXW/wVob10HkOJ1X/pAlcI7D+2YoZKg5do8G/w6RYgA==

"@jest/console@^29.7.0":
  version "29.7.0"
  resolved "https://registry.npmjs.org/@jest/console/-/console-29.7.0.tgz"
  integrity sha512-5Ni4CU7XHQi32IJ398EEP4RrB8eV09sXP2ROqD4bksHrnTree52PsxvX8tpL8LvTZ3pFzXyPbNQReSN41CAhOg==
  dependencies:
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    chalk "^4.0.0"
    jest-message-util "^29.7.0"
    jest-util "^29.7.0"
    slash "^3.0.0"

"@jest/core@^29.7.0":
  version "29.7.0"
  resolved "https://registry.npmjs.org/@jest/core/-/core-29.7.0.tgz"
  integrity sha512-n7aeXWKMnGtDA48y8TLWJPJmLmmZ642Ceo78cYWEpiD7FzDgmNDV/GCVRorPABdXLJZ/9wzzgZAlHjXjxDHGsg==
  dependencies:
    "@jest/console" "^29.7.0"
    "@jest/reporters" "^29.7.0"
    "@jest/test-result" "^29.7.0"
    "@jest/transform" "^29.7.0"
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    ansi-escapes "^4.2.1"
    chalk "^4.0.0"
    ci-info "^3.2.0"
    exit "^0.1.2"
    graceful-fs "^4.2.9"
    jest-changed-files "^29.7.0"
    jest-config "^29.7.0"
    jest-haste-map "^29.7.0"
    jest-message-util "^29.7.0"
    jest-regex-util "^29.6.3"
    jest-resolve "^29.7.0"
    jest-resolve-dependencies "^29.7.0"
    jest-runner "^29.7.0"
    jest-runtime "^29.7.0"
    jest-snapshot "^29.7.0"
    jest-util "^29.7.0"
    jest-validate "^29.7.0"
    jest-watcher "^29.7.0"
    micromatch "^4.0.4"
    pretty-format "^29.7.0"
    slash "^3.0.0"
    strip-ansi "^6.0.0"

"@jest/environment@^29.7.0":
  version "29.7.0"
  resolved "https://registry.npmjs.org/@jest/environment/-/environment-29.7.0.tgz"
  integrity sha512-aQIfHDq33ExsN4jP1NWGXhxgQ/wixs60gDiKO+XVMd8Mn0NWPWgc34ZQDTb2jKaUWQ7MuwoitXAsN2XVXNMpAw==
  dependencies:
    "@jest/fake-timers" "^29.7.0"
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    jest-mock "^29.7.0"

"@jest/expect-utils@^29.7.0":
  version "29.7.0"
  resolved "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-29.7.0.tgz"
  integrity sha512-GlsNBWiFQFCVi9QVSx7f5AgMeLxe9YCCs5PuP2O2LdjDAA8Jh9eX7lA1Jq/xdXw3Wb3hyvlFNfZIfcRetSzYcA==
  dependencies:
    jest-get-type "^29.6.3"

"@jest/expect@^29.7.0":
  version "29.7.0"
  resolved "https://registry.npmjs.org/@jest/expect/-/expect-29.7.0.tgz"
  integrity sha512-8uMeAMycttpva3P1lBHB8VciS9V0XAr3GymPpipdyQXbBcuhkLQOSe8E/p92RyAdToS6ZD1tFkX+CkhoECE0dQ==
  dependencies:
    expect "^29.7.0"
    jest-snapshot "^29.7.0"

"@jest/fake-timers@^29.7.0":
  version "29.7.0"
  resolved "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-29.7.0.tgz"
  integrity sha512-q4DH1Ha4TTFPdxLsqDXK1d3+ioSL7yL5oCMJZgDYm6i+6CygW5E5xVr/D1HdsGxjt1ZWSfUAs9OxSB/BNelWrQ==
  dependencies:
    "@jest/types" "^29.6.3"
    "@sinonjs/fake-timers" "^10.0.2"
    "@types/node" "*"
    jest-message-util "^29.7.0"
    jest-mock "^29.7.0"
    jest-util "^29.7.0"

"@jest/globals@^29.7.0":
  version "29.7.0"
  resolved "https://registry.npmjs.org/@jest/globals/-/globals-29.7.0.tgz"
  integrity sha512-mpiz3dutLbkW2MNFubUGUEVLkTGiqW6yLVTA+JbP6fI6J5iL9Y0Nlg8k95pcF8ctKwCS7WVxteBs29hhfAotzQ==
  dependencies:
    "@jest/environment" "^29.7.0"
    "@jest/expect" "^29.7.0"
    "@jest/types" "^29.6.3"
    jest-mock "^29.7.0"

"@jest/reporters@^29.7.0":
  version "29.7.0"
  resolved "https://registry.npmjs.org/@jest/reporters/-/reporters-29.7.0.tgz"
  integrity sha512-DApq0KJbJOEzAFYjHADNNxAE3KbhxQB1y5Kplb5Waqw6zVbuWatSnMjE5gs8FUgEPmNsnZA3NCWl9NG0ia04Pg==
  dependencies:
    "@bcoe/v8-coverage" "^0.2.3"
    "@jest/console" "^29.7.0"
    "@jest/test-result" "^29.7.0"
    "@jest/transform" "^29.7.0"
    "@jest/types" "^29.6.3"
    "@jridgewell/trace-mapping" "^0.3.18"
    "@types/node" "*"
    chalk "^4.0.0"
    collect-v8-coverage "^1.0.0"
    exit "^0.1.2"
    glob "^7.1.3"
    graceful-fs "^4.2.9"
    istanbul-lib-coverage "^3.0.0"
    istanbul-lib-instrument "^6.0.0"
    istanbul-lib-report "^3.0.0"
    istanbul-lib-source-maps "^4.0.0"
    istanbul-reports "^3.1.3"
    jest-message-util "^29.7.0"
    jest-util "^29.7.0"
    jest-worker "^29.7.0"
    slash "^3.0.0"
    string-length "^4.0.1"
    strip-ansi "^6.0.0"
    v8-to-istanbul "^9.0.1"

"@jest/schemas@^29.6.3":
  version "29.6.3"
  resolved "https://registry.npmjs.org/@jest/schemas/-/schemas-29.6.3.tgz"
  integrity sha512-mo5j5X+jIZmJQveBKeS/clAueipV7KgiX1vMgCxam1RNYiqE1w62n0/tJJnHtjW8ZHcQco5gY85jA3mi0L+nSA==
  dependencies:
    "@sinclair/typebox" "^0.27.8"

"@jest/source-map@^29.6.3":
  version "29.6.3"
  resolved "https://registry.npmjs.org/@jest/source-map/-/source-map-29.6.3.tgz"
  integrity sha512-MHjT95QuipcPrpLM+8JMSzFx6eHp5Bm+4XeFDJlwsvVBjmKNiIAvasGK2fxz2WbGRlnvqehFbh07MMa7n3YJnw==
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.18"
    callsites "^3.0.0"
    graceful-fs "^4.2.9"

"@jest/test-result@^29.7.0":
  version "29.7.0"
  resolved "https://registry.npmjs.org/@jest/test-result/-/test-result-29.7.0.tgz"
  integrity sha512-Fdx+tv6x1zlkJPcWXmMDAG2HBnaR9XPSd5aDWQVsfrZmLVT3lU1cwyxLgRmXR9yrq4NBoEm9BMsfgFzTQAbJYA==
  dependencies:
    "@jest/console" "^29.7.0"
    "@jest/types" "^29.6.3"
    "@types/istanbul-lib-coverage" "^2.0.0"
    collect-v8-coverage "^1.0.0"

"@jest/test-sequencer@^29.7.0":
  version "29.7.0"
  resolved "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-29.7.0.tgz"
  integrity sha512-GQwJ5WZVrKnOJuiYiAF52UNUJXgTZx1NHjFSEB0qEMmSZKAkdMoIzw/Cj6x6NF4AvV23AUqDpFzQkN/eYCYTxw==
  dependencies:
    "@jest/test-result" "^29.7.0"
    graceful-fs "^4.2.9"
    jest-haste-map "^29.7.0"
    slash "^3.0.0"

"@jest/transform@^29.7.0":
  version "29.7.0"
  resolved "https://registry.npmjs.org/@jest/transform/-/transform-29.7.0.tgz"
  integrity sha512-ok/BTPFzFKVMwO5eOHRrvnBVHdRy9IrsrW1GpMaQ9MCnilNLXQKmAX8s1YXDFaai9xJpac2ySzV0YeRRECr2Vw==
  dependencies:
    "@babel/core" "^7.11.6"
    "@jest/types" "^29.6.3"
    "@jridgewell/trace-mapping" "^0.3.18"
    babel-plugin-istanbul "^6.1.1"
    chalk "^4.0.0"
    convert-source-map "^2.0.0"
    fast-json-stable-stringify "^2.1.0"
    graceful-fs "^4.2.9"
    jest-haste-map "^29.7.0"
    jest-regex-util "^29.6.3"
    jest-util "^29.7.0"
    micromatch "^4.0.4"
    pirates "^4.0.4"
    slash "^3.0.0"
    write-file-atomic "^4.0.2"

"@jest/types@^29.6.3":
  version "29.6.3"
  resolved "https://registry.npmjs.org/@jest/types/-/types-29.6.3.tgz"
  integrity sha512-u3UPsIilWKOM3F9CXtrG8LEJmNxwoCQC/XVj4IKYXvvpx7QIi/Kg1LI5uDmDpKlac62NUtX7eLjRh+jVZcLOzw==
  dependencies:
    "@jest/schemas" "^29.6.3"
    "@types/istanbul-lib-coverage" "^2.0.0"
    "@types/istanbul-reports" "^3.0.0"
    "@types/node" "*"
    "@types/yargs" "^17.0.8"
    chalk "^4.0.0"

"@jridgewell/gen-mapping@^0.3.2", "@jridgewell/gen-mapping@^0.3.5":
  version "0.3.8"
  resolved "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.8.tgz"
  integrity sha512-imAbBGkb+ebQyxKgzv5Hu2nmROxoDOXHh80evxdoXNOrvAnVx7zimzc1Oo5h9RlfV4vPXaE2iM5pOFbvOCClWA==
  dependencies:
    "@jridgewell/set-array" "^1.2.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.24"

"@jridgewell/resolve-uri@^3.0.3", "@jridgewell/resolve-uri@^3.1.0":
  version "3.1.2"
  resolved "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz"
  integrity sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==

"@jridgewell/set-array@^1.2.1":
  version "1.2.1"
  resolved "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.2.1.tgz"
  integrity sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==

"@jridgewell/sourcemap-codec@^1.4.10", "@jridgewell/sourcemap-codec@^1.4.14", "@jridgewell/sourcemap-codec@^1.4.15", "@jridgewell/sourcemap-codec@^1.5.0":
  version "1.5.0"
  resolved "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz"
  integrity sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==

"@jridgewell/trace-mapping@^0.3.12", "@jridgewell/trace-mapping@^0.3.18", "@jridgewell/trace-mapping@^0.3.24", "@jridgewell/trace-mapping@^0.3.25":
  version "0.3.25"
  resolved "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz"
  integrity sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

"@jridgewell/trace-mapping@0.3.9":
  version "0.3.9"
  resolved "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.9.tgz"
  integrity sha512-3Belt6tdc8bPgAtbcmdtNJlirVoTmEb5e2gC94PnkwEW9jI6CAHUeoG85tjWP5WquqfavoMtMwiG4P926ZKKuQ==
  dependencies:
    "@jridgewell/resolve-uri" "^3.0.3"
    "@jridgewell/sourcemap-codec" "^1.4.10"

"@js-sdsl/ordered-map@^4.4.2":
  version "4.4.2"
  resolved "https://registry.npmjs.org/@js-sdsl/ordered-map/-/ordered-map-4.4.2.tgz"
  integrity sha512-iUKgm52T8HOE/makSxjqoWhe95ZJA1/G1sYsGev2JDKUSS14KAgg1LHb+Ba+IPow0xflbnSkOsZcO08C7w1gYw==

"@june-so/analytics-next@^4.0.0":
  version "4.0.0"
  resolved "https://registry.npmjs.org/@june-so/analytics-next/-/analytics-next-4.0.0.tgz"
  integrity sha512-PzW9BTDkxXlHEb1GsO/xmHrenc77YiiTcg/ZdbVbCRoVzPwN2cGvxRuzAjdYr0JPYZI8UNjoJiyUaL0ObOd8Lw==
  dependencies:
    "@lukeed/uuid" "^2.0.0"
    "@segment/analytics-core" "1.2.2"
    "@segment/analytics.js-video-plugins" "^0.2.1"
    "@segment/facade" "^3.4.9"
    dset "^3.1.2"
    js-cookie "3.0.1"
    node-fetch "^2.6.7"
    spark-md5 "^3.0.1"
    tslib "^2.4.1"
    typescript "^4.9.5"
    unfetch "^4.1.0"

"@lukeed/csprng@^1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@lukeed/csprng/-/csprng-1.1.0.tgz"
  integrity sha512-Z7C/xXCiGWsg0KuKsHTKJxbWhpI3Vs5GwLfOean7MGyVFGqdRgBbAjOCh6u4bbjPc/8MJ2pZmK/0DLdCbivLDA==

"@lukeed/uuid@^2.0.0":
  version "2.0.1"
  resolved "https://registry.npmjs.org/@lukeed/uuid/-/uuid-2.0.1.tgz"
  integrity sha512-qC72D4+CDdjGqJvkFMMEAtancHUQ7/d/tAiHf64z8MopFDmcrtbcJuerDtFceuAfQJ2pDSfCKCtbqoGBNnwg0w==
  dependencies:
    "@lukeed/csprng" "^1.1.0"

"@modelcontextprotocol/sdk@^1.8.0":
  version "1.11.0"
  resolved "https://registry.npmjs.org/@modelcontextprotocol/sdk/-/sdk-1.11.0.tgz"
  integrity sha512-k/1pb70eD638anoi0e8wUGAlbMJXyvdV4p62Ko+EZ7eBe1xMx8Uhak1R5DgfoofsK5IBBnRwsYGTaLZl+6/+RQ==
  dependencies:
    content-type "^1.0.5"
    cors "^2.8.5"
    cross-spawn "^7.0.3"
    eventsource "^3.0.2"
    express "^5.0.1"
    express-rate-limit "^7.5.0"
    pkce-challenge "^5.0.0"
    raw-body "^3.0.0"
    zod "^3.23.8"
    zod-to-json-schema "^3.24.1"

"@monaco-editor/loader@^1.5.0":
  version "1.5.0"
  resolved "https://registry.npmjs.org/@monaco-editor/loader/-/loader-1.5.0.tgz"
  integrity sha512-hKoGSM+7aAc7eRTRjpqAZucPmoNOC4UUbknb/VNoTkEIkCPhqV8LfbsgM1webRM7S/z21eHEx9Fkwx8Z/C/+Xw==
  dependencies:
    state-local "^1.0.6"

"@monaco-editor/react@^4.7.0":
  version "4.7.0"
  resolved "https://registry.npmjs.org/@monaco-editor/react/-/react-4.7.0.tgz"
  integrity sha512-cyzXQCtO47ydzxpQtCGSQGOC8Gk3ZUeBXFAxD+CWXYFo5OqZyZUonFl0DwUlTyAfRHntBfw2p3w4s9R6oe1eCA==
  dependencies:
    "@monaco-editor/loader" "^1.5.0"

"@next/env@15.1.7":
  version "15.1.7"
  resolved "https://registry.npmjs.org/@next/env/-/env-15.1.7.tgz"
  integrity sha512-d9jnRrkuOH7Mhi+LHav2XW91HOgTAWHxjMPkXMGBc9B2b7614P7kjt8tAplRvJpbSt4nbO1lugcT/kAaWzjlLQ==

"@next/eslint-plugin-next@^15.2.1":
  version "15.3.1"
  resolved "https://registry.npmjs.org/@next/eslint-plugin-next/-/eslint-plugin-next-15.3.1.tgz"
  integrity sha512-oEs4dsfM6iyER3jTzMm4kDSbrQJq8wZw5fmT6fg2V3SMo+kgG+cShzLfEV20senZzv8VF+puNLheiGPlBGsv2A==
  dependencies:
    fast-glob "3.3.1"

"@next/eslint-plugin-next@15.1.7":
  version "15.1.7"
  resolved "https://registry.npmjs.org/@next/eslint-plugin-next/-/eslint-plugin-next-15.1.7.tgz"
  integrity sha512-kRP7RjSxfTO13NE317ek3mSGzoZlI33nc/i5hs1KaWpK+egs85xg0DJ4p32QEiHnR0mVjuUfhRIun7awqfL7pQ==
  dependencies:
    fast-glob "3.3.1"

"@next/swc-darwin-arm64@15.1.7":
  version "15.1.7"
  resolved "https://registry.npmjs.org/@next/swc-darwin-arm64/-/swc-darwin-arm64-15.1.7.tgz"
  integrity sha512-hPFwzPJDpA8FGj7IKV3Yf1web3oz2YsR8du4amKw8d+jAOHfYHYFpMkoF6vgSY4W6vB29RtZEklK9ayinGiCmQ==

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  resolved "https://registry.npmjs.org/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz"
  integrity sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@^2.0.2", "@nodelib/fs.stat@2.0.5":
  version "2.0.5"
  resolved "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz"
  integrity sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==

"@nodelib/fs.walk@^1.2.3":
  version "1.2.8"
  resolved "https://registry.npmjs.org/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz"
  integrity sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@nolyfill/is-core-module@1.0.39":
  version "1.0.39"
  resolved "https://registry.npmjs.org/@nolyfill/is-core-module/-/is-core-module-1.0.39.tgz"
  integrity sha512-nn5ozdjYQpUCZlWGuxcJY/KpxkWQs4DcbMCmKojjyrYDEAGy4Ce19NN4v5MduafTwJlbKc99UA8YhSVqq9yPZA==

"@opentelemetry/api-logs@^0.202.0", "@opentelemetry/api-logs@0.202.0":
  version "0.202.0"
  resolved "https://registry.npmjs.org/@opentelemetry/api-logs/-/api-logs-0.202.0.tgz"
  integrity sha512-fTBjMqKCfotFWfLzaKyhjLvyEyq5vDKTTFfBmx21btv3gvy8Lq6N5Dh2OzqeuN4DjtpSvNT1uNVfg08eD2Rfxw==
  dependencies:
    "@opentelemetry/api" "^1.3.0"

"@opentelemetry/api-logs@0.57.2":
  version "0.57.2"
  resolved "https://registry.npmjs.org/@opentelemetry/api-logs/-/api-logs-0.57.2.tgz"
  integrity sha512-uIX52NnTM0iBh84MShlpouI7UKqkZ7MrUszTmaypHBu4r7NofznSnQRfJ+uUeDtQDj6w8eFGg5KBLDAwAPz1+A==
  dependencies:
    "@opentelemetry/api" "^1.3.0"

"@opentelemetry/api@^1.3.0", "@opentelemetry/api@^1.9.0", "@opentelemetry/api@~1.9.0", "@opentelemetry/api@1.9.0":
  version "1.9.0"
  resolved "https://registry.npmjs.org/@opentelemetry/api/-/api-1.9.0.tgz"
  integrity sha512-3giAOQvZiH5F9bMlMiv8+GSPMeqg0dbaeo58/0SlA9sxSqZhnUtxzX9/2FzyhS9sWQf5S0GJE0AKBrFqjpeYcg==

"@opentelemetry/auto-instrumentations-node@^0.60.1":
  version "0.60.1"
  resolved "https://registry.npmjs.org/@opentelemetry/auto-instrumentations-node/-/auto-instrumentations-node-0.60.1.tgz"
  integrity sha512-oMBVXiun0qWhj693Y24Ie+75q45YXHRFeH9vX/XBWKRNJIM/02ufjmNvmOdoHY0EPxU9rBmWCW82Uidf54iSPA==
  dependencies:
    "@opentelemetry/instrumentation" "^0.202.0"
    "@opentelemetry/instrumentation-amqplib" "^0.49.0"
    "@opentelemetry/instrumentation-aws-lambda" "^0.53.0"
    "@opentelemetry/instrumentation-aws-sdk" "^0.54.0"
    "@opentelemetry/instrumentation-bunyan" "^0.48.0"
    "@opentelemetry/instrumentation-cassandra-driver" "^0.48.0"
    "@opentelemetry/instrumentation-connect" "^0.46.0"
    "@opentelemetry/instrumentation-cucumber" "^0.17.0"
    "@opentelemetry/instrumentation-dataloader" "^0.19.0"
    "@opentelemetry/instrumentation-dns" "^0.46.0"
    "@opentelemetry/instrumentation-express" "^0.51.0"
    "@opentelemetry/instrumentation-fastify" "^0.47.0"
    "@opentelemetry/instrumentation-fs" "^0.22.0"
    "@opentelemetry/instrumentation-generic-pool" "^0.46.0"
    "@opentelemetry/instrumentation-graphql" "^0.50.0"
    "@opentelemetry/instrumentation-grpc" "^0.202.0"
    "@opentelemetry/instrumentation-hapi" "^0.49.0"
    "@opentelemetry/instrumentation-http" "^0.202.0"
    "@opentelemetry/instrumentation-ioredis" "^0.50.0"
    "@opentelemetry/instrumentation-kafkajs" "^0.11.0"
    "@opentelemetry/instrumentation-knex" "^0.47.0"
    "@opentelemetry/instrumentation-koa" "^0.50.1"
    "@opentelemetry/instrumentation-lru-memoizer" "^0.47.0"
    "@opentelemetry/instrumentation-memcached" "^0.46.0"
    "@opentelemetry/instrumentation-mongodb" "^0.55.1"
    "@opentelemetry/instrumentation-mongoose" "^0.49.0"
    "@opentelemetry/instrumentation-mysql" "^0.48.0"
    "@opentelemetry/instrumentation-mysql2" "^0.48.0"
    "@opentelemetry/instrumentation-nestjs-core" "^0.48.0"
    "@opentelemetry/instrumentation-net" "^0.46.1"
    "@opentelemetry/instrumentation-oracledb" "^0.28.0"
    "@opentelemetry/instrumentation-pg" "^0.54.0"
    "@opentelemetry/instrumentation-pino" "^0.49.0"
    "@opentelemetry/instrumentation-redis" "^0.49.1"
    "@opentelemetry/instrumentation-redis-4" "^0.49.0"
    "@opentelemetry/instrumentation-restify" "^0.48.1"
    "@opentelemetry/instrumentation-router" "^0.47.0"
    "@opentelemetry/instrumentation-runtime-node" "^0.16.0"
    "@opentelemetry/instrumentation-socket.io" "^0.49.0"
    "@opentelemetry/instrumentation-tedious" "^0.21.0"
    "@opentelemetry/instrumentation-undici" "^0.13.1"
    "@opentelemetry/instrumentation-winston" "^0.47.0"
    "@opentelemetry/resource-detector-alibaba-cloud" "^0.31.2"
    "@opentelemetry/resource-detector-aws" "^2.2.0"
    "@opentelemetry/resource-detector-azure" "^0.9.0"
    "@opentelemetry/resource-detector-container" "^0.7.2"
    "@opentelemetry/resource-detector-gcp" "^0.36.0"
    "@opentelemetry/resources" "^2.0.0"
    "@opentelemetry/sdk-node" "^0.202.0"

"@opentelemetry/auto-instrumentations-web@^0.48.0":
  version "0.48.0"
  resolved "https://registry.npmjs.org/@opentelemetry/auto-instrumentations-web/-/auto-instrumentations-web-0.48.0.tgz"
  integrity sha512-8+nhw7s4TsGGqFznEx3lc3FHMOk4DURmdzUGlGyE/M6AX3fc0mRwBMmuWgJY43QqxhhRXJ6wgEXMsiB52b/Jkw==
  dependencies:
    "@opentelemetry/instrumentation" "^0.202.0"
    "@opentelemetry/instrumentation-document-load" "^0.47.0"
    "@opentelemetry/instrumentation-fetch" "^0.202.0"
    "@opentelemetry/instrumentation-user-interaction" "^0.47.0"
    "@opentelemetry/instrumentation-xml-http-request" "^0.202.0"

"@opentelemetry/context-async-hooks@^1.30.1":
  version "1.30.1"
  resolved "https://registry.npmjs.org/@opentelemetry/context-async-hooks/-/context-async-hooks-1.30.1.tgz"
  integrity sha512-s5vvxXPVdjqS3kTLKMeBMvop9hbWkwzBpu+mUO2M7sZtlkyDJGwFe33wRKnbaYDo8ExRVBIIdwIGrqpxHuKttA==

"@opentelemetry/context-async-hooks@2.0.1":
  version "2.0.1"
  resolved "https://registry.npmjs.org/@opentelemetry/context-async-hooks/-/context-async-hooks-2.0.1.tgz"
  integrity sha512-XuY23lSI3d4PEqKA+7SLtAgwqIfc6E/E9eAQWLN1vlpC53ybO3o6jW4BsXo1xvz9lYyyWItfQDDLzezER01mCw==

"@opentelemetry/core@^1.1.0":
  version "1.30.1"
  resolved "https://registry.npmjs.org/@opentelemetry/core/-/core-1.30.1.tgz"
  integrity sha512-OOCM2C/QIURhJMuKaekP3TRBxBKxG/TWWA0TL2J6nXUtDnuCtccy49LUJF8xPFXMX+0LMcxFpCo8M9cGY1W6rQ==
  dependencies:
    "@opentelemetry/semantic-conventions" "1.28.0"

"@opentelemetry/core@^1.26.0", "@opentelemetry/core@^1.30.1", "@opentelemetry/core@^1.8.0", "@opentelemetry/core@1.30.1":
  version "1.30.1"
  resolved "https://registry.npmjs.org/@opentelemetry/core/-/core-1.30.1.tgz"
  integrity sha512-OOCM2C/QIURhJMuKaekP3TRBxBKxG/TWWA0TL2J6nXUtDnuCtccy49LUJF8xPFXMX+0LMcxFpCo8M9cGY1W6rQ==
  dependencies:
    "@opentelemetry/semantic-conventions" "1.28.0"

"@opentelemetry/core@^2.0.0", "@opentelemetry/core@2.0.1":
  version "2.0.1"
  resolved "https://registry.npmjs.org/@opentelemetry/core/-/core-2.0.1.tgz"
  integrity sha512-MaZk9SJIDgo1peKevlbhP6+IwIiNPNmswNL4AF0WaQJLbHXjr9SrZMgS12+iqr9ToV4ZVosCcc0f8Rg67LXjxw==
  dependencies:
    "@opentelemetry/semantic-conventions" "^1.29.0"

"@opentelemetry/exporter-logs-otlp-grpc@0.202.0":
  version "0.202.0"
  resolved "https://registry.npmjs.org/@opentelemetry/exporter-logs-otlp-grpc/-/exporter-logs-otlp-grpc-0.202.0.tgz"
  integrity sha512-Y84L8Yja/A2qjGEzC/To0yrMUXHrtwJzHtZ2za1/ulZplRe5QFsLNyHixIS42ZYUKuNyWMDgOFhnN2Pz5uThtg==
  dependencies:
    "@grpc/grpc-js" "^1.7.1"
    "@opentelemetry/core" "2.0.1"
    "@opentelemetry/otlp-exporter-base" "0.202.0"
    "@opentelemetry/otlp-grpc-exporter-base" "0.202.0"
    "@opentelemetry/otlp-transformer" "0.202.0"
    "@opentelemetry/sdk-logs" "0.202.0"

"@opentelemetry/exporter-logs-otlp-http@^0.202.0", "@opentelemetry/exporter-logs-otlp-http@0.202.0":
  version "0.202.0"
  resolved "https://registry.npmjs.org/@opentelemetry/exporter-logs-otlp-http/-/exporter-logs-otlp-http-0.202.0.tgz"
  integrity sha512-mJWLkmoG+3r+SsYQC+sbWoy1rjowJhMhFvFULeIPTxSI+EZzKPya0+NZ3+vhhgx2UTybGQlye3FBtCH3o6Rejg==
  dependencies:
    "@opentelemetry/api-logs" "0.202.0"
    "@opentelemetry/core" "2.0.1"
    "@opentelemetry/otlp-exporter-base" "0.202.0"
    "@opentelemetry/otlp-transformer" "0.202.0"
    "@opentelemetry/sdk-logs" "0.202.0"

"@opentelemetry/exporter-logs-otlp-proto@0.202.0":
  version "0.202.0"
  resolved "https://registry.npmjs.org/@opentelemetry/exporter-logs-otlp-proto/-/exporter-logs-otlp-proto-0.202.0.tgz"
  integrity sha512-qYwbmNWPkP7AbzX8o4DRu5bb/a0TWYNcpZc1NEAOhuV7pgBpAUPEClxRWPN94ulIia+PfQjzFGMaRwmLGmNP6g==
  dependencies:
    "@opentelemetry/api-logs" "0.202.0"
    "@opentelemetry/core" "2.0.1"
    "@opentelemetry/otlp-exporter-base" "0.202.0"
    "@opentelemetry/otlp-transformer" "0.202.0"
    "@opentelemetry/resources" "2.0.1"
    "@opentelemetry/sdk-logs" "0.202.0"
    "@opentelemetry/sdk-trace-base" "2.0.1"

"@opentelemetry/exporter-metrics-otlp-grpc@0.202.0":
  version "0.202.0"
  resolved "https://registry.npmjs.org/@opentelemetry/exporter-metrics-otlp-grpc/-/exporter-metrics-otlp-grpc-0.202.0.tgz"
  integrity sha512-/dq/rf4KCkTYoP+NyPXTE+5wjvfhAHSqK62vRsJ/IalG61VPQvwaL18yWcavbI+44ImQwtMeZxfIJSox7oQL0w==
  dependencies:
    "@grpc/grpc-js" "^1.7.1"
    "@opentelemetry/core" "2.0.1"
    "@opentelemetry/exporter-metrics-otlp-http" "0.202.0"
    "@opentelemetry/otlp-exporter-base" "0.202.0"
    "@opentelemetry/otlp-grpc-exporter-base" "0.202.0"
    "@opentelemetry/otlp-transformer" "0.202.0"
    "@opentelemetry/resources" "2.0.1"
    "@opentelemetry/sdk-metrics" "2.0.1"

"@opentelemetry/exporter-metrics-otlp-http@^0.202.0", "@opentelemetry/exporter-metrics-otlp-http@0.202.0":
  version "0.202.0"
  resolved "https://registry.npmjs.org/@opentelemetry/exporter-metrics-otlp-http/-/exporter-metrics-otlp-http-0.202.0.tgz"
  integrity sha512-ooYcrf/m9ZuVGpQnER7WRH+JZbDPD389HG7VS/EnvIEF5WpNYEqf+NdmtaAcs51d81QrytTYAubc5bVWi//28w==
  dependencies:
    "@opentelemetry/core" "2.0.1"
    "@opentelemetry/otlp-exporter-base" "0.202.0"
    "@opentelemetry/otlp-transformer" "0.202.0"
    "@opentelemetry/resources" "2.0.1"
    "@opentelemetry/sdk-metrics" "2.0.1"

"@opentelemetry/exporter-metrics-otlp-proto@0.202.0":
  version "0.202.0"
  resolved "https://registry.npmjs.org/@opentelemetry/exporter-metrics-otlp-proto/-/exporter-metrics-otlp-proto-0.202.0.tgz"
  integrity sha512-X0RpPpPjyCAmIq9tySZm0Hk3Ltw8KWsqeNq5I7gS9AR9RzbVHb/l+eiMI1CqSRvW9R47HXcUu/epmEzY8ebFAg==
  dependencies:
    "@opentelemetry/core" "2.0.1"
    "@opentelemetry/exporter-metrics-otlp-http" "0.202.0"
    "@opentelemetry/otlp-exporter-base" "0.202.0"
    "@opentelemetry/otlp-transformer" "0.202.0"
    "@opentelemetry/resources" "2.0.1"
    "@opentelemetry/sdk-metrics" "2.0.1"

"@opentelemetry/exporter-prometheus@0.202.0":
  version "0.202.0"
  resolved "https://registry.npmjs.org/@opentelemetry/exporter-prometheus/-/exporter-prometheus-0.202.0.tgz"
  integrity sha512-6RvQqZHAPFiwL1OKRJe4ta6SgJx/g8or41B+OovVVEie3HeCDhDGL9S1VJNkBozUz6wTY8a47fQwdMrCOUdMhQ==
  dependencies:
    "@opentelemetry/core" "2.0.1"
    "@opentelemetry/resources" "2.0.1"
    "@opentelemetry/sdk-metrics" "2.0.1"

"@opentelemetry/exporter-trace-otlp-grpc@0.202.0":
  version "0.202.0"
  resolved "https://registry.npmjs.org/@opentelemetry/exporter-trace-otlp-grpc/-/exporter-trace-otlp-grpc-0.202.0.tgz"
  integrity sha512-d5wLdbNA3ahpSeD0I34vbDFMTh4vPsXemH0bKDXLeCVULCAjOJXuZmEiuRammiDgVvvX7CAb/IGLDz8d2QHvoA==
  dependencies:
    "@grpc/grpc-js" "^1.7.1"
    "@opentelemetry/core" "2.0.1"
    "@opentelemetry/otlp-exporter-base" "0.202.0"
    "@opentelemetry/otlp-grpc-exporter-base" "0.202.0"
    "@opentelemetry/otlp-transformer" "0.202.0"
    "@opentelemetry/resources" "2.0.1"
    "@opentelemetry/sdk-trace-base" "2.0.1"

"@opentelemetry/exporter-trace-otlp-http@^0.202.0", "@opentelemetry/exporter-trace-otlp-http@~0.202.0", "@opentelemetry/exporter-trace-otlp-http@0.202.0":
  version "0.202.0"
  resolved "https://registry.npmjs.org/@opentelemetry/exporter-trace-otlp-http/-/exporter-trace-otlp-http-0.202.0.tgz"
  integrity sha512-/hKE8DaFCJuaQqE1IxpgkcjOolUIwgi3TgHElPVKGdGRBSmJMTmN/cr6vWa55pCJIXPyhKvcMrbrya7DZ3VmzA==
  dependencies:
    "@opentelemetry/core" "2.0.1"
    "@opentelemetry/otlp-exporter-base" "0.202.0"
    "@opentelemetry/otlp-transformer" "0.202.0"
    "@opentelemetry/resources" "2.0.1"
    "@opentelemetry/sdk-trace-base" "2.0.1"

"@opentelemetry/exporter-trace-otlp-proto@0.202.0":
  version "0.202.0"
  resolved "https://registry.npmjs.org/@opentelemetry/exporter-trace-otlp-proto/-/exporter-trace-otlp-proto-0.202.0.tgz"
  integrity sha512-z3vzdMclCETGIn8uUBgpz7w651ftCiH2qh3cewhBk+rF0EYPNQ3mJvyxktLnKIBZ/ci0zUknAzzYC7LIIZmggQ==
  dependencies:
    "@opentelemetry/core" "2.0.1"
    "@opentelemetry/otlp-exporter-base" "0.202.0"
    "@opentelemetry/otlp-transformer" "0.202.0"
    "@opentelemetry/resources" "2.0.1"
    "@opentelemetry/sdk-trace-base" "2.0.1"

"@opentelemetry/exporter-zipkin@2.0.1":
  version "2.0.1"
  resolved "https://registry.npmjs.org/@opentelemetry/exporter-zipkin/-/exporter-zipkin-2.0.1.tgz"
  integrity sha512-a9eeyHIipfdxzCfc2XPrE+/TI3wmrZUDFtG2RRXHSbZZULAny7SyybSvaDvS77a7iib5MPiAvluwVvbGTsHxsw==
  dependencies:
    "@opentelemetry/core" "2.0.1"
    "@opentelemetry/resources" "2.0.1"
    "@opentelemetry/sdk-trace-base" "2.0.1"
    "@opentelemetry/semantic-conventions" "^1.29.0"

"@opentelemetry/instrumentation-amqplib@^0.46.1":
  version "0.46.1"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-amqplib/-/instrumentation-amqplib-0.46.1.tgz"
  integrity sha512-AyXVnlCf/xV3K/rNumzKxZqsULyITJH6OVLiW6730JPRqWA7Zc9bvYoVNpN6iOpTU8CasH34SU/ksVJmObFibQ==
  dependencies:
    "@opentelemetry/core" "^1.8.0"
    "@opentelemetry/instrumentation" "^0.57.1"
    "@opentelemetry/semantic-conventions" "^1.27.0"

"@opentelemetry/instrumentation-amqplib@^0.49.0":
  version "0.49.0"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-amqplib/-/instrumentation-amqplib-0.49.0.tgz"
  integrity sha512-OCGkE+1JoUN+gOzs3u0GSa7GV//KX6NMKzaPchedae7ZwFVyyBQ8VECJngHgW3k/FLABFnq9Oiym2WZGiWugVQ==
  dependencies:
    "@opentelemetry/core" "^2.0.0"
    "@opentelemetry/instrumentation" "^0.202.0"
    "@opentelemetry/semantic-conventions" "^1.27.0"

"@opentelemetry/instrumentation-aws-lambda@^0.53.0":
  version "0.53.0"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-aws-lambda/-/instrumentation-aws-lambda-0.53.0.tgz"
  integrity sha512-dZywDIc4t7o28eU9W4QMB+mNhRdH5/kVxVmxRtB46/diHg8Im6RFncuiCVJ1l9ig/RUtwR3dU9LX1huFBwxkPw==
  dependencies:
    "@opentelemetry/instrumentation" "^0.202.0"
    "@opentelemetry/semantic-conventions" "^1.27.0"
    "@types/aws-lambda" "8.10.147"

"@opentelemetry/instrumentation-aws-sdk@^0.54.0":
  version "0.54.0"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-aws-sdk/-/instrumentation-aws-sdk-0.54.0.tgz"
  integrity sha512-4XnXfpACX8fpOnt/D8d/1AFg3uOwBTG9TopQBuikDZJYUrLUSdT7UiotCFqAM/Z6hQJh72Jy3591C/OrmKct7A==
  dependencies:
    "@opentelemetry/core" "^2.0.0"
    "@opentelemetry/instrumentation" "^0.202.0"
    "@opentelemetry/propagation-utils" "^0.31.2"
    "@opentelemetry/semantic-conventions" "^1.31.0"

"@opentelemetry/instrumentation-bunyan@^0.48.0":
  version "0.48.0"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-bunyan/-/instrumentation-bunyan-0.48.0.tgz"
  integrity sha512-Q6ay5CXIKuyejadPoLboz+jKumB3Zuxyk35ycFh9vfIeww3+mNRyMVj6KxHRS0Imbv9zhNbP3uyrUpvEMMyHuw==
  dependencies:
    "@opentelemetry/api-logs" "^0.202.0"
    "@opentelemetry/instrumentation" "^0.202.0"
    "@types/bunyan" "1.8.11"

"@opentelemetry/instrumentation-cassandra-driver@^0.48.0":
  version "0.48.0"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-cassandra-driver/-/instrumentation-cassandra-driver-0.48.0.tgz"
  integrity sha512-0dcX8Kx0S6ZAOknrbA+BBh1j5lg5F20W18m5VYoGUxkuLIUbWkQA3uaqeTfqbOwmnBmb1upDPUWPR+g5N12B4Q==
  dependencies:
    "@opentelemetry/instrumentation" "^0.202.0"
    "@opentelemetry/semantic-conventions" "^1.27.0"

"@opentelemetry/instrumentation-connect@^0.46.0":
  version "0.46.0"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-connect/-/instrumentation-connect-0.46.0.tgz"
  integrity sha512-YNq/7M1JXnWRkpKPC9dbYZA36cg547gY0p1bijW7vuZJ9t5f3alo6w8TWtZwV/hOFtBGHDXVhKVfp2Mh6zVHjQ==
  dependencies:
    "@opentelemetry/core" "^2.0.0"
    "@opentelemetry/instrumentation" "^0.202.0"
    "@opentelemetry/semantic-conventions" "^1.27.0"
    "@types/connect" "3.4.38"

"@opentelemetry/instrumentation-connect@0.43.1":
  version "0.43.1"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-connect/-/instrumentation-connect-0.43.1.tgz"
  integrity sha512-ht7YGWQuV5BopMcw5Q2hXn3I8eG8TH0J/kc/GMcW4CuNTgiP6wCu44BOnucJWL3CmFWaRHI//vWyAhaC8BwePw==
  dependencies:
    "@opentelemetry/core" "^1.8.0"
    "@opentelemetry/instrumentation" "^0.57.1"
    "@opentelemetry/semantic-conventions" "^1.27.0"
    "@types/connect" "3.4.38"

"@opentelemetry/instrumentation-cucumber@^0.17.0":
  version "0.17.0"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-cucumber/-/instrumentation-cucumber-0.17.0.tgz"
  integrity sha512-TTfQ9DmUlbeBsYZjNdJqs8mlcn1uY3t/AsTsALDBEFg6tWV+S1ADM9kVmKnscfbCwcQX2x17f/6a1Kpq5p91ww==
  dependencies:
    "@opentelemetry/instrumentation" "^0.202.0"
    "@opentelemetry/semantic-conventions" "^1.27.0"

"@opentelemetry/instrumentation-dataloader@^0.19.0":
  version "0.19.0"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-dataloader/-/instrumentation-dataloader-0.19.0.tgz"
  integrity sha512-zIVRnRs3zDZCqStQcpIdRx3Dz9WXFSVj9qimqI7CRuKao9qnrZYUVQHvvVlLZX3JAg+nDC6JRS95zvbq50hj4A==
  dependencies:
    "@opentelemetry/instrumentation" "^0.202.0"

"@opentelemetry/instrumentation-dataloader@0.16.1":
  version "0.16.1"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-dataloader/-/instrumentation-dataloader-0.16.1.tgz"
  integrity sha512-K/qU4CjnzOpNkkKO4DfCLSQshejRNAJtd4esgigo/50nxCB6XCyi1dhAblUHM9jG5dRm8eu0FB+t87nIo99LYQ==
  dependencies:
    "@opentelemetry/instrumentation" "^0.57.1"

"@opentelemetry/instrumentation-dns@^0.46.0":
  version "0.46.0"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-dns/-/instrumentation-dns-0.46.0.tgz"
  integrity sha512-m8u72x2fSIjhP1ITJX9Ims3eR4Qn8ze+QWy9NHYO01JlmiMamoc9TfIOd4dyOtxVja4tjnkWceKQdlEH9F9BoA==
  dependencies:
    "@opentelemetry/instrumentation" "^0.202.0"

"@opentelemetry/instrumentation-document-load@^0.47.0":
  version "0.47.0"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-document-load/-/instrumentation-document-load-0.47.0.tgz"
  integrity sha512-CkxA85nMvieX4IWIW7oTTXChHBuBzZ2NV+PyD9cLuzNZgrdks7YzYwCBhsyDVN6ptZ3Wd0S8Ys2Pfr9YgFqoDQ==
  dependencies:
    "@opentelemetry/core" "^2.0.0"
    "@opentelemetry/instrumentation" "^0.202.0"
    "@opentelemetry/sdk-trace-web" "^2.0.0"
    "@opentelemetry/semantic-conventions" "^1.27.0"

"@opentelemetry/instrumentation-express@^0.51.0":
  version "0.51.0"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-express/-/instrumentation-express-0.51.0.tgz"
  integrity sha512-v1mgfvyeQh7yfsZ8wZlr+jgFGk9FxzLfNH0EH0UYGO9das8fCIkixsEasZMWhjwAJKjlf+ElTZ2jE2pT7I3DyQ==
  dependencies:
    "@opentelemetry/core" "^2.0.0"
    "@opentelemetry/instrumentation" "^0.202.0"
    "@opentelemetry/semantic-conventions" "^1.27.0"

"@opentelemetry/instrumentation-express@0.47.1":
  version "0.47.1"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-express/-/instrumentation-express-0.47.1.tgz"
  integrity sha512-QNXPTWteDclR2B4pDFpz0TNghgB33UMjUt14B+BZPmtH1MwUFAfLHBaP5If0Z5NZC+jaH8oF2glgYjrmhZWmSw==
  dependencies:
    "@opentelemetry/core" "^1.8.0"
    "@opentelemetry/instrumentation" "^0.57.1"
    "@opentelemetry/semantic-conventions" "^1.27.0"

"@opentelemetry/instrumentation-fastify@^0.47.0":
  version "0.47.0"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-fastify/-/instrumentation-fastify-0.47.0.tgz"
  integrity sha512-dLld0pI63WR1BXvNiGKFWzqrnhgItiIDNsRf/vVOhKV20HQNUQk5FfzcX0eUyiJtW/+u95Txh/vdfeQRwLELcA==
  dependencies:
    "@opentelemetry/core" "^2.0.0"
    "@opentelemetry/instrumentation" "^0.202.0"
    "@opentelemetry/semantic-conventions" "^1.27.0"

"@opentelemetry/instrumentation-fastify@0.44.2":
  version "0.44.2"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-fastify/-/instrumentation-fastify-0.44.2.tgz"
  integrity sha512-arSp97Y4D2NWogoXRb8CzFK3W2ooVdvqRRtQDljFt9uC3zI6OuShgey6CVFC0JxT1iGjkAr1r4PDz23mWrFULQ==
  dependencies:
    "@opentelemetry/core" "^1.8.0"
    "@opentelemetry/instrumentation" "^0.57.1"
    "@opentelemetry/semantic-conventions" "^1.27.0"

"@opentelemetry/instrumentation-fetch@^0.202.0":
  version "0.202.0"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-fetch/-/instrumentation-fetch-0.202.0.tgz"
  integrity sha512-RlLgOJAKs9cQIRXPoLnS6YG8CeQt1gR+WJpzthQlqt4hdgNmfnyB7zZrg1yddECF0K2lPGBqF4s+IqjA4dy3JQ==
  dependencies:
    "@opentelemetry/core" "2.0.1"
    "@opentelemetry/instrumentation" "0.202.0"
    "@opentelemetry/sdk-trace-web" "2.0.1"
    "@opentelemetry/semantic-conventions" "^1.29.0"

"@opentelemetry/instrumentation-fs@^0.22.0":
  version "0.22.0"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-fs/-/instrumentation-fs-0.22.0.tgz"
  integrity sha512-ktQVFD6pd8eAIW6t2DtDuXj2lxq+wnQ8WUkJLNZzl3rEE2TZEiHg7wIkWVoxl4Cz4pJ2YZJbdU2fHAizuDebDw==
  dependencies:
    "@opentelemetry/core" "^2.0.0"
    "@opentelemetry/instrumentation" "^0.202.0"

"@opentelemetry/instrumentation-fs@0.19.1":
  version "0.19.1"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-fs/-/instrumentation-fs-0.19.1.tgz"
  integrity sha512-6g0FhB3B9UobAR60BGTcXg4IHZ6aaYJzp0Ki5FhnxyAPt8Ns+9SSvgcrnsN2eGmk3RWG5vYycUGOEApycQL24A==
  dependencies:
    "@opentelemetry/core" "^1.8.0"
    "@opentelemetry/instrumentation" "^0.57.1"

"@opentelemetry/instrumentation-generic-pool@^0.46.0":
  version "0.46.0"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-generic-pool/-/instrumentation-generic-pool-0.46.0.tgz"
  integrity sha512-QJUH9n5Ld0xz54gX1k3L2RDoSyJjeZaASA17Zvm0uVa40v+s8oMfCa1/4y9TONFSVbL0fPbAGojVsRRtg6dJ5w==
  dependencies:
    "@opentelemetry/instrumentation" "^0.202.0"

"@opentelemetry/instrumentation-generic-pool@0.43.1":
  version "0.43.1"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-generic-pool/-/instrumentation-generic-pool-0.43.1.tgz"
  integrity sha512-M6qGYsp1cURtvVLGDrPPZemMFEbuMmCXgQYTReC/IbimV5sGrLBjB+/hANUpRZjX67nGLdKSVLZuQQAiNz+sww==
  dependencies:
    "@opentelemetry/instrumentation" "^0.57.1"

"@opentelemetry/instrumentation-graphql@^0.50.0":
  version "0.50.0"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-graphql/-/instrumentation-graphql-0.50.0.tgz"
  integrity sha512-Nn3vBS5T0Dv4+9WF1dGR0Lgsxuz6ztQmTsxoHvesm6YAAXiHffnwsxBEJUKEJcjxfXzjO1SVuLDkv1bAeQ3NFw==
  dependencies:
    "@opentelemetry/instrumentation" "^0.202.0"

"@opentelemetry/instrumentation-graphql@0.47.1":
  version "0.47.1"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-graphql/-/instrumentation-graphql-0.47.1.tgz"
  integrity sha512-EGQRWMGqwiuVma8ZLAZnExQ7sBvbOx0N/AE/nlafISPs8S+QtXX+Viy6dcQwVWwYHQPAcuY3bFt3xgoAwb4ZNQ==
  dependencies:
    "@opentelemetry/instrumentation" "^0.57.1"

"@opentelemetry/instrumentation-grpc@^0.202.0":
  version "0.202.0"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-grpc/-/instrumentation-grpc-0.202.0.tgz"
  integrity sha512-dWvefHNAyAfaHVmxQ/ySLQSI2hGKLgK1sBtvae4w9xruqU08bBMtvmVeGMA/5whfiUDU8ftp1/84U4Zoe5N56A==
  dependencies:
    "@opentelemetry/instrumentation" "0.202.0"
    "@opentelemetry/semantic-conventions" "^1.29.0"

"@opentelemetry/instrumentation-hapi@^0.49.0":
  version "0.49.0"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-hapi/-/instrumentation-hapi-0.49.0.tgz"
  integrity sha512-d4BcCjbW7Pfg4FpbAAF0cK/ue3dN02WMw0uO2G792KzDjxj05MtZm3eBTz672j3ejV9hM0HvPPhUHUsIC0H6Gw==
  dependencies:
    "@opentelemetry/core" "^2.0.0"
    "@opentelemetry/instrumentation" "^0.202.0"
    "@opentelemetry/semantic-conventions" "^1.27.0"

"@opentelemetry/instrumentation-hapi@0.45.2":
  version "0.45.2"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-hapi/-/instrumentation-hapi-0.45.2.tgz"
  integrity sha512-7Ehow/7Wp3aoyCrZwQpU7a2CnoMq0XhIcioFuKjBb0PLYfBfmTsFTUyatlHu0fRxhwcRsSQRTvEhmZu8CppBpQ==
  dependencies:
    "@opentelemetry/core" "^1.8.0"
    "@opentelemetry/instrumentation" "^0.57.1"
    "@opentelemetry/semantic-conventions" "^1.27.0"

"@opentelemetry/instrumentation-http@^0.202.0":
  version "0.202.0"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-http/-/instrumentation-http-0.202.0.tgz"
  integrity sha512-oX+jyY2KBg4/nVH3vZhSWDbhywkHgE0fq3YinhUBx0jv+YUWC2UKA7qLkxr/CSzfKsFi/Km0NKV+llH17yYGKw==
  dependencies:
    "@opentelemetry/core" "2.0.1"
    "@opentelemetry/instrumentation" "0.202.0"
    "@opentelemetry/semantic-conventions" "^1.29.0"
    forwarded-parse "2.1.2"

"@opentelemetry/instrumentation-http@0.57.2":
  version "0.57.2"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-http/-/instrumentation-http-0.57.2.tgz"
  integrity sha512-1Uz5iJ9ZAlFOiPuwYg29Bf7bJJc/GeoeJIFKJYQf67nTVKFe8RHbEtxgkOmK4UGZNHKXcpW4P8cWBYzBn1USpg==
  dependencies:
    "@opentelemetry/core" "1.30.1"
    "@opentelemetry/instrumentation" "0.57.2"
    "@opentelemetry/semantic-conventions" "1.28.0"
    forwarded-parse "2.1.2"
    semver "^7.5.2"

"@opentelemetry/instrumentation-ioredis@^0.50.0":
  version "0.50.0"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-ioredis/-/instrumentation-ioredis-0.50.0.tgz"
  integrity sha512-f2e+3xPxMRdlt1rjZpRhxuqrfumlWe3NX0Y+W857RBBV11HhbeZZaYbO5MMaxV3xBZv4dwPSGx96GjExUWY0WA==
  dependencies:
    "@opentelemetry/instrumentation" "^0.202.0"
    "@opentelemetry/redis-common" "^0.37.0"
    "@opentelemetry/semantic-conventions" "^1.27.0"

"@opentelemetry/instrumentation-ioredis@0.47.1":
  version "0.47.1"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-ioredis/-/instrumentation-ioredis-0.47.1.tgz"
  integrity sha512-OtFGSN+kgk/aoKgdkKQnBsQFDiG8WdCxu+UrHr0bXScdAmtSzLSraLo7wFIb25RVHfRWvzI5kZomqJYEg/l1iA==
  dependencies:
    "@opentelemetry/instrumentation" "^0.57.1"
    "@opentelemetry/redis-common" "^0.36.2"
    "@opentelemetry/semantic-conventions" "^1.27.0"

"@opentelemetry/instrumentation-kafkajs@^0.11.0":
  version "0.11.0"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-kafkajs/-/instrumentation-kafkajs-0.11.0.tgz"
  integrity sha512-+i9VqVEPNObB1tkwcLV6zAafnve72h2Iwo48E11M/kVXMNXlgGhiYckYCmzba8c2u5XD/V98XZDrCIyO8CLCNA==
  dependencies:
    "@opentelemetry/instrumentation" "^0.202.0"
    "@opentelemetry/semantic-conventions" "^1.30.0"

"@opentelemetry/instrumentation-kafkajs@0.7.1":
  version "0.7.1"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-kafkajs/-/instrumentation-kafkajs-0.7.1.tgz"
  integrity sha512-OtjaKs8H7oysfErajdYr1yuWSjMAectT7Dwr+axIoZqT9lmEOkD/H/3rgAs8h/NIuEi2imSXD+vL4MZtOuJfqQ==
  dependencies:
    "@opentelemetry/instrumentation" "^0.57.1"
    "@opentelemetry/semantic-conventions" "^1.27.0"

"@opentelemetry/instrumentation-knex@^0.47.0":
  version "0.47.0"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-knex/-/instrumentation-knex-0.47.0.tgz"
  integrity sha512-OjqjnzXD5+FXVGkOznbRAz9yByb4UWzIUhXjuHvOQ50IUY8mv3rM2Gj6Ar7m5JsENiS5DtAy2Vfwk4e9zNC0ng==
  dependencies:
    "@opentelemetry/instrumentation" "^0.202.0"
    "@opentelemetry/semantic-conventions" "^1.33.1"

"@opentelemetry/instrumentation-knex@0.44.1":
  version "0.44.1"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-knex/-/instrumentation-knex-0.44.1.tgz"
  integrity sha512-U4dQxkNhvPexffjEmGwCq68FuftFK15JgUF05y/HlK3M6W/G2iEaACIfXdSnwVNe9Qh0sPfw8LbOPxrWzGWGMQ==
  dependencies:
    "@opentelemetry/instrumentation" "^0.57.1"
    "@opentelemetry/semantic-conventions" "^1.27.0"

"@opentelemetry/instrumentation-koa@^0.50.1":
  version "0.50.1"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-koa/-/instrumentation-koa-0.50.1.tgz"
  integrity sha512-HoQ9OuzLx4z6/BfA4medM6cj5+UXWQWakQVCd/Xd+gU+gA1eCxwdoECH44p+mTl3GFS7/icgfGE1if/lguaG0Q==
  dependencies:
    "@opentelemetry/core" "^2.0.0"
    "@opentelemetry/instrumentation" "^0.202.0"
    "@opentelemetry/semantic-conventions" "^1.27.0"

"@opentelemetry/instrumentation-koa@0.47.1":
  version "0.47.1"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-koa/-/instrumentation-koa-0.47.1.tgz"
  integrity sha512-l/c+Z9F86cOiPJUllUCt09v+kICKvT+Vg1vOAJHtHPsJIzurGayucfCMq2acd/A/yxeNWunl9d9eqZ0G+XiI6A==
  dependencies:
    "@opentelemetry/core" "^1.8.0"
    "@opentelemetry/instrumentation" "^0.57.1"
    "@opentelemetry/semantic-conventions" "^1.27.0"

"@opentelemetry/instrumentation-lru-memoizer@^0.47.0":
  version "0.47.0"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-lru-memoizer/-/instrumentation-lru-memoizer-0.47.0.tgz"
  integrity sha512-UJ2UlCAIF+N4zNkiHdMr4O0caN0K6YboAso3/zaFdG1QiPR2zqZcbWAGFBikZ9HSByU+NwbxTXDzlpkcDZIqWg==
  dependencies:
    "@opentelemetry/instrumentation" "^0.202.0"

"@opentelemetry/instrumentation-lru-memoizer@0.44.1":
  version "0.44.1"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-lru-memoizer/-/instrumentation-lru-memoizer-0.44.1.tgz"
  integrity sha512-5MPkYCvG2yw7WONEjYj5lr5JFehTobW7wX+ZUFy81oF2lr9IPfZk9qO+FTaM0bGEiymwfLwKe6jE15nHn1nmHg==
  dependencies:
    "@opentelemetry/instrumentation" "^0.57.1"

"@opentelemetry/instrumentation-memcached@^0.46.0":
  version "0.46.0"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-memcached/-/instrumentation-memcached-0.46.0.tgz"
  integrity sha512-FFDcOVJUxZQqbg57gVskZGXRfEsZXwOvCaPv6/qIZRw5glLXPTulpnfG/s8NAltsj2buXSvS4eKFo+0HKH0apw==
  dependencies:
    "@opentelemetry/instrumentation" "^0.202.0"
    "@opentelemetry/semantic-conventions" "^1.27.0"
    "@types/memcached" "^2.2.6"

"@opentelemetry/instrumentation-mongodb@^0.55.1":
  version "0.55.1"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-mongodb/-/instrumentation-mongodb-0.55.1.tgz"
  integrity sha512-Wb13YixWm8nB27ZSQW3h070UWkivoh6bjeyDUY6lLimSUulALr+YHBn0t71U1aTcUeaZv3IBNaPRimFXhz6gBA==
  dependencies:
    "@opentelemetry/instrumentation" "^0.202.0"
    "@opentelemetry/semantic-conventions" "^1.27.0"

"@opentelemetry/instrumentation-mongodb@0.52.0":
  version "0.52.0"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-mongodb/-/instrumentation-mongodb-0.52.0.tgz"
  integrity sha512-1xmAqOtRUQGR7QfJFfGV/M2kC7wmI2WgZdpru8hJl3S0r4hW0n3OQpEHlSGXJAaNFyvT+ilnwkT+g5L4ljHR6g==
  dependencies:
    "@opentelemetry/instrumentation" "^0.57.1"
    "@opentelemetry/semantic-conventions" "^1.27.0"

"@opentelemetry/instrumentation-mongoose@^0.49.0":
  version "0.49.0"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-mongoose/-/instrumentation-mongoose-0.49.0.tgz"
  integrity sha512-nF+43QFe8IoW20TmTJZdxZhnVZGEglODUvzAo3fRmaBFAkwUXRGzRgABS255PCjIbScEaRRDCXc6EAsSkwRNPg==
  dependencies:
    "@opentelemetry/core" "^2.0.0"
    "@opentelemetry/instrumentation" "^0.202.0"
    "@opentelemetry/semantic-conventions" "^1.27.0"

"@opentelemetry/instrumentation-mongoose@0.46.1":
  version "0.46.1"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-mongoose/-/instrumentation-mongoose-0.46.1.tgz"
  integrity sha512-3kINtW1LUTPkiXFRSSBmva1SXzS/72we/jL22N+BnF3DFcoewkdkHPYOIdAAk9gSicJ4d5Ojtt1/HeibEc5OQg==
  dependencies:
    "@opentelemetry/core" "^1.8.0"
    "@opentelemetry/instrumentation" "^0.57.1"
    "@opentelemetry/semantic-conventions" "^1.27.0"

"@opentelemetry/instrumentation-mysql@^0.48.0":
  version "0.48.0"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-mysql/-/instrumentation-mysql-0.48.0.tgz"
  integrity sha512-o7DwkkRn3eLWfzJdbXrlCS1EhbIOgB0W74eucbP+5Lk0XDGixy4yURTkmNclCcsemgzRZfEq0YvYQV29Yhpo5A==
  dependencies:
    "@opentelemetry/instrumentation" "^0.202.0"
    "@opentelemetry/semantic-conventions" "^1.27.0"
    "@types/mysql" "2.15.26"

"@opentelemetry/instrumentation-mysql@0.45.1":
  version "0.45.1"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-mysql/-/instrumentation-mysql-0.45.1.tgz"
  integrity sha512-TKp4hQ8iKQsY7vnp/j0yJJ4ZsP109Ht6l4RHTj0lNEG1TfgTrIH5vJMbgmoYXWzNHAqBH2e7fncN12p3BP8LFg==
  dependencies:
    "@opentelemetry/instrumentation" "^0.57.1"
    "@opentelemetry/semantic-conventions" "^1.27.0"
    "@types/mysql" "2.15.26"

"@opentelemetry/instrumentation-mysql2@^0.48.0":
  version "0.48.0"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-mysql2/-/instrumentation-mysql2-0.48.0.tgz"
  integrity sha512-eCRpv0WV2s0Pa6CpjPWzZiLZDqx8kqZJopJESd4ywoUwtijXzBiTRidp/8aL9k+kl4drhm2GVNr4thUCMlEOSA==
  dependencies:
    "@opentelemetry/instrumentation" "^0.202.0"
    "@opentelemetry/semantic-conventions" "^1.27.0"
    "@opentelemetry/sql-common" "^0.41.0"

"@opentelemetry/instrumentation-mysql2@0.45.2":
  version "0.45.2"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-mysql2/-/instrumentation-mysql2-0.45.2.tgz"
  integrity sha512-h6Ad60FjCYdJZ5DTz1Lk2VmQsShiViKe0G7sYikb0GHI0NVvApp2XQNRHNjEMz87roFttGPLHOYVPlfy+yVIhQ==
  dependencies:
    "@opentelemetry/instrumentation" "^0.57.1"
    "@opentelemetry/semantic-conventions" "^1.27.0"
    "@opentelemetry/sql-common" "^0.40.1"

"@opentelemetry/instrumentation-nestjs-core@^0.48.0":
  version "0.48.0"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-nestjs-core/-/instrumentation-nestjs-core-0.48.0.tgz"
  integrity sha512-ytK4ABSkWcD9vyMU8GpinvodAGaRxBFuxybP/m7sgLtEboXMJjdWnEHb7lH/CX1ICiVKRXWdYg9npdu6yBCW5Q==
  dependencies:
    "@opentelemetry/instrumentation" "^0.202.0"
    "@opentelemetry/semantic-conventions" "^1.30.0"

"@opentelemetry/instrumentation-net@^0.46.1":
  version "0.46.1"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-net/-/instrumentation-net-0.46.1.tgz"
  integrity sha512-r7Buqem+odrTTPlWfT7EqS24QnDAL4U+c4e38RzcRtdZF00Z34oqEpge7TZcQLo0vEASWbHQ/WjWNR7ZYKFKBA==
  dependencies:
    "@opentelemetry/instrumentation" "^0.202.0"
    "@opentelemetry/semantic-conventions" "^1.27.0"

"@opentelemetry/instrumentation-oracledb@^0.28.0":
  version "0.28.0"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-oracledb/-/instrumentation-oracledb-0.28.0.tgz"
  integrity sha512-VObbQRd3g8nDLLOeGjm5l6TnB9dtEaJoedLfLwMGrlD6lkai+hdfalYh6FOF5dce+dJouZdW6NUUAaBj4f4KcA==
  dependencies:
    "@opentelemetry/instrumentation" "^0.202.0"
    "@opentelemetry/semantic-conventions" "^1.27.0"
    "@types/oracledb" "6.5.2"

"@opentelemetry/instrumentation-pg@^0.54.0":
  version "0.54.0"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-pg/-/instrumentation-pg-0.54.0.tgz"
  integrity sha512-KQnEGwm65p1zFZGjKGw+oMilGcR4l1q3qgRmETO7ySEfMddH3t6jwlbqmcjO3N3bVcPkYgjioGVQGvdpvz7O1w==
  dependencies:
    "@opentelemetry/core" "^2.0.0"
    "@opentelemetry/instrumentation" "^0.202.0"
    "@opentelemetry/semantic-conventions" "^1.27.0"
    "@opentelemetry/sql-common" "^0.41.0"
    "@types/pg" "8.15.1"
    "@types/pg-pool" "2.0.6"

"@opentelemetry/instrumentation-pg@0.51.1":
  version "0.51.1"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-pg/-/instrumentation-pg-0.51.1.tgz"
  integrity sha512-QxgjSrxyWZc7Vk+qGSfsejPVFL1AgAJdSBMYZdDUbwg730D09ub3PXScB9d04vIqPriZ+0dqzjmQx0yWKiCi2Q==
  dependencies:
    "@opentelemetry/core" "^1.26.0"
    "@opentelemetry/instrumentation" "^0.57.1"
    "@opentelemetry/semantic-conventions" "^1.27.0"
    "@opentelemetry/sql-common" "^0.40.1"
    "@types/pg" "8.6.1"
    "@types/pg-pool" "2.0.6"

"@opentelemetry/instrumentation-pino@^0.49.0":
  version "0.49.0"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-pino/-/instrumentation-pino-0.49.0.tgz"
  integrity sha512-nngcqUnIeVnDvRMf6fixYwlMbTNzCVGv93CacyR/8TL/pjyumje020PC5q7b6CfcTdToiD5GMTMKvWBiTd08cA==
  dependencies:
    "@opentelemetry/api-logs" "^0.202.0"
    "@opentelemetry/core" "^2.0.0"
    "@opentelemetry/instrumentation" "^0.202.0"

"@opentelemetry/instrumentation-redis-4@^0.49.0":
  version "0.49.0"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-redis-4/-/instrumentation-redis-4-0.49.0.tgz"
  integrity sha512-i+Wsl7M2LXEDA2yXouNJ3fttSzzb5AhlehvSBVRIFuinY51XrrKSH66biO0eox+pYQMwAlPxJ778XcMQffN78A==
  dependencies:
    "@opentelemetry/instrumentation" "^0.202.0"
    "@opentelemetry/redis-common" "^0.37.0"
    "@opentelemetry/semantic-conventions" "^1.27.0"

"@opentelemetry/instrumentation-redis-4@0.46.1":
  version "0.46.1"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-redis-4/-/instrumentation-redis-4-0.46.1.tgz"
  integrity sha512-UMqleEoabYMsWoTkqyt9WAzXwZ4BlFZHO40wr3d5ZvtjKCHlD4YXLm+6OLCeIi/HkX7EXvQaz8gtAwkwwSEvcQ==
  dependencies:
    "@opentelemetry/instrumentation" "^0.57.1"
    "@opentelemetry/redis-common" "^0.36.2"
    "@opentelemetry/semantic-conventions" "^1.27.0"

"@opentelemetry/instrumentation-redis@^0.49.1":
  version "0.49.1"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-redis/-/instrumentation-redis-0.49.1.tgz"
  integrity sha512-Ds5Ke9qE9kTlDThqLSJJntkIvuMQCBPiFKwHntocb/3q/9q5D47BNwawO5Mj9sVMV6zkld5M5Pb9Av39iieuOg==
  dependencies:
    "@opentelemetry/instrumentation" "^0.202.0"
    "@opentelemetry/redis-common" "^0.37.0"
    "@opentelemetry/semantic-conventions" "^1.27.0"

"@opentelemetry/instrumentation-restify@^0.48.1":
  version "0.48.1"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-restify/-/instrumentation-restify-0.48.1.tgz"
  integrity sha512-0KY7mWpm0TJJ8ajhsNsLUmsBE/yNr70o128Crn30eDmnyRQkG7uS0xfDi6keExjF7SKzXQabs3Gtx7SuFmE80Q==
  dependencies:
    "@opentelemetry/core" "^2.0.0"
    "@opentelemetry/instrumentation" "^0.202.0"
    "@opentelemetry/semantic-conventions" "^1.27.0"

"@opentelemetry/instrumentation-router@^0.47.0":
  version "0.47.0"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-router/-/instrumentation-router-0.47.0.tgz"
  integrity sha512-U0zA1LTDqtTWyd5e4SdoqQA/8QUOhc4LDv9U7b+8FMFTty95OF84apUdatl09Dzc51XeWPWIV7VutmSCd/zsUg==
  dependencies:
    "@opentelemetry/instrumentation" "^0.202.0"
    "@opentelemetry/semantic-conventions" "^1.27.0"

"@opentelemetry/instrumentation-runtime-node@^0.16.0":
  version "0.16.0"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-runtime-node/-/instrumentation-runtime-node-0.16.0.tgz"
  integrity sha512-Q/GB9LsKLrRCEIPLAQTDQvydnLmLXBSRkYkWzwKzY/LCkOs+Cl8YiJG08p6D4CaJ6lvP0iG4kwPHk1ydNbdehg==
  dependencies:
    "@opentelemetry/instrumentation" "^0.202.0"

"@opentelemetry/instrumentation-socket.io@^0.49.0":
  version "0.49.0"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-socket.io/-/instrumentation-socket.io-0.49.0.tgz"
  integrity sha512-DpMtNBEcaLCcbP1WVBPCSgRiBs31igTQkal1gUm40VL/XAv5GUqRAUnvHZrQh3yPipOqzV65pdb0jJXdps/tug==
  dependencies:
    "@opentelemetry/instrumentation" "^0.202.0"
    "@opentelemetry/semantic-conventions" "^1.27.0"

"@opentelemetry/instrumentation-tedious@^0.21.0":
  version "0.21.0"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-tedious/-/instrumentation-tedious-0.21.0.tgz"
  integrity sha512-pt37kHYGQ8D2vBOQwyB/TKUqLPF8Q4rfTNu3whZsPOsc6QHDPXpfQISIupWAnMjAaeujF/Spg6IA04W6jXrzRQ==
  dependencies:
    "@opentelemetry/instrumentation" "^0.202.0"
    "@opentelemetry/semantic-conventions" "^1.27.0"
    "@types/tedious" "^4.0.14"

"@opentelemetry/instrumentation-tedious@0.18.1":
  version "0.18.1"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-tedious/-/instrumentation-tedious-0.18.1.tgz"
  integrity sha512-5Cuy/nj0HBaH+ZJ4leuD7RjgvA844aY2WW+B5uLcWtxGjRZl3MNLuxnNg5DYWZNPO+NafSSnra0q49KWAHsKBg==
  dependencies:
    "@opentelemetry/instrumentation" "^0.57.1"
    "@opentelemetry/semantic-conventions" "^1.27.0"
    "@types/tedious" "^4.0.14"

"@opentelemetry/instrumentation-undici@^0.13.1":
  version "0.13.1"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-undici/-/instrumentation-undici-0.13.1.tgz"
  integrity sha512-w0e7q983oNa+dQiWOEgU+1R6H48ks6mICZKrIxY08KqZPFroPUYbH4Db7X6p8m4QhuHgI2/wEAgLf9h03ILzcg==
  dependencies:
    "@opentelemetry/core" "^2.0.0"
    "@opentelemetry/instrumentation" "^0.202.0"

"@opentelemetry/instrumentation-undici@0.10.1":
  version "0.10.1"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-undici/-/instrumentation-undici-0.10.1.tgz"
  integrity sha512-rkOGikPEyRpMCmNu9AQuV5dtRlDmJp2dK5sw8roVshAGoB6hH/3QjDtRhdwd75SsJwgynWUNRUYe0wAkTo16tQ==
  dependencies:
    "@opentelemetry/core" "^1.8.0"
    "@opentelemetry/instrumentation" "^0.57.1"

"@opentelemetry/instrumentation-user-interaction@^0.47.0":
  version "0.47.0"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-user-interaction/-/instrumentation-user-interaction-0.47.0.tgz"
  integrity sha512-IpycEgIr/ZH55rlgC6429+8ze5AE3OhqWGGNSuW0XddiO/jSXUhLCadNQYP2LS7ouS2bgOwr1gEF0FtQtaIe9w==
  dependencies:
    "@opentelemetry/core" "^2.0.0"
    "@opentelemetry/instrumentation" "^0.202.0"
    "@opentelemetry/sdk-trace-web" "^2.0.0"

"@opentelemetry/instrumentation-winston@^0.47.0":
  version "0.47.0"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-winston/-/instrumentation-winston-0.47.0.tgz"
  integrity sha512-r+GqnZU/aFldQyB5QdOlxsMlH9KZ4+zJfnYplz3lbC9f9ozAIlVAeoshvWTtbv7Oxp2NnK64EfnNP1pClaGEqA==
  dependencies:
    "@opentelemetry/api-logs" "^0.202.0"
    "@opentelemetry/instrumentation" "^0.202.0"

"@opentelemetry/instrumentation-xml-http-request@^0.202.0":
  version "0.202.0"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation-xml-http-request/-/instrumentation-xml-http-request-0.202.0.tgz"
  integrity sha512-N0wZyWpdUviscnhNKbRr2mEEfTtVi0ki7v6Lr9ZsK5mUtg12e9Mf/LsT2Msl7tvyGDGGi8Tmm8ssFrfLOADqDw==
  dependencies:
    "@opentelemetry/core" "2.0.1"
    "@opentelemetry/instrumentation" "0.202.0"
    "@opentelemetry/sdk-trace-web" "2.0.1"
    "@opentelemetry/semantic-conventions" "^1.29.0"

"@opentelemetry/instrumentation@^0.202.0", "@opentelemetry/instrumentation@~0.202.0", "@opentelemetry/instrumentation@0.202.0":
  version "0.202.0"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation/-/instrumentation-0.202.0.tgz"
  integrity sha512-Uz3BxZWPgDwgHM2+vCKEQRh0R8WKrd/q6Tus1vThRClhlPO39Dyz7mDrOr6KuqGXAlBQ1e5Tnymzri4RMZNaWA==
  dependencies:
    "@opentelemetry/api-logs" "0.202.0"
    import-in-the-middle "^1.8.1"
    require-in-the-middle "^7.1.1"

"@opentelemetry/instrumentation@^0.52.0 || ^0.53.0 || ^0.54.0 || ^0.55.0 || ^0.56.0 || ^0.57.0":
  version "0.57.2"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation/-/instrumentation-0.57.2.tgz"
  integrity sha512-BdBGhQBh8IjZ2oIIX6F2/Q3LKm/FDDKi6ccYKcBTeilh6SNdNKveDOLk73BkSJjQLJk6qe4Yh+hHw1UPhCDdrg==
  dependencies:
    "@opentelemetry/api-logs" "0.57.2"
    "@types/shimmer" "^1.2.0"
    import-in-the-middle "^1.8.1"
    require-in-the-middle "^7.1.1"
    semver "^7.5.2"
    shimmer "^1.2.1"

"@opentelemetry/instrumentation@^0.57.1", "@opentelemetry/instrumentation@^0.57.2", "@opentelemetry/instrumentation@0.57.2":
  version "0.57.2"
  resolved "https://registry.npmjs.org/@opentelemetry/instrumentation/-/instrumentation-0.57.2.tgz"
  integrity sha512-BdBGhQBh8IjZ2oIIX6F2/Q3LKm/FDDKi6ccYKcBTeilh6SNdNKveDOLk73BkSJjQLJk6qe4Yh+hHw1UPhCDdrg==
  dependencies:
    "@opentelemetry/api-logs" "0.57.2"
    "@types/shimmer" "^1.2.0"
    import-in-the-middle "^1.8.1"
    require-in-the-middle "^7.1.1"
    semver "^7.5.2"
    shimmer "^1.2.1"

"@opentelemetry/opentelemetry-browser-detector@~0.202.0":
  version "0.202.0"
  resolved "https://registry.npmjs.org/@opentelemetry/opentelemetry-browser-detector/-/opentelemetry-browser-detector-0.202.0.tgz"
  integrity sha512-rzPg5xXn6v+9elNV0TI0ycFiIjg7sv5mryj8oOVR8HKz48Xyk61RmJIOmyb9tmqJgsdoGWOLkQN84DTKGc/hzQ==
  dependencies:
    "@opentelemetry/resources" "2.0.1"

"@opentelemetry/otlp-exporter-base@0.202.0":
  version "0.202.0"
  resolved "https://registry.npmjs.org/@opentelemetry/otlp-exporter-base/-/otlp-exporter-base-0.202.0.tgz"
  integrity sha512-nMEOzel+pUFYuBJg2znGmHJWbmvMbdX5/RhoKNKowguMbURhz0fwik5tUKplLcUtl8wKPL1y9zPnPxeBn65N0Q==
  dependencies:
    "@opentelemetry/core" "2.0.1"
    "@opentelemetry/otlp-transformer" "0.202.0"

"@opentelemetry/otlp-grpc-exporter-base@0.202.0":
  version "0.202.0"
  resolved "https://registry.npmjs.org/@opentelemetry/otlp-grpc-exporter-base/-/otlp-grpc-exporter-base-0.202.0.tgz"
  integrity sha512-yIEHVxFA5dmYif7lZbbB66qulLLhrklj6mI2X3cuGW5hYPyUErztEmbroM+6teu/XobBi9bLHid2VT4NIaRuGg==
  dependencies:
    "@grpc/grpc-js" "^1.7.1"
    "@opentelemetry/core" "2.0.1"
    "@opentelemetry/otlp-exporter-base" "0.202.0"
    "@opentelemetry/otlp-transformer" "0.202.0"

"@opentelemetry/otlp-transformer@0.202.0":
  version "0.202.0"
  resolved "https://registry.npmjs.org/@opentelemetry/otlp-transformer/-/otlp-transformer-0.202.0.tgz"
  integrity sha512-5XO77QFzs9WkexvJQL9ksxL8oVFb/dfi9NWQSq7Sv0Efr9x3N+nb1iklP1TeVgxqJ7m1xWiC/Uv3wupiQGevMw==
  dependencies:
    "@opentelemetry/api-logs" "0.202.0"
    "@opentelemetry/core" "2.0.1"
    "@opentelemetry/resources" "2.0.1"
    "@opentelemetry/sdk-logs" "0.202.0"
    "@opentelemetry/sdk-metrics" "2.0.1"
    "@opentelemetry/sdk-trace-base" "2.0.1"
    protobufjs "^7.3.0"

"@opentelemetry/propagation-utils@^0.31.2":
  version "0.31.2"
  resolved "https://registry.npmjs.org/@opentelemetry/propagation-utils/-/propagation-utils-0.31.2.tgz"
  integrity sha512-FlJzdZ0cQY8qqOsJ/A+L/t05LvZtnsMq6vbamunVMYRi9TAy+xq37t+nT/dx3dKJ/2k409jDj9eA0Yhj9RtTug==

"@opentelemetry/propagator-b3@2.0.1":
  version "2.0.1"
  resolved "https://registry.npmjs.org/@opentelemetry/propagator-b3/-/propagator-b3-2.0.1.tgz"
  integrity sha512-Hc09CaQ8Tf5AGLmf449H726uRoBNGPBL4bjr7AnnUpzWMvhdn61F78z9qb6IqB737TffBsokGAK1XykFEZ1igw==
  dependencies:
    "@opentelemetry/core" "2.0.1"

"@opentelemetry/propagator-jaeger@2.0.1":
  version "2.0.1"
  resolved "https://registry.npmjs.org/@opentelemetry/propagator-jaeger/-/propagator-jaeger-2.0.1.tgz"
  integrity sha512-7PMdPBmGVH2eQNb/AtSJizQNgeNTfh6jQFqys6lfhd6P4r+m/nTh3gKPPpaCXVdRQ+z93vfKk+4UGty390283w==
  dependencies:
    "@opentelemetry/core" "2.0.1"

"@opentelemetry/redis-common@^0.36.2":
  version "0.36.2"
  resolved "https://registry.npmjs.org/@opentelemetry/redis-common/-/redis-common-0.36.2.tgz"
  integrity sha512-faYX1N0gpLhej/6nyp6bgRjzAKXn5GOEMYY7YhciSfCoITAktLUtQ36d24QEWNA1/WA1y6qQunCe0OhHRkVl9g==

"@opentelemetry/redis-common@^0.37.0":
  version "0.37.0"
  resolved "https://registry.npmjs.org/@opentelemetry/redis-common/-/redis-common-0.37.0.tgz"
  integrity sha512-tJwgE6jt32bLs/9J6jhQRKU2EZnsD8qaO13aoFyXwF6s4LhpT7YFHf3Z03MqdILk6BA2BFUhoyh7k9fj9i032A==

"@opentelemetry/resource-detector-alibaba-cloud@^0.31.2":
  version "0.31.2"
  resolved "https://registry.npmjs.org/@opentelemetry/resource-detector-alibaba-cloud/-/resource-detector-alibaba-cloud-0.31.2.tgz"
  integrity sha512-Itp6duMXkAIQzmDHIf1kc6Llj/fa0BxilaELp0K6Fp9y+b0ex9LksNAQfTDFPHNine7tFoXauvvHbJFXIB6mqw==
  dependencies:
    "@opentelemetry/core" "^2.0.0"
    "@opentelemetry/resources" "^2.0.0"
    "@opentelemetry/semantic-conventions" "^1.27.0"

"@opentelemetry/resource-detector-aws@^2.2.0":
  version "2.2.0"
  resolved "https://registry.npmjs.org/@opentelemetry/resource-detector-aws/-/resource-detector-aws-2.2.0.tgz"
  integrity sha512-6k7//RWAv4U1PeZhv0Too0Sv7sp7/A6s6g9h5ZYauPcroh2t4gOmkQSspSLYCynn34YZwn3FGbuaMwTDjHEJig==
  dependencies:
    "@opentelemetry/core" "^2.0.0"
    "@opentelemetry/resources" "^2.0.0"
    "@opentelemetry/semantic-conventions" "^1.27.0"

"@opentelemetry/resource-detector-azure@^0.9.0":
  version "0.9.0"
  resolved "https://registry.npmjs.org/@opentelemetry/resource-detector-azure/-/resource-detector-azure-0.9.0.tgz"
  integrity sha512-5wJwAAW2vhbqIhgaRisU1y0F5mUco59F/dKgmnnnT6YNbxjrbdUZYxKF5Wl7deJoACVdL5wi/3N97GCXPEwwCQ==
  dependencies:
    "@opentelemetry/core" "^2.0.0"
    "@opentelemetry/resources" "^2.0.0"
    "@opentelemetry/semantic-conventions" "^1.27.0"

"@opentelemetry/resource-detector-container@^0.7.2":
  version "0.7.2"
  resolved "https://registry.npmjs.org/@opentelemetry/resource-detector-container/-/resource-detector-container-0.7.2.tgz"
  integrity sha512-St3Krrbpvq7k0UoUNlm7Z4Xqf9HdS9R5yPslwl/WPaZpj/Bf/54WZTPmNQat+93Ey6PTX0ISKg26DfcjPemUhg==
  dependencies:
    "@opentelemetry/core" "^2.0.0"
    "@opentelemetry/resources" "^2.0.0"
    "@opentelemetry/semantic-conventions" "^1.27.0"

"@opentelemetry/resource-detector-gcp@^0.36.0":
  version "0.36.0"
  resolved "https://registry.npmjs.org/@opentelemetry/resource-detector-gcp/-/resource-detector-gcp-0.36.0.tgz"
  integrity sha512-mWnEcg4tA+IDPrkETWo42psEsDN20dzYZSm4ZH8m8uiQALnNksVmf5C3An0GUEj5zrrxMasjSuv4zEH1gI40XQ==
  dependencies:
    "@opentelemetry/core" "^2.0.0"
    "@opentelemetry/resources" "^2.0.0"
    "@opentelemetry/semantic-conventions" "^1.27.0"
    gcp-metadata "^6.0.0"

"@opentelemetry/resources@^1.30.1", "@opentelemetry/resources@1.30.1":
  version "1.30.1"
  resolved "https://registry.npmjs.org/@opentelemetry/resources/-/resources-1.30.1.tgz"
  integrity sha512-5UxZqiAgLYGFjS4s9qm5mBVo433u+dSPUFWVWXmLAD4wB65oMCoXaJP1KJa9DIYYMeHu3z4BZcStG3LC593cWA==
  dependencies:
    "@opentelemetry/core" "1.30.1"
    "@opentelemetry/semantic-conventions" "1.28.0"

"@opentelemetry/resources@^2.0.0", "@opentelemetry/resources@2.0.1":
  version "2.0.1"
  resolved "https://registry.npmjs.org/@opentelemetry/resources/-/resources-2.0.1.tgz"
  integrity sha512-dZOB3R6zvBwDKnHDTB4X1xtMArB/d324VsbiPkX/Yu0Q8T2xceRthoIVFhJdvgVM2QhGVUyX9tzwiNxGtoBJUw==
  dependencies:
    "@opentelemetry/core" "2.0.1"
    "@opentelemetry/semantic-conventions" "^1.29.0"

"@opentelemetry/sdk-logs@0.202.0":
  version "0.202.0"
  resolved "https://registry.npmjs.org/@opentelemetry/sdk-logs/-/sdk-logs-0.202.0.tgz"
  integrity sha512-pv8QiQLQzk4X909YKm0lnW4hpuQg4zHwJ4XBd5bZiXcd9urvrJNoNVKnxGHPiDVX/GiLFvr5DMYsDBQbZCypRQ==
  dependencies:
    "@opentelemetry/api-logs" "0.202.0"
    "@opentelemetry/core" "2.0.1"
    "@opentelemetry/resources" "2.0.1"

"@opentelemetry/sdk-metrics@2.0.1":
  version "2.0.1"
  resolved "https://registry.npmjs.org/@opentelemetry/sdk-metrics/-/sdk-metrics-2.0.1.tgz"
  integrity sha512-wf8OaJoSnujMAHWR3g+/hGvNcsC16rf9s1So4JlMiFaFHiE4HpIA3oUh+uWZQ7CNuK8gVW/pQSkgoa5HkkOl0g==
  dependencies:
    "@opentelemetry/core" "2.0.1"
    "@opentelemetry/resources" "2.0.1"

"@opentelemetry/sdk-node@^0.202.0":
  version "0.202.0"
  resolved "https://registry.npmjs.org/@opentelemetry/sdk-node/-/sdk-node-0.202.0.tgz"
  integrity sha512-SF9vXWVd9I5CZ69mW3GfwfLI2SHgyvEqntcg0en5y8kRp5+2PPoa3Mkgj0WzFLrbSgTw4PsXn7c7H6eSdrtV0w==
  dependencies:
    "@opentelemetry/api-logs" "0.202.0"
    "@opentelemetry/core" "2.0.1"
    "@opentelemetry/exporter-logs-otlp-grpc" "0.202.0"
    "@opentelemetry/exporter-logs-otlp-http" "0.202.0"
    "@opentelemetry/exporter-logs-otlp-proto" "0.202.0"
    "@opentelemetry/exporter-metrics-otlp-grpc" "0.202.0"
    "@opentelemetry/exporter-metrics-otlp-http" "0.202.0"
    "@opentelemetry/exporter-metrics-otlp-proto" "0.202.0"
    "@opentelemetry/exporter-prometheus" "0.202.0"
    "@opentelemetry/exporter-trace-otlp-grpc" "0.202.0"
    "@opentelemetry/exporter-trace-otlp-http" "0.202.0"
    "@opentelemetry/exporter-trace-otlp-proto" "0.202.0"
    "@opentelemetry/exporter-zipkin" "2.0.1"
    "@opentelemetry/instrumentation" "0.202.0"
    "@opentelemetry/propagator-b3" "2.0.1"
    "@opentelemetry/propagator-jaeger" "2.0.1"
    "@opentelemetry/resources" "2.0.1"
    "@opentelemetry/sdk-logs" "0.202.0"
    "@opentelemetry/sdk-metrics" "2.0.1"
    "@opentelemetry/sdk-trace-base" "2.0.1"
    "@opentelemetry/sdk-trace-node" "2.0.1"
    "@opentelemetry/semantic-conventions" "^1.29.0"

"@opentelemetry/sdk-trace-base@^1.30.1":
  version "1.30.1"
  resolved "https://registry.npmjs.org/@opentelemetry/sdk-trace-base/-/sdk-trace-base-1.30.1.tgz"
  integrity sha512-jVPgBbH1gCy2Lb7X0AVQ8XAfgg0pJ4nvl8/IiQA6nxOsPvS+0zMJaFSs2ltXe0J6C8dqjcnpyqINDJmU30+uOg==
  dependencies:
    "@opentelemetry/core" "1.30.1"
    "@opentelemetry/resources" "1.30.1"
    "@opentelemetry/semantic-conventions" "1.28.0"

"@opentelemetry/sdk-trace-base@^2.0.0", "@opentelemetry/sdk-trace-base@2.0.1":
  version "2.0.1"
  resolved "https://registry.npmjs.org/@opentelemetry/sdk-trace-base/-/sdk-trace-base-2.0.1.tgz"
  integrity sha512-xYLlvk/xdScGx1aEqvxLwf6sXQLXCjk3/1SQT9X9AoN5rXRhkdvIFShuNNmtTEPRBqcsMbS4p/gJLNI2wXaDuQ==
  dependencies:
    "@opentelemetry/core" "2.0.1"
    "@opentelemetry/resources" "2.0.1"
    "@opentelemetry/semantic-conventions" "^1.29.0"

"@opentelemetry/sdk-trace-node@2.0.1":
  version "2.0.1"
  resolved "https://registry.npmjs.org/@opentelemetry/sdk-trace-node/-/sdk-trace-node-2.0.1.tgz"
  integrity sha512-UhdbPF19pMpBtCWYP5lHbTogLWx9N0EBxtdagvkn5YtsAnCBZzL7SjktG+ZmupRgifsHMjwUaCCaVmqGfSADmA==
  dependencies:
    "@opentelemetry/context-async-hooks" "2.0.1"
    "@opentelemetry/core" "2.0.1"
    "@opentelemetry/sdk-trace-base" "2.0.1"

"@opentelemetry/sdk-trace-web@^2.0.0", "@opentelemetry/sdk-trace-web@2.0.1":
  version "2.0.1"
  resolved "https://registry.npmjs.org/@opentelemetry/sdk-trace-web/-/sdk-trace-web-2.0.1.tgz"
  integrity sha512-R4/i0rISvAujG4Zwk3s6ySyrWG+Db3SerZVM4jZ2lEzjrNylF7nRAy1hVvWe8gTbwIxX+6w6ZvZwdtl2C7UQHQ==
  dependencies:
    "@opentelemetry/core" "2.0.1"
    "@opentelemetry/sdk-trace-base" "2.0.1"

"@opentelemetry/semantic-conventions@^1.27.0", "@opentelemetry/semantic-conventions@^1.30.0":
  version "1.32.0"
  resolved "https://registry.npmjs.org/@opentelemetry/semantic-conventions/-/semantic-conventions-1.32.0.tgz"
  integrity sha512-s0OpmpQFSfMrmedAn9Lhg4KWJELHCU6uU9dtIJ28N8UGhf9Y55im5X8fEzwhwDwiSqN+ZPSNrDJF7ivf/AuRPQ==

"@opentelemetry/semantic-conventions@^1.29.0":
  version "1.34.0"
  resolved "https://registry.npmjs.org/@opentelemetry/semantic-conventions/-/semantic-conventions-1.34.0.tgz"
  integrity sha512-aKcOkyrorBGlajjRdVoJWHTxfxO1vCNHLJVlSDaRHDIdjU+pX8IYQPvPDkYiujKLbRnWU+1TBwEt0QRgSm4SGA==

"@opentelemetry/semantic-conventions@^1.31.0":
  version "1.34.0"
  resolved "https://registry.npmjs.org/@opentelemetry/semantic-conventions/-/semantic-conventions-1.34.0.tgz"
  integrity sha512-aKcOkyrorBGlajjRdVoJWHTxfxO1vCNHLJVlSDaRHDIdjU+pX8IYQPvPDkYiujKLbRnWU+1TBwEt0QRgSm4SGA==

"@opentelemetry/semantic-conventions@^1.33.1":
  version "1.34.0"
  resolved "https://registry.npmjs.org/@opentelemetry/semantic-conventions/-/semantic-conventions-1.34.0.tgz"
  integrity sha512-aKcOkyrorBGlajjRdVoJWHTxfxO1vCNHLJVlSDaRHDIdjU+pX8IYQPvPDkYiujKLbRnWU+1TBwEt0QRgSm4SGA==

"@opentelemetry/semantic-conventions@1.28.0":
  version "1.28.0"
  resolved "https://registry.npmjs.org/@opentelemetry/semantic-conventions/-/semantic-conventions-1.28.0.tgz"
  integrity sha512-lp4qAiMTD4sNWW4DbKLBkfiMZ4jbAboJIGOQr5DvciMRI494OapieI9qiODpOt0XBr1LjIDy1xAGAnVs5supTA==

"@opentelemetry/sql-common@^0.40.1":
  version "0.40.1"
  resolved "https://registry.npmjs.org/@opentelemetry/sql-common/-/sql-common-0.40.1.tgz"
  integrity sha512-nSDlnHSqzC3pXn/wZEZVLuAuJ1MYMXPBwtv2qAbCa3847SaHItdE7SzUq/Jtb0KZmh1zfAbNi3AAMjztTT4Ugg==
  dependencies:
    "@opentelemetry/core" "^1.1.0"

"@opentelemetry/sql-common@^0.41.0":
  version "0.41.0"
  resolved "https://registry.npmjs.org/@opentelemetry/sql-common/-/sql-common-0.41.0.tgz"
  integrity sha512-pmzXctVbEERbqSfiAgdes9Y63xjoOyXcD7B6IXBkVb+vbM7M9U98mn33nGXxPf4dfYR0M+vhcKRZmbSJ7HfqFA==
  dependencies:
    "@opentelemetry/core" "^2.0.0"

"@opentelemetry/web-common@~0.202.0":
  version "0.202.0"
  resolved "https://registry.npmjs.org/@opentelemetry/web-common/-/web-common-0.202.0.tgz"
  integrity sha512-GXLLvPypnqqkQirWt+ns9f3mDyP8l5LryhYi7yqE1QVRlbFCkRl2zMbKbPHTZ7hMbicMS8NUlQEdfEXLBK86rQ==
  dependencies:
    "@opentelemetry/sdk-logs" "0.202.0"
    "@opentelemetry/sdk-trace-base" "2.0.1"
    "@opentelemetry/semantic-conventions" "^1.29.0"

"@pkgjs/parseargs@^0.11.0":
  version "0.11.0"
  resolved "https://registry.npmjs.org/@pkgjs/parseargs/-/parseargs-0.11.0.tgz"
  integrity sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg==

"@prisma/instrumentation@6.6.0":
  version "6.6.0"
  resolved "https://registry.npmjs.org/@prisma/instrumentation/-/instrumentation-6.6.0.tgz"
  integrity sha512-M/a6njz3hbf2oucwdbjNKrSMLuyMCwgDrmTtkF1pm4Nm7CU45J/Hd6lauF2CDACTUYzu3ymcV7P0ZAhIoj6WRw==
  dependencies:
    "@opentelemetry/instrumentation" "^0.52.0 || ^0.53.0 || ^0.54.0 || ^0.55.0 || ^0.56.0 || ^0.57.0"

"@protobufjs/aspromise@^1.1.1", "@protobufjs/aspromise@^1.1.2":
  version "1.1.2"
  resolved "https://registry.npmjs.org/@protobufjs/aspromise/-/aspromise-1.1.2.tgz"
  integrity sha512-j+gKExEuLmKwvz3OgROXtrJ2UG2x8Ch2YZUxahh+s1F2HZ+wAceUNLkvy6zKCPVRkU++ZWQrdxsUeQXmcg4uoQ==

"@protobufjs/base64@^1.1.2":
  version "1.1.2"
  resolved "https://registry.npmjs.org/@protobufjs/base64/-/base64-1.1.2.tgz"
  integrity sha512-AZkcAA5vnN/v4PDqKyMR5lx7hZttPDgClv83E//FMNhR2TMcLUhfRUBHCmSl0oi9zMgDDqRUJkSxO3wm85+XLg==

"@protobufjs/codegen@^2.0.4":
  version "2.0.4"
  resolved "https://registry.npmjs.org/@protobufjs/codegen/-/codegen-2.0.4.tgz"
  integrity sha512-YyFaikqM5sH0ziFZCN3xDC7zeGaB/d0IUb9CATugHWbd1FRFwWwt4ld4OYMPWu5a3Xe01mGAULCdqhMlPl29Jg==

"@protobufjs/eventemitter@^1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@protobufjs/eventemitter/-/eventemitter-1.1.0.tgz"
  integrity sha512-j9ednRT81vYJ9OfVuXG6ERSTdEL1xVsNgqpkxMsbIabzSo3goCjDIveeGv5d03om39ML71RdmrGNjG5SReBP/Q==

"@protobufjs/fetch@^1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@protobufjs/fetch/-/fetch-1.1.0.tgz"
  integrity sha512-lljVXpqXebpsijW71PZaCYeIcE5on1w5DlQy5WH6GLbFryLUrBD4932W/E2BSpfRJWseIL4v/KPgBFxDOIdKpQ==
  dependencies:
    "@protobufjs/aspromise" "^1.1.1"
    "@protobufjs/inquire" "^1.1.0"

"@protobufjs/float@^1.0.2":
  version "1.0.2"
  resolved "https://registry.npmjs.org/@protobufjs/float/-/float-1.0.2.tgz"
  integrity sha512-Ddb+kVXlXst9d+R9PfTIxh1EdNkgoRe5tOX6t01f1lYWOvJnSPDBlG241QLzcyPdoNTsblLUdujGSE4RzrTZGQ==

"@protobufjs/inquire@^1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@protobufjs/inquire/-/inquire-1.1.0.tgz"
  integrity sha512-kdSefcPdruJiFMVSbn801t4vFK7KB/5gd2fYvrxhuJYg8ILrmn9SKSX2tZdV6V+ksulWqS7aXjBcRXl3wHoD9Q==

"@protobufjs/path@^1.1.2":
  version "1.1.2"
  resolved "https://registry.npmjs.org/@protobufjs/path/-/path-1.1.2.tgz"
  integrity sha512-6JOcJ5Tm08dOHAbdR3GrvP+yUUfkjG5ePsHYczMFLq3ZmMkAD98cDgcT2iA1lJ9NVwFd4tH/iSSoe44YWkltEA==

"@protobufjs/pool@^1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@protobufjs/pool/-/pool-1.1.0.tgz"
  integrity sha512-0kELaGSIDBKvcgS4zkjz1PeddatrjYcmMWOlAuAPwAeccUrPHdUqo/J6LiymHHEiJT5NrF1UVwxY14f+fy4WQw==

"@protobufjs/utf8@^1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@protobufjs/utf8/-/utf8-1.1.0.tgz"
  integrity sha512-Vvn3zZrhQZkkBE8LSuW3em98c0FwgO4nxzv6OdSxPKJIEKY2bGbHn+mhGIPerzI4twdxaP8/0+06HBpwf345Lw==

"@radix-ui/number@1.1.1":
  version "1.1.1"
  resolved "https://registry.npmjs.org/@radix-ui/number/-/number-1.1.1.tgz"
  integrity sha512-MkKCwxlXTgz6CFoJx3pCwn07GKp36+aZyu/u2Ln2VrA5DcdyCZkASEDBTd8x5whTQQL5CiYf4prXKLcgQdv29g==

"@radix-ui/primitive@1.1.2":
  version "1.1.2"
  resolved "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.1.2.tgz"
  integrity sha512-XnbHrrprsNqZKQhStrSwgRUQzoCI1glLzdw79xiZPoofhGICeZRSQ3dIxAKH1gb3OHfNf4d6f+vAv3kil2eggA==

"@radix-ui/react-alert-dialog@^1.1.6":
  version "1.1.11"
  resolved "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.11.tgz"
  integrity sha512-4KfkwrFnAw3Y5Jeoq6G+JYSKW0JfIS3uDdFC/79Jw9AsMayZMizSSMxk1gkrolYXsa/WzbbDfOA7/D8N5D+l1g==
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-dialog" "1.1.11"
    "@radix-ui/react-primitive" "2.1.0"
    "@radix-ui/react-slot" "1.2.0"

"@radix-ui/react-arrow@1.1.4":
  version "1.1.4"
  resolved "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.4.tgz"
  integrity sha512-qz+fxrqgNxG0dYew5l7qR3c7wdgRu1XVUHGnGYX7rg5HM4p9SWaRmJwfgR3J0SgyUKayLmzQIun+N6rWRgiRKw==
  dependencies:
    "@radix-ui/react-primitive" "2.1.0"

"@radix-ui/react-arrow@1.1.6":
  version "1.1.6"
  resolved "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.6.tgz"
  integrity sha512-2JMfHJf/eVnwq+2dewT3C0acmCWD3XiVA1Da+jTDqo342UlU13WvXtqHhG+yJw5JeQmu4ue2eMy6gcEArLBlcw==
  dependencies:
    "@radix-ui/react-primitive" "2.1.2"

"@radix-ui/react-avatar@^1.1.3":
  version "1.1.7"
  resolved "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.7.tgz"
  integrity sha512-V7ODUt4mUoJTe3VUxZw6nfURxaPALVqmDQh501YmaQsk3D8AZQrOPRnfKn4H7JGDLBc0KqLhT94H79nV88ppNg==
  dependencies:
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-primitive" "2.1.0"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-is-hydrated" "0.1.0"
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-checkbox@^1.3.2":
  version "1.3.2"
  resolved "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.3.2.tgz"
  integrity sha512-yd+dI56KZqawxKZrJ31eENUwqc1QSqg4OZ15rybGjF2ZNwMO+wCyHzAVLRp9qoYJf7kYy0YpZ2b0JCzJ42HZpA==
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-presence" "1.1.4"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "@radix-ui/react-use-previous" "1.1.1"
    "@radix-ui/react-use-size" "1.1.1"

"@radix-ui/react-collapsible@^1.1.8":
  version "1.1.8"
  resolved "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.8.tgz"
  integrity sha512-hxEsLvK9WxIAPyxdDRULL4hcaSjMZCfP7fHB0Z1uUnDoDBat1Zh46hwYfa69DeZAbJrPckjf0AGAtEZyvDyJbw==
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-presence" "1.1.4"
    "@radix-ui/react-primitive" "2.1.0"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-collection@1.1.4":
  version "1.1.4"
  resolved "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.4.tgz"
  integrity sha512-cv4vSf7HttqXilDnAnvINd53OTl1/bjUYVZrkFnA7nwmY9Ob2POUy0WY0sfqBAe1s5FyKsyceQlqiEGPYNTadg==
  dependencies:
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-primitive" "2.1.0"
    "@radix-ui/react-slot" "1.2.0"

"@radix-ui/react-compose-refs@^1.1.1", "@radix-ui/react-compose-refs@1.1.2":
  version "1.1.2"
  resolved "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.2.tgz"
  integrity sha512-z4eqJvfiNnFMHIIvXP3CY57y2WJs5g2v3X0zm9mEJkrkNv4rDxu+sg9Jh8EkXyeqBkB7SOcboo9dMVqhyrACIg==

"@radix-ui/react-context@1.1.2":
  version "1.1.2"
  resolved "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.2.tgz"
  integrity sha512-jCi/QKUM2r1Ju5a3J64TH2A5SpKAgh0LpknyqdQ4m6DCV0xJ2HG1xARRwNGPQfi1SLdLWZ1OJz6F4OMBBNiGJA==

"@radix-ui/react-dialog@^1.1.13", "@radix-ui/react-dialog@^1.1.6":
  version "1.1.13"
  resolved "https://registry.npmjs.org/@radix-ui/react-dialog/-/react-dialog-1.1.13.tgz"
  integrity sha512-ARFmqUyhIVS3+riWzwGTe7JLjqwqgnODBUZdqpWar/z1WFs9z76fuOs/2BOWCR+YboRn4/WN9aoaGVwqNRr8VA==
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-dismissable-layer" "1.1.9"
    "@radix-ui/react-focus-guards" "1.1.2"
    "@radix-ui/react-focus-scope" "1.1.6"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-portal" "1.1.8"
    "@radix-ui/react-presence" "1.1.4"
    "@radix-ui/react-primitive" "2.1.2"
    "@radix-ui/react-slot" "1.2.2"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    aria-hidden "^1.2.4"
    react-remove-scroll "^2.6.3"

"@radix-ui/react-dialog@1.1.11":
  version "1.1.11"
  resolved "https://registry.npmjs.org/@radix-ui/react-dialog/-/react-dialog-1.1.11.tgz"
  integrity sha512-yI7S1ipkP5/+99qhSI6nthfo/tR6bL6Zgxi/+1UO6qPa6UeM6nlafWcQ65vB4rU2XjgjMfMhI3k9Y5MztA62VQ==
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-dismissable-layer" "1.1.7"
    "@radix-ui/react-focus-guards" "1.1.2"
    "@radix-ui/react-focus-scope" "1.1.4"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-portal" "1.1.6"
    "@radix-ui/react-presence" "1.1.4"
    "@radix-ui/react-primitive" "2.1.0"
    "@radix-ui/react-slot" "1.2.0"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    aria-hidden "^1.2.4"
    react-remove-scroll "^2.6.3"

"@radix-ui/react-direction@1.1.1":
  version "1.1.1"
  resolved "https://registry.npmjs.org/@radix-ui/react-direction/-/react-direction-1.1.1.tgz"
  integrity sha512-1UEWRX6jnOA2y4H5WczZ44gOOjTEmlqv1uNW4GAJEO5+bauCBhv8snY65Iw5/VOS/ghKN9gr2KjnLKxrsvoMVw==

"@radix-ui/react-dismissable-layer@1.1.7":
  version "1.1.7"
  resolved "https://registry.npmjs.org/@radix-ui/react-dismissable-layer/-/react-dismissable-layer-1.1.7.tgz"
  integrity sha512-j5+WBUdhccJsmH5/H0K6RncjDtoALSEr6jbkaZu+bjw6hOPOhHycr6vEUujl+HBK8kjUfWcoCJXxP6e4lUlMZw==
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-primitive" "2.1.0"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-escape-keydown" "1.1.1"

"@radix-ui/react-dismissable-layer@1.1.9":
  version "1.1.9"
  resolved "https://registry.npmjs.org/@radix-ui/react-dismissable-layer/-/react-dismissable-layer-1.1.9.tgz"
  integrity sha512-way197PiTvNp+WBP7svMJasHl+vibhWGQDb6Mgf5mhEWJkgb85z7Lfl9TUdkqpWsf8GRNmoopx9ZxCyDzmgRMQ==
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-primitive" "2.1.2"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-escape-keydown" "1.1.1"

"@radix-ui/react-dropdown-menu@^2.1.6":
  version "2.1.12"
  resolved "https://registry.npmjs.org/@radix-ui/react-dropdown-menu/-/react-dropdown-menu-2.1.12.tgz"
  integrity sha512-VJoMs+BWWE7YhzEQyVwvF9n22Eiyr83HotCVrMQzla/OwRovXCgah7AcaEr4hMNj4gJxSdtIbcHGvmJXOoJVHA==
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-menu" "2.1.12"
    "@radix-ui/react-primitive" "2.1.0"
    "@radix-ui/react-use-controllable-state" "1.2.2"

"@radix-ui/react-focus-guards@1.1.2":
  version "1.1.2"
  resolved "https://registry.npmjs.org/@radix-ui/react-focus-guards/-/react-focus-guards-1.1.2.tgz"
  integrity sha512-fyjAACV62oPV925xFCrH8DR5xWhg9KYtJT4s3u54jxp+L/hbpTY2kIeEFFbFe+a/HCE94zGQMZLIpVTPVZDhaA==

"@radix-ui/react-focus-scope@1.1.4":
  version "1.1.4"
  resolved "https://registry.npmjs.org/@radix-ui/react-focus-scope/-/react-focus-scope-1.1.4.tgz"
  integrity sha512-r2annK27lIW5w9Ho5NyQgqs0MmgZSTIKXWpVCJaLC1q2kZrZkcqnmHkCHMEmv8XLvsLlurKMPT+kbKkRkm/xVA==
  dependencies:
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-primitive" "2.1.0"
    "@radix-ui/react-use-callback-ref" "1.1.1"

"@radix-ui/react-focus-scope@1.1.6":
  version "1.1.6"
  resolved "https://registry.npmjs.org/@radix-ui/react-focus-scope/-/react-focus-scope-1.1.6.tgz"
  integrity sha512-r9zpYNUQY+2jWHWZGyddQLL9YHkM/XvSFHVcWs7bdVuxMAnCwTAuy6Pf47Z4nw7dYcUou1vg/VgjjrrH03VeBw==
  dependencies:
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-primitive" "2.1.2"
    "@radix-ui/react-use-callback-ref" "1.1.1"

"@radix-ui/react-id@^1.1.0", "@radix-ui/react-id@1.1.1":
  version "1.1.1"
  resolved "https://registry.npmjs.org/@radix-ui/react-id/-/react-id-1.1.1.tgz"
  integrity sha512-kGkGegYIdQsOb4XjsfM97rXsiHaBwco+hFI66oO4s9LU+PLAC5oJ7khdOVFxkhsmlbpUqDAvXw11CluXP+jkHg==
  dependencies:
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-label@^2.1.6":
  version "2.1.6"
  resolved "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.6.tgz"
  integrity sha512-S/hv1mTlgcPX2gCTJrWuTjSXf7ER3Zf7zWGtOprxhIIY93Qin3n5VgNA0Ez9AgrK/lEtlYgzLd4f5x6AVar4Yw==
  dependencies:
    "@radix-ui/react-primitive" "2.1.2"

"@radix-ui/react-menu@2.1.12":
  version "2.1.12"
  resolved "https://registry.npmjs.org/@radix-ui/react-menu/-/react-menu-2.1.12.tgz"
  integrity sha512-+qYq6LfbiGo97Zz9fioX83HCiIYYFNs8zAsVCMQrIakoNYylIzWuoD/anAD3UzvvR6cnswmfRFJFq/zYYq/k7Q==
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-collection" "1.1.4"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-dismissable-layer" "1.1.7"
    "@radix-ui/react-focus-guards" "1.1.2"
    "@radix-ui/react-focus-scope" "1.1.4"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-popper" "1.2.4"
    "@radix-ui/react-portal" "1.1.6"
    "@radix-ui/react-presence" "1.1.4"
    "@radix-ui/react-primitive" "2.1.0"
    "@radix-ui/react-roving-focus" "1.1.7"
    "@radix-ui/react-slot" "1.2.0"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    aria-hidden "^1.2.4"
    react-remove-scroll "^2.6.3"

"@radix-ui/react-popover@^1.1.13":
  version "1.1.13"
  resolved "https://registry.npmjs.org/@radix-ui/react-popover/-/react-popover-1.1.13.tgz"
  integrity sha512-84uqQV3omKDR076izYgcha6gdpN8m3z6w/AeJ83MSBJYVG/AbOHdLjAgsPZkeC/kt+k64moXFCnio8BbqXszlw==
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-dismissable-layer" "1.1.9"
    "@radix-ui/react-focus-guards" "1.1.2"
    "@radix-ui/react-focus-scope" "1.1.6"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-popper" "1.2.6"
    "@radix-ui/react-portal" "1.1.8"
    "@radix-ui/react-presence" "1.1.4"
    "@radix-ui/react-primitive" "2.1.2"
    "@radix-ui/react-slot" "1.2.2"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    aria-hidden "^1.2.4"
    react-remove-scroll "^2.6.3"

"@radix-ui/react-popper@1.2.4":
  version "1.2.4"
  resolved "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.4.tgz"
  integrity sha512-3p2Rgm/a1cK0r/UVkx5F/K9v/EplfjAeIFCGOPYPO4lZ0jtg4iSQXt/YGTSLWaf4x7NG6Z4+uKFcylcTZjeqDA==
  dependencies:
    "@floating-ui/react-dom" "^2.0.0"
    "@radix-ui/react-arrow" "1.1.4"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-primitive" "2.1.0"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-layout-effect" "1.1.1"
    "@radix-ui/react-use-rect" "1.1.1"
    "@radix-ui/react-use-size" "1.1.1"
    "@radix-ui/rect" "1.1.1"

"@radix-ui/react-popper@1.2.6":
  version "1.2.6"
  resolved "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.6.tgz"
  integrity sha512-7iqXaOWIjDBfIG7aq8CUEeCSsQMLFdn7VEE8TaFz704DtEzpPHR7w/uuzRflvKgltqSAImgcmxQ7fFX3X7wasg==
  dependencies:
    "@floating-ui/react-dom" "^2.0.0"
    "@radix-ui/react-arrow" "1.1.6"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-primitive" "2.1.2"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-layout-effect" "1.1.1"
    "@radix-ui/react-use-rect" "1.1.1"
    "@radix-ui/react-use-size" "1.1.1"
    "@radix-ui/rect" "1.1.1"

"@radix-ui/react-portal@1.1.6":
  version "1.1.6"
  resolved "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.6.tgz"
  integrity sha512-XmsIl2z1n/TsYFLIdYam2rmFwf9OC/Sh2avkbmVMDuBZIe7hSpM0cYnWPAo7nHOVx8zTuwDZGByfcqLdnzp3Vw==
  dependencies:
    "@radix-ui/react-primitive" "2.1.0"
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-portal@1.1.8":
  version "1.1.8"
  resolved "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.8.tgz"
  integrity sha512-hQsTUIn7p7fxCPvao/q6wpbxmCwgLrlz+nOrJgC+RwfZqWY/WN+UMqkXzrtKbPrF82P43eCTl3ekeKuyAQbFeg==
  dependencies:
    "@radix-ui/react-primitive" "2.1.2"
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-presence@1.1.4":
  version "1.1.4"
  resolved "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.4.tgz"
  integrity sha512-ueDqRbdc4/bkaQT3GIpLQssRlFgWaL/U2z/S31qRwwLWoxHLgry3SIfCwhxeQNbirEUXFa+lq3RL3oBYXtcmIA==
  dependencies:
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-primitive@^2.0.2":
  version "2.1.2"
  resolved "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.1.2.tgz"
  integrity sha512-uHa+l/lKfxuDD2zjN/0peM/RhhSmRjr5YWdk/37EnSv1nJ88uvG85DPexSm8HdFQROd2VdERJ6ynXbkCFi+APw==
  dependencies:
    "@radix-ui/react-slot" "1.2.2"

"@radix-ui/react-primitive@2.1.0":
  version "2.1.0"
  resolved "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.1.0.tgz"
  integrity sha512-/J/FhLdK0zVcILOwt5g+dH4KnkonCtkVJsa2G6JmvbbtZfBEI1gMsO3QMjseL4F/SwfAMt1Vc/0XKYKq+xJ1sw==
  dependencies:
    "@radix-ui/react-slot" "1.2.0"

"@radix-ui/react-primitive@2.1.2":
  version "2.1.2"
  resolved "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.1.2.tgz"
  integrity sha512-uHa+l/lKfxuDD2zjN/0peM/RhhSmRjr5YWdk/37EnSv1nJ88uvG85DPexSm8HdFQROd2VdERJ6ynXbkCFi+APw==
  dependencies:
    "@radix-ui/react-slot" "1.2.2"

"@radix-ui/react-primitive@2.1.3":
  version "2.1.3"
  resolved "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.1.3.tgz"
  integrity sha512-m9gTwRkhy2lvCPe6QJp4d3G1TYEUHn/FzJUtq9MjH46an1wJU+GdoGC5VLof8RX8Ft/DlpshApkhswDLZzHIcQ==
  dependencies:
    "@radix-ui/react-slot" "1.2.3"

"@radix-ui/react-progress@^1.1.7":
  version "1.1.7"
  resolved "https://registry.npmjs.org/@radix-ui/react-progress/-/react-progress-1.1.7.tgz"
  integrity sha512-vPdg/tF6YC/ynuBIJlk1mm7Le0VgW6ub6J2UWnTQ7/D23KXcPI1qy+0vBkgKgd38RCMJavBXpB83HPNFMTb0Fg==
  dependencies:
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-primitive" "2.1.3"

"@radix-ui/react-radio-group@^1.2.3":
  version "1.3.4"
  resolved "https://registry.npmjs.org/@radix-ui/react-radio-group/-/react-radio-group-1.3.4.tgz"
  integrity sha512-N4J9QFdW5zcJNxxY/zwTXBN4Uc5VEuRM7ZLjNfnWoKmNvgrPtNNw4P8zY532O3qL6aPkaNO+gY9y6bfzmH4U1g==
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-presence" "1.1.4"
    "@radix-ui/react-primitive" "2.1.0"
    "@radix-ui/react-roving-focus" "1.1.7"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "@radix-ui/react-use-previous" "1.1.1"
    "@radix-ui/react-use-size" "1.1.1"

"@radix-ui/react-roving-focus@1.1.7":
  version "1.1.7"
  resolved "https://registry.npmjs.org/@radix-ui/react-roving-focus/-/react-roving-focus-1.1.7.tgz"
  integrity sha512-C6oAg451/fQT3EGbWHbCQjYTtbyjNO1uzQgMzwyivcHT3GKNEmu1q3UuREhN+HzHAVtv3ivMVK08QlC+PkYw9Q==
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-collection" "1.1.4"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-primitive" "2.1.0"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-controllable-state" "1.2.2"

"@radix-ui/react-scroll-area@^1.2.3":
  version "1.2.6"
  resolved "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.6.tgz"
  integrity sha512-lj8OMlpPERXrQIHlEQdlXHJoRT52AMpBrgyPYylOhXYq5e/glsEdtOc/kCQlsTdtgN5U0iDbrrolDadvektJGQ==
  dependencies:
    "@radix-ui/number" "1.1.1"
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-presence" "1.1.4"
    "@radix-ui/react-primitive" "2.1.0"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-select@^2.1.6":
  version "2.2.2"
  resolved "https://registry.npmjs.org/@radix-ui/react-select/-/react-select-2.2.2.tgz"
  integrity sha512-HjkVHtBkuq+r3zUAZ/CvNWUGKPfuicGDbgtZgiQuFmNcV5F+Tgy24ep2nsAW2nFgvhGPJVqeBZa6KyVN0EyrBA==
  dependencies:
    "@radix-ui/number" "1.1.1"
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-collection" "1.1.4"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-dismissable-layer" "1.1.7"
    "@radix-ui/react-focus-guards" "1.1.2"
    "@radix-ui/react-focus-scope" "1.1.4"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-popper" "1.2.4"
    "@radix-ui/react-portal" "1.1.6"
    "@radix-ui/react-primitive" "2.1.0"
    "@radix-ui/react-slot" "1.2.0"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "@radix-ui/react-use-layout-effect" "1.1.1"
    "@radix-ui/react-use-previous" "1.1.1"
    "@radix-ui/react-visually-hidden" "1.2.0"
    aria-hidden "^1.2.4"
    react-remove-scroll "^2.6.3"

"@radix-ui/react-separator@^1.1.2":
  version "1.1.4"
  resolved "https://registry.npmjs.org/@radix-ui/react-separator/-/react-separator-1.1.4.tgz"
  integrity sha512-2fTm6PSiUm8YPq9W0E4reYuv01EE3aFSzt8edBiXqPHshF8N9+Kymt/k0/R+F3dkY5lQyB/zPtrP82phskLi7w==
  dependencies:
    "@radix-ui/react-primitive" "2.1.0"

"@radix-ui/react-slider@^1.2.3":
  version "1.3.2"
  resolved "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.3.2.tgz"
  integrity sha512-oQnqfgSiYkxZ1MrF6672jw2/zZvpB+PJsrIc3Zm1zof1JHf/kj7WhmROw7JahLfOwYQ5/+Ip0rFORgF1tjSiaQ==
  dependencies:
    "@radix-ui/number" "1.1.1"
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-collection" "1.1.4"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-primitive" "2.1.0"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "@radix-ui/react-use-layout-effect" "1.1.1"
    "@radix-ui/react-use-previous" "1.1.1"
    "@radix-ui/react-use-size" "1.1.1"

"@radix-ui/react-slot@^1.2.2", "@radix-ui/react-slot@1.2.2":
  version "1.2.2"
  resolved "https://registry.npmjs.org/@radix-ui/react-slot/-/react-slot-1.2.2.tgz"
  integrity sha512-y7TBO4xN4Y94FvcWIOIh18fM4R1A8S4q1jhoz4PNzOoHsFcN8pogcFmZrTYAm4F9VRUrWP/Mw7xSKybIeRI+CQ==
  dependencies:
    "@radix-ui/react-compose-refs" "1.1.2"

"@radix-ui/react-slot@1.2.0":
  version "1.2.0"
  resolved "https://registry.npmjs.org/@radix-ui/react-slot/-/react-slot-1.2.0.tgz"
  integrity sha512-ujc+V6r0HNDviYqIK3rW4ffgYiZ8g5DEHrGJVk4x7kTlLXRDILnKX9vAUYeIsLOoDpDJ0ujpqMkjH4w2ofuo6w==
  dependencies:
    "@radix-ui/react-compose-refs" "1.1.2"

"@radix-ui/react-slot@1.2.3":
  version "1.2.3"
  resolved "https://registry.npmjs.org/@radix-ui/react-slot/-/react-slot-1.2.3.tgz"
  integrity sha512-aeNmHnBxbi2St0au6VBVC7JXFlhLlOnvIIlePNniyUNAClzmtAUEY8/pBiK3iHjufOlwA+c20/8jngo7xcrg8A==
  dependencies:
    "@radix-ui/react-compose-refs" "1.1.2"

"@radix-ui/react-switch@^1.1.3":
  version "1.2.2"
  resolved "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.2.2.tgz"
  integrity sha512-7Z8n6L+ifMIIYZ83f28qWSceUpkXuslI2FJ34+kDMTiyj91ENdpdQ7VCidrzj5JfwfZTeano/BnGBbu/jqa5rQ==
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-primitive" "2.1.0"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "@radix-ui/react-use-previous" "1.1.1"
    "@radix-ui/react-use-size" "1.1.1"

"@radix-ui/react-tabs@^1.1.3":
  version "1.1.9"
  resolved "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.9.tgz"
  integrity sha512-KIjtwciYvquiW/wAFkELZCVnaNLBsYNhTNcvl+zfMAbMhRkcvNuCLXDDd22L0j7tagpzVh/QwbFpwAATg7ILPw==
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-presence" "1.1.4"
    "@radix-ui/react-primitive" "2.1.0"
    "@radix-ui/react-roving-focus" "1.1.7"
    "@radix-ui/react-use-controllable-state" "1.2.2"

"@radix-ui/react-tooltip@^1.2.6":
  version "1.2.6"
  resolved "https://registry.npmjs.org/@radix-ui/react-tooltip/-/react-tooltip-1.2.6.tgz"
  integrity sha512-zYb+9dc9tkoN2JjBDIIPLQtk3gGyz8FMKoqYTb8EMVQ5a5hBcdHPECrsZVI4NpPAUOixhkoqg7Hj5ry5USowfA==
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-dismissable-layer" "1.1.9"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-popper" "1.2.6"
    "@radix-ui/react-portal" "1.1.8"
    "@radix-ui/react-presence" "1.1.4"
    "@radix-ui/react-primitive" "2.1.2"
    "@radix-ui/react-slot" "1.2.2"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "@radix-ui/react-visually-hidden" "1.2.2"

"@radix-ui/react-use-callback-ref@1.1.1":
  version "1.1.1"
  resolved "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-1.1.1.tgz"
  integrity sha512-FkBMwD+qbGQeMu1cOHnuGB6x4yzPjho8ap5WtbEJ26umhgqVXbhekKUQO+hZEL1vU92a3wHwdp0HAcqAUF5iDg==

"@radix-ui/react-use-controllable-state@1.2.2":
  version "1.2.2"
  resolved "https://registry.npmjs.org/@radix-ui/react-use-controllable-state/-/react-use-controllable-state-1.2.2.tgz"
  integrity sha512-BjasUjixPFdS+NKkypcyyN5Pmg83Olst0+c6vGov0diwTEo6mgdqVR6hxcEgFuh4QrAs7Rc+9KuGJ9TVCj0Zzg==
  dependencies:
    "@radix-ui/react-use-effect-event" "0.0.2"
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-use-effect-event@0.0.2":
  version "0.0.2"
  resolved "https://registry.npmjs.org/@radix-ui/react-use-effect-event/-/react-use-effect-event-0.0.2.tgz"
  integrity sha512-Qp8WbZOBe+blgpuUT+lw2xheLP8q0oatc9UpmiemEICxGvFLYmHm9QowVZGHtJlGbS6A6yJ3iViad/2cVjnOiA==
  dependencies:
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-use-escape-keydown@1.1.1":
  version "1.1.1"
  resolved "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.1.1.tgz"
  integrity sha512-Il0+boE7w/XebUHyBjroE+DbByORGR9KKmITzbR7MyQ4akpORYP/ZmbhAr0DG7RmmBqoOnZdy2QlvajJ2QA59g==
  dependencies:
    "@radix-ui/react-use-callback-ref" "1.1.1"

"@radix-ui/react-use-is-hydrated@0.1.0":
  version "0.1.0"
  resolved "https://registry.npmjs.org/@radix-ui/react-use-is-hydrated/-/react-use-is-hydrated-0.1.0.tgz"
  integrity sha512-U+UORVEq+cTnRIaostJv9AGdV3G6Y+zbVd+12e18jQ5A3c0xL03IhnHuiU4UV69wolOQp5GfR58NW/EgdQhwOA==
  dependencies:
    use-sync-external-store "^1.5.0"

"@radix-ui/react-use-layout-effect@1.1.1":
  version "1.1.1"
  resolved "https://registry.npmjs.org/@radix-ui/react-use-layout-effect/-/react-use-layout-effect-1.1.1.tgz"
  integrity sha512-RbJRS4UWQFkzHTTwVymMTUv8EqYhOp8dOOviLj2ugtTiXRaRQS7GLGxZTLL1jWhMeoSCf5zmcZkqTl9IiYfXcQ==

"@radix-ui/react-use-previous@1.1.1":
  version "1.1.1"
  resolved "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-1.1.1.tgz"
  integrity sha512-2dHfToCj/pzca2Ck724OZ5L0EVrr3eHRNsG/b3xQJLA2hZpVCS99bLAX+hm1IHXDEnzU6by5z/5MIY794/a8NQ==

"@radix-ui/react-use-rect@1.1.1":
  version "1.1.1"
  resolved "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-1.1.1.tgz"
  integrity sha512-QTYuDesS0VtuHNNvMh+CjlKJ4LJickCMUAqjlE3+j8w+RlRpwyX3apEQKGFzbZGdo7XNG1tXa+bQqIE7HIXT2w==
  dependencies:
    "@radix-ui/rect" "1.1.1"

"@radix-ui/react-use-size@1.1.1":
  version "1.1.1"
  resolved "https://registry.npmjs.org/@radix-ui/react-use-size/-/react-use-size-1.1.1.tgz"
  integrity sha512-ewrXRDTAqAXlkl6t/fkXWNAhFX9I+CkKlw6zjEwk86RSPKwZr3xpBRso655aqYafwtnbpHLj6toFzmd6xdVptQ==
  dependencies:
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-visually-hidden@1.2.0":
  version "1.2.0"
  resolved "https://registry.npmjs.org/@radix-ui/react-visually-hidden/-/react-visually-hidden-1.2.0.tgz"
  integrity sha512-rQj0aAWOpCdCMRbI6pLQm8r7S2BM3YhTa0SzOYD55k+hJA8oo9J+H+9wLM9oMlZWOX/wJWPTzfDfmZkf7LvCfg==
  dependencies:
    "@radix-ui/react-primitive" "2.1.0"

"@radix-ui/react-visually-hidden@1.2.2":
  version "1.2.2"
  resolved "https://registry.npmjs.org/@radix-ui/react-visually-hidden/-/react-visually-hidden-1.2.2.tgz"
  integrity sha512-ORCmRUbNiZIv6uV5mhFrhsIKw4UX/N3syZtyqvry61tbGm4JlgQuSn0hk5TwCARsCjkcnuRkSdCE3xfb+ADHew==
  dependencies:
    "@radix-ui/react-primitive" "2.1.2"

"@radix-ui/rect@1.1.1":
  version "1.1.1"
  resolved "https://registry.npmjs.org/@radix-ui/rect/-/rect-1.1.1.tgz"
  integrity sha512-HPwpGIzkl28mWyZqG52jiqDJ12waP11Pa1lGoiyUkIEuMLBP0oeK/C89esbXrxsky5we7dfd8U58nm0SgAWpVw==

"@rollup/plugin-commonjs@28.0.1":
  version "28.0.1"
  resolved "https://registry.npmjs.org/@rollup/plugin-commonjs/-/plugin-commonjs-28.0.1.tgz"
  integrity sha512-+tNWdlWKbpB3WgBN7ijjYkq9X5uhjmcvyjEght4NmH5fAU++zfQzAJ6wumLS+dNcvwEZhKx2Z+skY8m7v0wGSA==
  dependencies:
    "@rollup/pluginutils" "^5.0.1"
    commondir "^1.0.1"
    estree-walker "^2.0.2"
    fdir "^6.2.0"
    is-reference "1.2.1"
    magic-string "^0.30.3"
    picomatch "^4.0.2"

"@rollup/pluginutils@^5.0.1":
  version "5.1.4"
  resolved "https://registry.npmjs.org/@rollup/pluginutils/-/pluginutils-5.1.4.tgz"
  integrity sha512-USm05zrsFxYLPdWWq+K3STlWiT/3ELn3RcV5hJMghpeAIhxfsUIg6mt12CBJBInWMV4VneoV7SfGv8xIwo2qNQ==
  dependencies:
    "@types/estree" "^1.0.0"
    estree-walker "^2.0.2"
    picomatch "^4.0.2"

"@rollup/rollup-darwin-arm64@4.35.0":
  version "4.35.0"
  resolved "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.35.0.tgz"
  integrity sha512-Uk+GjOJR6CY844/q6r5DR/6lkPFOw0hjfOIzVx22THJXMxktXG6CbejseJFznU8vHcEBLpiXKY3/6xc+cBm65Q==

"@rtsao/scc@^1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@rtsao/scc/-/scc-1.1.0.tgz"
  integrity sha512-zt6OdqaDoOnJ1ZYsCYGt9YmWzDXl4vQdKTyJev62gFhRGKdx7mcT54V9KIjg+d2wi9EXsPvAPKe7i7WjfVWB8g==

"@rushstack/eslint-patch@^1.10.3":
  version "1.11.0"
  resolved "https://registry.npmjs.org/@rushstack/eslint-patch/-/eslint-patch-1.11.0.tgz"
  integrity sha512-zxnHvoMQVqewTJr/W4pKjF0bMGiKJv1WX7bSrkl46Hg0QjESbzBROWK0Wg4RphzSOS5Jiy7eFimmM3UgMrMZbQ==

"@segment/analytics-core@1.2.2":
  version "1.2.2"
  resolved "https://registry.npmjs.org/@segment/analytics-core/-/analytics-core-1.2.2.tgz"
  integrity sha512-zVWSDcyh7Rp32xL5v2fuEk2yZxxy+JA93vF1L3EF9XAYLSra/uEHJEswOWieXSdDHVRHes7APORp136usFE/tw==
  dependencies:
    "@lukeed/uuid" "^2.0.0"
    dset "^3.1.2"
    tslib "^2.4.1"

"@segment/analytics.js-video-plugins@^0.2.1":
  version "0.2.1"
  resolved "https://registry.npmjs.org/@segment/analytics.js-video-plugins/-/analytics.js-video-plugins-0.2.1.tgz"
  integrity sha512-lZwCyEXT4aaHBLNK433okEKdxGAuyrVmop4BpQqQSJuRz0DglPZgd9B/XjiiWs1UyOankg2aNYMN3VcS8t4eSQ==
  dependencies:
    unfetch "^3.1.1"

"@segment/facade@^3.4.9":
  version "3.4.10"
  resolved "https://registry.npmjs.org/@segment/facade/-/facade-3.4.10.tgz"
  integrity sha512-xVQBbB/lNvk/u8+ey0kC/+g8pT3l0gCT8O2y9Z+StMMn3KAFAQ9w8xfgef67tJybktOKKU7pQGRPolRM1i1pdA==
  dependencies:
    "@segment/isodate-traverse" "^1.1.1"
    inherits "^2.0.4"
    new-date "^1.0.3"
    obj-case "0.2.1"

"@segment/isodate-traverse@^1.1.1":
  version "1.1.1"
  resolved "https://registry.npmjs.org/@segment/isodate-traverse/-/isodate-traverse-1.1.1.tgz"
  integrity sha512-+G6e1SgAUkcq0EDMi+SRLfT48TNlLPF3QnSgFGVs0V9F3o3fq/woQ2rHFlW20W0yy5NnCUH0QGU3Am2rZy/E3w==
  dependencies:
    "@segment/isodate" "^1.0.3"

"@segment/isodate@^1.0.3", "@segment/isodate@1.0.3":
  version "1.0.3"
  resolved "https://registry.npmjs.org/@segment/isodate/-/isodate-1.0.3.tgz"
  integrity sha512-BtanDuvJqnACFkeeYje7pWULVv8RgZaqKHWwGFnL/g/TH/CcZjkIVTfGDp/MAxmilYHUkrX70SqwnYSTNEaN7A==

"@sentry-internal/browser-utils@9.15.0":
  version "9.15.0"
  resolved "https://registry.npmjs.org/@sentry-internal/browser-utils/-/browser-utils-9.15.0.tgz"
  integrity sha512-tIM+9zXCefkInRiNmBkXKgkamRjEOlAcf768cBKlMWVOatfNrSEB0UEV7qkAYqnQGWkbPkHFMbFJxWptydLODw==
  dependencies:
    "@sentry/core" "9.15.0"

"@sentry-internal/feedback@9.15.0":
  version "9.15.0"
  resolved "https://registry.npmjs.org/@sentry-internal/feedback/-/feedback-9.15.0.tgz"
  integrity sha512-jyN0r57WL8V5ViwZpiNvbIhF9I89jxn6mtIQcyV85EjIXDyzJmeTgxc/FIU0kcDVv6zso3qnGRJUxGK+GvoYZg==
  dependencies:
    "@sentry/core" "9.15.0"

"@sentry-internal/replay-canvas@9.15.0":
  version "9.15.0"
  resolved "https://registry.npmjs.org/@sentry-internal/replay-canvas/-/replay-canvas-9.15.0.tgz"
  integrity sha512-a1/oiXwcW5OmILjD7/R2UEsPQWXJBUr0u388uCKDUGeyXLxBBbIJGS5E8oLwVQLVxhVJrODgxvT19z9OVcbn7g==
  dependencies:
    "@sentry-internal/replay" "9.15.0"
    "@sentry/core" "9.15.0"

"@sentry-internal/replay@9.15.0":
  version "9.15.0"
  resolved "https://registry.npmjs.org/@sentry-internal/replay/-/replay-9.15.0.tgz"
  integrity sha512-lv6ENRmfeBuod6Tr1WgLeF0+wIIXlHWNAGofsaNUvm8UKS7USicFsQWKOZPk4UyjTfrEClPp2vx+o7aUcZS6TQ==
  dependencies:
    "@sentry-internal/browser-utils" "9.15.0"
    "@sentry/core" "9.15.0"

"@sentry/babel-plugin-component-annotate@3.3.1":
  version "3.3.1"
  resolved "https://registry.npmjs.org/@sentry/babel-plugin-component-annotate/-/babel-plugin-component-annotate-3.3.1.tgz"
  integrity sha512-5GOxGT7lZN+I8A7Vp0rWY+726FDKEw8HnFiebe51rQrMbfGfCu2Aw9uSM0nT9OG6xhV6WvGccIcCszTPs4fUZQ==

"@sentry/browser@9.15.0":
  version "9.15.0"
  resolved "https://registry.npmjs.org/@sentry/browser/-/browser-9.15.0.tgz"
  integrity sha512-ppHESKFVQFpAb3rQI2ateDkmMytVcvAWsjZrZ3hF9iEnO3iTIIu32ib5nqQUL4KKXZQovYnDrSlDcdv3ZwX/8Q==
  dependencies:
    "@sentry-internal/browser-utils" "9.15.0"
    "@sentry-internal/feedback" "9.15.0"
    "@sentry-internal/replay" "9.15.0"
    "@sentry-internal/replay-canvas" "9.15.0"
    "@sentry/core" "9.15.0"

"@sentry/bundler-plugin-core@3.3.1":
  version "3.3.1"
  resolved "https://registry.npmjs.org/@sentry/bundler-plugin-core/-/bundler-plugin-core-3.3.1.tgz"
  integrity sha512-Dd6xaWb293j9otEJ1yJqG2Ra6zB49OPzMNdIkdP8wdY+S9UFQE5PyKTyredmPY7hqCc005OrUQZolIIo9Zl13A==
  dependencies:
    "@babel/core" "^7.18.5"
    "@sentry/babel-plugin-component-annotate" "3.3.1"
    "@sentry/cli" "2.42.2"
    dotenv "^16.3.1"
    find-up "^5.0.0"
    glob "^9.3.2"
    magic-string "0.30.8"
    unplugin "1.0.1"

"@sentry/cli-darwin@2.42.2":
  version "2.42.2"
  resolved "https://registry.npmjs.org/@sentry/cli-darwin/-/cli-darwin-2.42.2.tgz"
  integrity sha512-GtJSuxER7Vrp1IpxdUyRZzcckzMnb4N5KTW7sbTwUiwqARRo+wxS+gczYrS8tdgtmXs5XYhzhs+t4d52ITHMIg==

"@sentry/cli@2.42.2":
  version "2.42.2"
  resolved "https://registry.npmjs.org/@sentry/cli/-/cli-2.42.2.tgz"
  integrity sha512-spb7S/RUumCGyiSTg8DlrCX4bivCNmU/A1hcfkwuciTFGu8l5CDc2I6jJWWZw8/0enDGxuj5XujgXvU5tr4bxg==
  dependencies:
    https-proxy-agent "^5.0.0"
    node-fetch "^2.6.7"
    progress "^2.0.3"
    proxy-from-env "^1.1.0"
    which "^2.0.2"
  optionalDependencies:
    "@sentry/cli-darwin" "2.42.2"
    "@sentry/cli-linux-arm" "2.42.2"
    "@sentry/cli-linux-arm64" "2.42.2"
    "@sentry/cli-linux-i686" "2.42.2"
    "@sentry/cli-linux-x64" "2.42.2"
    "@sentry/cli-win32-i686" "2.42.2"
    "@sentry/cli-win32-x64" "2.42.2"

"@sentry/core@9.15.0":
  version "9.15.0"
  resolved "https://registry.npmjs.org/@sentry/core/-/core-9.15.0.tgz"
  integrity sha512-lBmo3bzzaYUesdzc2H5K3fajfXyUNuj5koqyFoCAI8rnt9CBl7SUc/P07+E5eipF8mxgiU3QtkI7ALzRQN8pqQ==

"@sentry/nextjs@^9":
  version "9.15.0"
  resolved "https://registry.npmjs.org/@sentry/nextjs/-/nextjs-9.15.0.tgz"
  integrity sha512-lx/q1Uqe37MtNff8UIBL5G8SaHn48lDlZyQKrsTd+4txBwT2DsAnyR029n/ZQW5bc1/rLM/qebKLy76x+Xq0vA==
  dependencies:
    "@opentelemetry/api" "^1.9.0"
    "@opentelemetry/semantic-conventions" "^1.30.0"
    "@rollup/plugin-commonjs" "28.0.1"
    "@sentry-internal/browser-utils" "9.15.0"
    "@sentry/core" "9.15.0"
    "@sentry/node" "9.15.0"
    "@sentry/opentelemetry" "9.15.0"
    "@sentry/react" "9.15.0"
    "@sentry/vercel-edge" "9.15.0"
    "@sentry/webpack-plugin" "3.3.1"
    chalk "3.0.0"
    resolve "1.22.8"
    rollup "4.35.0"
    stacktrace-parser "^0.1.10"

"@sentry/node@9.15.0":
  version "9.15.0"
  resolved "https://registry.npmjs.org/@sentry/node/-/node-9.15.0.tgz"
  integrity sha512-K0LdJxIzYbbsbiT+1tKgNq6MUHuDs2DggBDcFEp3T+yXVJYN1AyalUli06Kmxq8yvou6hgLwWL4gjIcB1IQySA==
  dependencies:
    "@opentelemetry/api" "^1.9.0"
    "@opentelemetry/context-async-hooks" "^1.30.1"
    "@opentelemetry/core" "^1.30.1"
    "@opentelemetry/instrumentation" "^0.57.2"
    "@opentelemetry/instrumentation-amqplib" "^0.46.1"
    "@opentelemetry/instrumentation-connect" "0.43.1"
    "@opentelemetry/instrumentation-dataloader" "0.16.1"
    "@opentelemetry/instrumentation-express" "0.47.1"
    "@opentelemetry/instrumentation-fastify" "0.44.2"
    "@opentelemetry/instrumentation-fs" "0.19.1"
    "@opentelemetry/instrumentation-generic-pool" "0.43.1"
    "@opentelemetry/instrumentation-graphql" "0.47.1"
    "@opentelemetry/instrumentation-hapi" "0.45.2"
    "@opentelemetry/instrumentation-http" "0.57.2"
    "@opentelemetry/instrumentation-ioredis" "0.47.1"
    "@opentelemetry/instrumentation-kafkajs" "0.7.1"
    "@opentelemetry/instrumentation-knex" "0.44.1"
    "@opentelemetry/instrumentation-koa" "0.47.1"
    "@opentelemetry/instrumentation-lru-memoizer" "0.44.1"
    "@opentelemetry/instrumentation-mongodb" "0.52.0"
    "@opentelemetry/instrumentation-mongoose" "0.46.1"
    "@opentelemetry/instrumentation-mysql" "0.45.1"
    "@opentelemetry/instrumentation-mysql2" "0.45.2"
    "@opentelemetry/instrumentation-pg" "0.51.1"
    "@opentelemetry/instrumentation-redis-4" "0.46.1"
    "@opentelemetry/instrumentation-tedious" "0.18.1"
    "@opentelemetry/instrumentation-undici" "0.10.1"
    "@opentelemetry/resources" "^1.30.1"
    "@opentelemetry/sdk-trace-base" "^1.30.1"
    "@opentelemetry/semantic-conventions" "^1.30.0"
    "@prisma/instrumentation" "6.6.0"
    "@sentry/core" "9.15.0"
    "@sentry/opentelemetry" "9.15.0"
    import-in-the-middle "^1.13.0"

"@sentry/opentelemetry@9.15.0":
  version "9.15.0"
  resolved "https://registry.npmjs.org/@sentry/opentelemetry/-/opentelemetry-9.15.0.tgz"
  integrity sha512-gGOzgSxbuh4B4SlEonL1LFsazmeqL/fn5CIQqRG0UWWxdt6TKAMlj0ThIlGF3jSHW2eXdpvs+4E73uFEaHIqfg==
  dependencies:
    "@sentry/core" "9.15.0"

"@sentry/react@9.15.0":
  version "9.15.0"
  resolved "https://registry.npmjs.org/@sentry/react/-/react-9.15.0.tgz"
  integrity sha512-8nojSjiEd/EWIgoWVfkNIkBGL2yoFZoVMBUTcYlypsMnUHNko2RJItOBqZs5/DRBxuzfBKVt8PF+gkhQOm6mPg==
  dependencies:
    "@sentry/browser" "9.15.0"
    "@sentry/core" "9.15.0"
    hoist-non-react-statics "^3.3.2"

"@sentry/vercel-edge@9.15.0":
  version "9.15.0"
  resolved "https://registry.npmjs.org/@sentry/vercel-edge/-/vercel-edge-9.15.0.tgz"
  integrity sha512-Rfc6pDbHMg5DMIgyZHVIO4IeHgxcH3myPBy9HP1hMLtcEqKL/YS8dK3oQrZoUsNP9chjXkrp4bBeKT/phX3pMg==
  dependencies:
    "@opentelemetry/api" "^1.9.0"
    "@sentry/core" "9.15.0"

"@sentry/webpack-plugin@3.3.1":
  version "3.3.1"
  resolved "https://registry.npmjs.org/@sentry/webpack-plugin/-/webpack-plugin-3.3.1.tgz"
  integrity sha512-AFRnGNUnlIvq3M+ADdfWb+DIXWKK6yYEkVPAyOppkjO+cL/19gjXMdvAwv+CMFts28YCFKF8Kr3pamUiCmwodA==
  dependencies:
    "@sentry/bundler-plugin-core" "3.3.1"
    unplugin "1.0.1"
    uuid "^9.0.0"

"@sinclair/typebox@^0.27.8":
  version "0.27.8"
  resolved "https://registry.npmjs.org/@sinclair/typebox/-/typebox-0.27.8.tgz"
  integrity sha512-+Fj43pSMwJs4KRrH/938Uf+uAELIgVBmQzg/q1YG10djyfA3TnrU8N8XzqCh/okZdszqBQTZf96idMfE5lnwTA==

"@sindresorhus/is@^4.6.0":
  version "4.6.0"
  resolved "https://registry.npmjs.org/@sindresorhus/is/-/is-4.6.0.tgz"
  integrity sha512-t09vSN3MdfsyCHoFcTRCH/iUtG7OJ0CsjzB8cjAmKc/va/kIgeDI/TxsigdncE/4be734m0cvIYwNaV4i2XqAw==

"@sinonjs/commons@^3.0.0":
  version "3.0.1"
  resolved "https://registry.npmjs.org/@sinonjs/commons/-/commons-3.0.1.tgz"
  integrity sha512-K3mCHKQ9sVh8o1C9cxkwxaOmXoAMlDxC1mYyHrjqOWEcBjYr76t96zL2zlj5dUGZ3HSw240X1qgH3Mjf1yJWpQ==
  dependencies:
    type-detect "4.0.8"

"@sinonjs/fake-timers@^10.0.2":
  version "10.3.0"
  resolved "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-10.3.0.tgz"
  integrity sha512-V4BG07kuYSUkTCSBHG8G8TNhM+F19jXFWnQtzj+we8DrkpSBCee9Z3Ms8yiGer/dlmhe35/Xdgyo3/0rQKg7YA==
  dependencies:
    "@sinonjs/commons" "^3.0.0"

"@standard-schema/utils@^0.3.0":
  version "0.3.0"
  resolved "https://registry.npmjs.org/@standard-schema/utils/-/utils-0.3.0.tgz"
  integrity sha512-e7Mew686owMaPJVNNLs55PUvgz371nKgwsc4vxE49zsODpJEnxgxRo2y/OKrqueavXgZNMDVj3DdHFlaSAeU8g==

"@stripe/react-stripe-js@^3.6.0":
  version "3.7.0"
  resolved "https://registry.npmjs.org/@stripe/react-stripe-js/-/react-stripe-js-3.7.0.tgz"
  integrity sha512-PYls/2S9l0FF+2n0wHaEJsEU8x7CmBagiH7zYOsxbBlLIHEsqUIQ4MlIAbV9Zg6xwT8jlYdlRIyBTHmO3yM7kQ==
  dependencies:
    prop-types "^15.7.2"

"@stripe/stripe-js@^7.0.0":
  version "7.3.0"
  resolved "https://registry.npmjs.org/@stripe/stripe-js/-/stripe-js-7.3.0.tgz"
  integrity sha512-xnCyFIEI5SQnQrKkCxVj7nS5fWTZap+zuIGzmmxLMdlmgahFJaihK4zogqE8YyKKTLtrp/EldkEijSgtXsRVDg==

"@supabase/auth-helpers-nextjs@^0.10.0":
  version "0.10.0"
  resolved "https://registry.npmjs.org/@supabase/auth-helpers-nextjs/-/auth-helpers-nextjs-0.10.0.tgz"
  integrity sha512-2dfOGsM4yZt0oS4TPiE7bD4vf7EVz7NRz/IJrV6vLg0GP7sMUx8wndv2euLGq4BjN9lUCpu6DG/uCC8j+ylwPg==
  dependencies:
    "@supabase/auth-helpers-shared" "0.7.0"
    set-cookie-parser "^2.6.0"

"@supabase/auth-helpers-shared@0.7.0":
  version "0.7.0"
  resolved "https://registry.npmjs.org/@supabase/auth-helpers-shared/-/auth-helpers-shared-0.7.0.tgz"
  integrity sha512-FBFf2ei2R7QC+B/5wWkthMha8Ca2bWHAndN+syfuEUUfufv4mLcAgBCcgNg5nJR8L0gZfyuaxgubtOc9aW3Cpg==
  dependencies:
    jose "^4.14.4"

"@supabase/auth-js@2.69.1":
  version "2.69.1"
  resolved "https://registry.npmjs.org/@supabase/auth-js/-/auth-js-2.69.1.tgz"
  integrity sha512-FILtt5WjCNzmReeRLq5wRs3iShwmnWgBvxHfqapC/VoljJl+W8hDAyFmf1NVw3zH+ZjZ05AKxiKxVeb0HNWRMQ==
  dependencies:
    "@supabase/node-fetch" "^2.6.14"

"@supabase/functions-js@2.4.4":
  version "2.4.4"
  resolved "https://registry.npmjs.org/@supabase/functions-js/-/functions-js-2.4.4.tgz"
  integrity sha512-WL2p6r4AXNGwop7iwvul2BvOtuJ1YQy8EbOd0dhG1oN1q8el/BIRSFCFnWAMM/vJJlHWLi4ad22sKbKr9mvjoA==
  dependencies:
    "@supabase/node-fetch" "^2.6.14"

"@supabase/node-fetch@^2.6.14", "@supabase/node-fetch@2.6.15":
  version "2.6.15"
  resolved "https://registry.npmjs.org/@supabase/node-fetch/-/node-fetch-2.6.15.tgz"
  integrity sha512-1ibVeYUacxWYi9i0cf5efil6adJ9WRyZBLivgjs+AUpewx1F3xPi7gLgaASI2SmIQxPoCEjAsLAzKPgMJVgOUQ==
  dependencies:
    whatwg-url "^5.0.0"

"@supabase/postgrest-js@1.19.4":
  version "1.19.4"
  resolved "https://registry.npmjs.org/@supabase/postgrest-js/-/postgrest-js-1.19.4.tgz"
  integrity sha512-O4soKqKtZIW3olqmbXXbKugUtByD2jPa8kL2m2c1oozAO11uCcGrRhkZL0kVxjBLrXHE0mdSkFsMj7jDSfyNpw==
  dependencies:
    "@supabase/node-fetch" "^2.6.14"

"@supabase/realtime-js@2.11.2":
  version "2.11.2"
  resolved "https://registry.npmjs.org/@supabase/realtime-js/-/realtime-js-2.11.2.tgz"
  integrity sha512-u/XeuL2Y0QEhXSoIPZZwR6wMXgB+RQbJzG9VErA3VghVt7uRfSVsjeqd7m5GhX3JR6dM/WRmLbVR8URpDWG4+w==
  dependencies:
    "@supabase/node-fetch" "^2.6.14"
    "@types/phoenix" "^1.5.4"
    "@types/ws" "^8.5.10"
    ws "^8.18.0"

"@supabase/ssr@^0.5.2":
  version "0.5.2"
  resolved "https://registry.npmjs.org/@supabase/ssr/-/ssr-0.5.2.tgz"
  integrity sha512-n3plRhr2Bs8Xun1o4S3k1CDv17iH5QY9YcoEvXX3bxV1/5XSasA0mNXYycFmADIdtdE6BG9MRjP5CGIs8qxC8A==
  dependencies:
    "@types/cookie" "^0.6.0"
    cookie "^0.7.0"

"@supabase/storage-js@2.7.1":
  version "2.7.1"
  resolved "https://registry.npmjs.org/@supabase/storage-js/-/storage-js-2.7.1.tgz"
  integrity sha512-asYHcyDR1fKqrMpytAS1zjyEfvxuOIp1CIXX7ji4lHHcJKqyk+sLl/Vxgm4sN6u8zvuUtae9e4kDxQP2qrwWBA==
  dependencies:
    "@supabase/node-fetch" "^2.6.14"

"@supabase/supabase-js@^2.48.1":
  version "2.49.4"
  resolved "https://registry.npmjs.org/@supabase/supabase-js/-/supabase-js-2.49.4.tgz"
  integrity sha512-jUF0uRUmS8BKt37t01qaZ88H9yV1mbGYnqLeuFWLcdV+x1P4fl0yP9DGtaEhFPZcwSom7u16GkLEH9QJZOqOkw==
  dependencies:
    "@supabase/auth-js" "2.69.1"
    "@supabase/functions-js" "2.4.4"
    "@supabase/node-fetch" "2.6.15"
    "@supabase/postgrest-js" "1.19.4"
    "@supabase/realtime-js" "2.11.2"
    "@supabase/storage-js" "2.7.1"

"@swc/counter@0.1.3":
  version "0.1.3"
  resolved "https://registry.npmjs.org/@swc/counter/-/counter-0.1.3.tgz"
  integrity sha512-e2BR4lsJkkRlKZ/qCHPw9ZaSxc0MVUd7gtbtaB7aMvHeJVYe8sOB8DBZkP2DtISHGSku9sCK6T6cnY0CtXrOCQ==

"@swc/helpers@0.5.15":
  version "0.5.15"
  resolved "https://registry.npmjs.org/@swc/helpers/-/helpers-0.5.15.tgz"
  integrity sha512-JQ5TuMi45Owi4/BIMAJBoSQoOJu12oOk/gADqlcUL9JEdHB8vyjUSsxqeNXnmXHjYKMi2WcYtezGEEhqUI/E2g==
  dependencies:
    tslib "^2.8.0"

"@tanstack/react-virtual@^3.11.2", "@tanstack/react-virtual@^3.13.8":
  version "3.13.8"
  resolved "https://registry.npmjs.org/@tanstack/react-virtual/-/react-virtual-3.13.8.tgz"
  integrity sha512-meS2AanUg50f3FBSNoAdBSRAh8uS0ue01qm7zrw65KGJtiXB9QXfybqZwkh4uFpRv2iX/eu5tjcH5wqUpwYLPg==
  dependencies:
    "@tanstack/virtual-core" "3.13.8"

"@tanstack/virtual-core@3.13.8":
  version "3.13.8"
  resolved "https://registry.npmjs.org/@tanstack/virtual-core/-/virtual-core-3.13.8.tgz"
  integrity sha512-BT6w89Hqy7YKaWewYzmecXQzcJh6HTBbKYJIIkMaNU49DZ06LoTV3z32DWWEdUsgW6n1xTmwTLs4GtWrZC261w==

"@testing-library/dom@^9.0.0":
  version "9.3.4"
  resolved "https://registry.npmjs.org/@testing-library/dom/-/dom-9.3.4.tgz"
  integrity sha512-FlS4ZWlp97iiNWig0Muq8p+3rVDjRiYE+YKGbAqXOu9nwJFFOdL00kFpz42M+4huzYi86vAK1sOOfyOG45muIQ==
  dependencies:
    "@babel/code-frame" "^7.10.4"
    "@babel/runtime" "^7.12.5"
    "@types/aria-query" "^5.0.1"
    aria-query "5.1.3"
    chalk "^4.1.0"
    dom-accessibility-api "^0.5.9"
    lz-string "^1.5.0"
    pretty-format "^27.0.2"

"@testing-library/jest-dom@^6.4.6":
  version "6.6.3"
  resolved "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-6.6.3.tgz"
  integrity sha512-IteBhl4XqYNkM54f4ejhLRJiZNqcSCoXUOG2CPK7qbD322KjQozM4kHQOfkG2oln9b9HTYqs+Sae8vBATubxxA==
  dependencies:
    "@adobe/css-tools" "^4.4.0"
    aria-query "^5.0.0"
    chalk "^3.0.0"
    css.escape "^1.5.1"
    dom-accessibility-api "^0.6.3"
    lodash "^4.17.21"
    redent "^3.0.0"

"@testing-library/react@^14.1.2":
  version "14.3.1"
  resolved "https://registry.npmjs.org/@testing-library/react/-/react-14.3.1.tgz"
  integrity sha512-H99XjUhWQw0lTgyMN05W3xQG1Nh4lq574D8keFf1dDoNTJgp66VbJozRaczoF+wsiaPJNt/TcnfpLGufGxSrZQ==
  dependencies:
    "@babel/runtime" "^7.12.5"
    "@testing-library/dom" "^9.0.0"
    "@types/react-dom" "^18.0.0"

"@testing-library/user-event@^14.5.2":
  version "14.6.1"
  resolved "https://registry.npmjs.org/@testing-library/user-event/-/user-event-14.6.1.tgz"
  integrity sha512-vq7fv0rnt+QTXgPxr5Hjc210p6YKq2kmdziLgnsZGgLJ9e6VAShx1pACLuRjd/AS/sr7phAR58OIIpf0LlmQNw==

"@tootallnate/once@2":
  version "2.0.0"
  resolved "https://registry.npmjs.org/@tootallnate/once/-/once-2.0.0.tgz"
  integrity sha512-XCuKFP5PS55gnMVu3dty8KPatLqUoy/ZYzDzAGCQ8JNFCkLXzmI7vNHCR+XpbZaMWQK/vQubr7PkYq8g470J/A==

"@tsconfig/node10@^1.0.7":
  version "1.0.11"
  resolved "https://registry.npmjs.org/@tsconfig/node10/-/node10-1.0.11.tgz"
  integrity sha512-DcRjDCujK/kCk/cUe8Xz8ZSpm8mS3mNNpta+jGCA6USEDfktlNvm1+IuZ9eTcDbNk41BHwpHHeW+N1lKCz4zOw==

"@tsconfig/node12@^1.0.7":
  version "1.0.11"
  resolved "https://registry.npmjs.org/@tsconfig/node12/-/node12-1.0.11.tgz"
  integrity sha512-cqefuRsh12pWyGsIoBKJA9luFu3mRxCA+ORZvA4ktLSzIuCUtWVxGIuXigEwO5/ywWFMZ2QEGKWvkZG1zDMTag==

"@tsconfig/node14@^1.0.0":
  version "1.0.3"
  resolved "https://registry.npmjs.org/@tsconfig/node14/-/node14-1.0.3.tgz"
  integrity sha512-ysT8mhdixWK6Hw3i1V2AeRqZ5WfXg1G43mqoYlM2nc6388Fq5jcXyr5mRsqViLx/GJYdoL0bfXD8nmF+Zn/Iow==

"@tsconfig/node16@^1.0.2":
  version "1.0.4"
  resolved "https://registry.npmjs.org/@tsconfig/node16/-/node16-1.0.4.tgz"
  integrity sha512-vxhUy4J8lyeyinH7Azl1pdd43GJhZH/tP2weN8TntQblOY+A0XbT8DJk1/oCPuOOyg/Ja757rG0CgHcWC8OfMA==

"@types/aria-query@^5.0.1":
  version "5.0.4"
  resolved "https://registry.npmjs.org/@types/aria-query/-/aria-query-5.0.4.tgz"
  integrity sha512-rfT93uj5s0PRL7EzccGMs3brplhcrghnDoV26NqKhCAS1hVo+WdNsPvE/yb6ilfr5hi2MEk6d5EWJTKdxg8jVw==

"@types/aws-lambda@8.10.147":
  version "8.10.147"
  resolved "https://registry.npmjs.org/@types/aws-lambda/-/aws-lambda-8.10.147.tgz"
  integrity sha512-nD0Z9fNIZcxYX5Mai2CTmFD7wX7UldCkW2ezCF8D1T5hdiLsnTWDGRpfRYntU6VjTdLQjOvyszru7I1c1oCQew==

"@types/babel__core@^7.1.14":
  version "7.20.5"
  resolved "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.20.5.tgz"
  integrity sha512-qoQprZvz5wQFJwMDqeseRXWv3rqMvhgpbXFfVyWhbx9X47POIA6i/+dXefEmZKoAgOaTdaIgNSMqMIU61yRyzA==
  dependencies:
    "@babel/parser" "^7.20.7"
    "@babel/types" "^7.20.7"
    "@types/babel__generator" "*"
    "@types/babel__template" "*"
    "@types/babel__traverse" "*"

"@types/babel__generator@*":
  version "7.27.0"
  resolved "https://registry.npmjs.org/@types/babel__generator/-/babel__generator-7.27.0.tgz"
  integrity sha512-ufFd2Xi92OAVPYsy+P4n7/U7e68fex0+Ee8gSG9KX7eo084CWiQ4sdxktvdl0bOPupXtVJPY19zk6EwWqUQ8lg==
  dependencies:
    "@babel/types" "^7.0.0"

"@types/babel__template@*":
  version "7.4.4"
  resolved "https://registry.npmjs.org/@types/babel__template/-/babel__template-7.4.4.tgz"
  integrity sha512-h/NUaSyG5EyxBIp8YRxo4RMe2/qQgvyowRwVMzhYhBCONbW8PUsg4lkFMrhgZhUe5z3L3MiLDuvyJ/CaPa2A8A==
  dependencies:
    "@babel/parser" "^7.1.0"
    "@babel/types" "^7.0.0"

"@types/babel__traverse@*", "@types/babel__traverse@^7.0.6":
  version "7.20.7"
  resolved "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.20.7.tgz"
  integrity sha512-dkO5fhS7+/oos4ciWxyEyjWe48zmG6wbCheo/G2ZnHx4fs3EU6YC6UM8rk56gAjNJ9P3MTH2jo5jb92/K6wbng==
  dependencies:
    "@babel/types" "^7.20.7"

"@types/bunyan@1.8.11":
  version "1.8.11"
  resolved "https://registry.npmjs.org/@types/bunyan/-/bunyan-1.8.11.tgz"
  integrity sha512-758fRH7umIMk5qt5ELmRMff4mLDlN+xyYzC+dkPTdKwbSkJFvz6xwyScrytPU0QIBbRRwbiE8/BIg8bpajerNQ==
  dependencies:
    "@types/node" "*"

"@types/connect@3.4.38":
  version "3.4.38"
  resolved "https://registry.npmjs.org/@types/connect/-/connect-3.4.38.tgz"
  integrity sha512-K6uROf1LD88uDQqJCktA4yzL1YYAK6NgfsI0v/mTgyPKWsX1CnJ0XPSDhViejru1GcRkLWb8RlzFYJRqGUbaug==
  dependencies:
    "@types/node" "*"

"@types/cookie@^0.6.0":
  version "0.6.0"
  resolved "https://registry.npmjs.org/@types/cookie/-/cookie-0.6.0.tgz"
  integrity sha512-4Kh9a6B2bQciAhf7FSuMRRkUWecJgJu9nPnx3yzpsfXX/c50REIqpHY4C82bXP90qrLtXtkDxTZosYO3UpOwlA==

"@types/debug@^4.0.0":
  version "4.1.12"
  resolved "https://registry.npmjs.org/@types/debug/-/debug-4.1.12.tgz"
  integrity sha512-vIChWdVG3LG1SMxEvI/AK+FWJthlrqlTu7fbrlywTkkaONwk/UAGaULXRlf8vkzFBLVm0zkMdCquhL5aOjhXPQ==
  dependencies:
    "@types/ms" "*"

"@types/diff-match-patch@^1.0.36":
  version "1.0.36"
  resolved "https://registry.npmjs.org/@types/diff-match-patch/-/diff-match-patch-1.0.36.tgz"
  integrity sha512-xFdR6tkm0MWvBfO8xXCSsinYxHcqkQUlcHeSpMC2ukzOb6lwQAfDmW+Qt0AvlGd8HpsS28qKsB+oPeJn9I39jg==

"@types/estree-jsx@^1.0.0":
  version "1.0.5"
  resolved "https://registry.npmjs.org/@types/estree-jsx/-/estree-jsx-1.0.5.tgz"
  integrity sha512-52CcUVNFyfb1A2ALocQw/Dd1BQFNmSdkuC3BkZ6iqhdMfQz7JWOFRuJFloOzjk+6WijU56m9oKXFAXc7o3Towg==
  dependencies:
    "@types/estree" "*"

"@types/estree@*", "@types/estree@^1.0.0", "@types/estree@^1.0.6":
  version "1.0.7"
  resolved "https://registry.npmjs.org/@types/estree/-/estree-1.0.7.tgz"
  integrity sha512-w28IoSUCJpidD/TGviZwwMJckNESJZXFu7NBZ5YJ4mEUnNraUn9Pm8HSZm/jDF1pDWYKspWE7oVphigUPRakIQ==

"@types/estree@1.0.6":
  version "1.0.6"
  resolved "https://registry.npmjs.org/@types/estree/-/estree-1.0.6.tgz"
  integrity sha512-AYnb1nQyY49te+VRAVgmzfcgjYS91mY5P0TKUDCLEM+gNnA+3T6rWITXRLYCpahpqSQbN5cE+gHpnPyXjHWxcw==

"@types/graceful-fs@^4.1.3":
  version "4.1.9"
  resolved "https://registry.npmjs.org/@types/graceful-fs/-/graceful-fs-4.1.9.tgz"
  integrity sha512-olP3sd1qOEe5dXTSaFvQG+02VdRXcdytWLAZsAq1PecU8uqQAhkrnbli7DagjtXKW/Bl7YJbUsa8MPcuc8LHEQ==
  dependencies:
    "@types/node" "*"

"@types/hast@^2.0.0":
  version "2.3.10"
  resolved "https://registry.npmjs.org/@types/hast/-/hast-2.3.10.tgz"
  integrity sha512-McWspRw8xx8J9HurkVBfYj0xKoE25tOFlHGdx4MJ5xORQrMGZNqJhVQWaIbm6Oyla5kYOXtDiopzKRJzEOkwJw==
  dependencies:
    "@types/unist" "^2"

"@types/hast@^3.0.0":
  version "3.0.4"
  resolved "https://registry.npmjs.org/@types/hast/-/hast-3.0.4.tgz"
  integrity sha512-WPs+bbQw5aCj+x6laNGWLH3wviHtoCv/P3+otBhbOhJgG8qtpdAMlTCxLtsTWA7LH1Oh/bFCHsBn0TPS5m30EQ==
  dependencies:
    "@types/unist" "*"

"@types/istanbul-lib-coverage@*", "@types/istanbul-lib-coverage@^2.0.0", "@types/istanbul-lib-coverage@^2.0.1":
  version "2.0.6"
  resolved "https://registry.npmjs.org/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.6.tgz"
  integrity sha512-2QF/t/auWm0lsy8XtKVPG19v3sSOQlJe/YHZgfjb/KBBHOGSV+J2q/S671rcq9uTBrLAXmZpqJiaQbMT+zNU1w==

"@types/istanbul-lib-report@*":
  version "3.0.3"
  resolved "https://registry.npmjs.org/@types/istanbul-lib-report/-/istanbul-lib-report-3.0.3.tgz"
  integrity sha512-NQn7AHQnk/RSLOxrBbGyJM/aVQ+pjj5HCgasFxc0K/KhoATfQ/47AyUl15I2yBUpihjmas+a+VJBOqecrFH+uA==
  dependencies:
    "@types/istanbul-lib-coverage" "*"

"@types/istanbul-reports@^3.0.0":
  version "3.0.4"
  resolved "https://registry.npmjs.org/@types/istanbul-reports/-/istanbul-reports-3.0.4.tgz"
  integrity sha512-pk2B1NWalF9toCRu6gjBzR69syFjP4Od8WRAX+0mmf9lAjCRicLOWc+ZrxZHx/0XRjotgkF9t6iaMJ+aXcOdZQ==
  dependencies:
    "@types/istanbul-lib-report" "*"

"@types/jest@^29.5.12":
  version "29.5.14"
  resolved "https://registry.npmjs.org/@types/jest/-/jest-29.5.14.tgz"
  integrity sha512-ZN+4sdnLUbo8EVvVc2ao0GFW6oVrQRPn4K2lglySj7APvSrgzxHiNNK99us4WDMi57xxA2yggblIAMNhXOotLQ==
  dependencies:
    expect "^29.0.0"
    pretty-format "^29.0.0"

"@types/js-cookie@^2.2.6":
  version "2.2.7"
  resolved "https://registry.npmjs.org/@types/js-cookie/-/js-cookie-2.2.7.tgz"
  integrity sha512-aLkWa0C0vO5b4Sr798E26QgOkss68Un0bLjs7u9qxzPT5CG+8DuNTffWES58YzJs3hrVAOs1wonycqEBqNJubA==

"@types/jsdom@^20.0.0":
  version "20.0.1"
  resolved "https://registry.npmjs.org/@types/jsdom/-/jsdom-20.0.1.tgz"
  integrity sha512-d0r18sZPmMQr1eG35u12FZfhIXNrnsPU/g5wvRKCUf/tOGilKKwYMYGqh33BNR6ba+2gkHw1EUiHoN3mn7E5IQ==
  dependencies:
    "@types/node" "*"
    "@types/tough-cookie" "*"
    parse5 "^7.0.0"

"@types/json-schema@^7.0.15":
  version "7.0.15"
  resolved "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.15.tgz"
  integrity sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==

"@types/json5@^0.0.29":
  version "0.0.29"
  resolved "https://registry.npmjs.org/@types/json5/-/json5-0.0.29.tgz"
  integrity sha512-dRLjCWHYg4oaA77cxO64oO+7JwCwnIzkZPdrrC71jQmQtlhM556pwKo5bUzqvZndkVbeFLIIi+9TC40JNF5hNQ==

"@types/lodash@^4.17.16":
  version "4.17.16"
  resolved "https://registry.npmjs.org/@types/lodash/-/lodash-4.17.16.tgz"
  integrity sha512-HX7Em5NYQAXKW+1T+FiuG27NGwzJfCX3s1GjOa7ujxZa52kjJLOr4FUxT+giF6Tgxv1e+/czV/iTtBw27WTU9g==

"@types/mdast@^4.0.0":
  version "4.0.4"
  resolved "https://registry.npmjs.org/@types/mdast/-/mdast-4.0.4.tgz"
  integrity sha512-kGaNbPh1k7AFzgpud/gMdvIm5xuECykRR+JnWKQno9TAXVa6WIVCGTPvYGekIDL4uwCZQSYbUxNBSb1aUo79oA==
  dependencies:
    "@types/unist" "*"

"@types/memcached@^2.2.6":
  version "2.2.10"
  resolved "https://registry.npmjs.org/@types/memcached/-/memcached-2.2.10.tgz"
  integrity sha512-AM9smvZN55Gzs2wRrqeMHVP7KE8KWgCJO/XL5yCly2xF6EKa4YlbpK+cLSAH4NG/Ah64HrlegmGqW8kYws7Vxg==
  dependencies:
    "@types/node" "*"

"@types/ms@*":
  version "2.1.0"
  resolved "https://registry.npmjs.org/@types/ms/-/ms-2.1.0.tgz"
  integrity sha512-GsCCIZDE/p3i96vtEqx+7dBUGXrc7zeSK3wwPHIaRThS+9OhWIXRqzs4d6k1SVU8g91DrNRWxWUGhp5KXQb2VA==

"@types/mysql@2.15.26":
  version "2.15.26"
  resolved "https://registry.npmjs.org/@types/mysql/-/mysql-2.15.26.tgz"
  integrity sha512-DSLCOXhkvfS5WNNPbfn2KdICAmk8lLc+/PNvnPnF7gOdMZCxopXduqv0OQ13y/yA/zXTSikZZqVgybUxOEg6YQ==
  dependencies:
    "@types/node" "*"

"@types/node-fetch@^2.6.4":
  version "2.6.12"
  resolved "https://registry.npmjs.org/@types/node-fetch/-/node-fetch-2.6.12.tgz"
  integrity sha512-8nneRWKCg3rMtF69nLQJnOYUcbafYeFSjqkw3jCRLsqkWFlHaoQrr5mXmofFGOx3DKn7UfmBMyov8ySvLRVldA==
  dependencies:
    "@types/node" "*"
    form-data "^4.0.0"

"@types/node@*":
  version "22.15.3"
  resolved "https://registry.npmjs.org/@types/node/-/node-22.15.3.tgz"
  integrity sha512-lX7HFZeHf4QG/J7tBZqrCAXwz9J5RD56Y6MpP0eJkka8p+K0RY/yBTW7CYFJ4VGCclxqOLKmiGP5juQc6MKgcw==
  dependencies:
    undici-types "~6.21.0"

"@types/node@^18.11.18":
  version "18.19.87"
  resolved "https://registry.npmjs.org/@types/node/-/node-18.19.87.tgz"
  integrity sha512-OIAAu6ypnVZHmsHCeJ+7CCSub38QNBS9uceMQeg7K5Ur0Jr+wG9wEOEvvMbhp09pxD5czIUy/jND7s7Tb6Nw7A==
  dependencies:
    undici-types "~5.26.4"

"@types/node@^20":
  version "20.17.32"
  resolved "https://registry.npmjs.org/@types/node/-/node-20.17.32.tgz"
  integrity sha512-zeMXFn8zQ+UkjK4ws0RiOC9EWByyW1CcVmLe+2rQocXRsGEDxUCwPEIVgpsGcLHS/P8JkT0oa3839BRABS0oPw==
  dependencies:
    undici-types "~6.19.2"

"@types/node@>=13.7.0":
  version "24.0.3"
  resolved "https://registry.npmjs.org/@types/node/-/node-24.0.3.tgz"
  integrity sha512-R4I/kzCYAdRLzfiCabn9hxWfbuHS573x+r0dJMkkzThEa7pbrcDWK+9zu3e7aBOouf+rQAciqPFMnxwr0aWgKg==
  dependencies:
    undici-types "~7.8.0"

"@types/oracledb@6.5.2":
  version "6.5.2"
  resolved "https://registry.npmjs.org/@types/oracledb/-/oracledb-6.5.2.tgz"
  integrity sha512-kK1eBS/Adeyis+3OlBDMeQQuasIDLUYXsi2T15ccNJ0iyUpQ4xDF7svFu3+bGVrI0CMBUclPciz+lsQR3JX3TQ==
  dependencies:
    "@types/node" "*"

"@types/pg-pool@2.0.6":
  version "2.0.6"
  resolved "https://registry.npmjs.org/@types/pg-pool/-/pg-pool-2.0.6.tgz"
  integrity sha512-TaAUE5rq2VQYxab5Ts7WZhKNmuN78Q6PiFonTDdpbx8a1H0M1vhy3rhiMjl+e2iHmogyMw7jZF4FrE6eJUy5HQ==
  dependencies:
    "@types/pg" "*"

"@types/pg@*", "@types/pg@8.15.1":
  version "8.15.1"
  resolved "https://registry.npmjs.org/@types/pg/-/pg-8.15.1.tgz"
  integrity sha512-YKHrkGWBX5+ivzvOQ66I0fdqsQTsvxqM0AGP2i0XrVZ9DP5VA/deEbTf7VuLPGpY7fJB9uGbkZ6KjVhuHcrTkQ==
  dependencies:
    "@types/node" "*"
    pg-protocol "*"
    pg-types "^4.0.1"

"@types/pg@8.6.1":
  version "8.6.1"
  resolved "https://registry.npmjs.org/@types/pg/-/pg-8.6.1.tgz"
  integrity sha512-1Kc4oAGzAl7uqUStZCDvaLFqZrW9qWSjXOmBfdgyBP5La7Us6Mg4GBvRlSoaZMhQF/zSj1C8CtKMBkoiT8eL8w==
  dependencies:
    "@types/node" "*"
    pg-protocol "*"
    pg-types "^2.2.0"

"@types/phoenix@^1.5.4":
  version "1.6.6"
  resolved "https://registry.npmjs.org/@types/phoenix/-/phoenix-1.6.6.tgz"
  integrity sha512-PIzZZlEppgrpoT2QgbnDU+MMzuR6BbCjllj0bM70lWoejMeNJAxCchxnv7J3XFkI8MpygtRpzXrIlmWUBclP5A==

"@types/prop-types@^15.7.14":
  version "15.7.14"
  resolved "https://registry.npmjs.org/@types/prop-types/-/prop-types-15.7.14.tgz"
  integrity sha512-gNMvNH49DJ7OJYv+KAKn0Xp45p8PLl6zo2YnvDIbTd4J6MER2BmWN49TG7n9LvkyihINxeKW8+3bfS2yDC9dzQ==

"@types/react-dom@^18.0.0":
  version "18.3.7"
  resolved "https://registry.npmjs.org/@types/react-dom/-/react-dom-18.3.7.tgz"
  integrity sha512-MEe3UeoENYVFXzoXEWsvcpg6ZvlrFNlOQ7EOsvhI3CfAXwzPfO8Qwuxd40nepsYKqyyVQnTdEfv68q91yLcKrQ==

"@types/react-dom@^19":
  version "19.1.3"
  resolved "https://registry.npmjs.org/@types/react-dom/-/react-dom-19.1.3.tgz"
  integrity sha512-rJXC08OG0h3W6wDMFxQrZF00Kq6qQvw0djHRdzl3U5DnIERz0MRce3WVc7IS6JYBwtaP/DwYtRRjVlvivNveKg==

"@types/react-syntax-highlighter@^15.5.13":
  version "15.5.13"
  resolved "https://registry.npmjs.org/@types/react-syntax-highlighter/-/react-syntax-highlighter-15.5.13.tgz"
  integrity sha512-uLGJ87j6Sz8UaBAooU0T6lWJ0dBmjZgN1PZTrj05TNql2/XpC6+4HhMT5syIdFUUt+FASfCeLLv4kBygNU+8qA==
  dependencies:
    "@types/react" "*"

"@types/react@*", "@types/react@^19":
  version "19.1.2"
  resolved "https://registry.npmjs.org/@types/react/-/react-19.1.2.tgz"
  integrity sha512-oxLPMytKchWGbnQM9O7D67uPa9paTNxO7jVoNMXgkkErULBPhPARCfkKL9ytcIJJRGjbsVwW4ugJzyFFvm/Tiw==
  dependencies:
    csstype "^3.0.2"

"@types/shimmer@^1.2.0":
  version "1.2.0"
  resolved "https://registry.npmjs.org/@types/shimmer/-/shimmer-1.2.0.tgz"
  integrity sha512-UE7oxhQLLd9gub6JKIAhDq06T0F6FnztwMNRvYgjeQSBeMc1ZG/tA47EwfduvkuQS8apbkM/lpLpWsaCeYsXVg==

"@types/stack-utils@^2.0.0":
  version "2.0.3"
  resolved "https://registry.npmjs.org/@types/stack-utils/-/stack-utils-2.0.3.tgz"
  integrity sha512-9aEbYZ3TbYMznPdcdr3SmIrLXwC/AKZXQeCf9Pgao5CKb8CyHuEX5jzWPTkvregvhRJHcpRO6BFoGW9ycaOkYw==

"@types/tedious@^4.0.14":
  version "4.0.14"
  resolved "https://registry.npmjs.org/@types/tedious/-/tedious-4.0.14.tgz"
  integrity sha512-KHPsfX/FoVbUGbyYvk1q9MMQHLPeRZhRJZdO45Q4YjvFkv4hMNghCWTvy7rdKessBsmtz4euWCWAB6/tVpI1Iw==
  dependencies:
    "@types/node" "*"

"@types/tough-cookie@*":
  version "4.0.5"
  resolved "https://registry.npmjs.org/@types/tough-cookie/-/tough-cookie-4.0.5.tgz"
  integrity sha512-/Ad8+nIOV7Rl++6f1BdKxFSMgmoqEoYbHRpPcx3JEfv8VRsQe9Z4mCXeJBzxs7mbHY/XOZZuXlRNfhpVPbs6ZA==

"@types/unist@*", "@types/unist@^3.0.0":
  version "3.0.3"
  resolved "https://registry.npmjs.org/@types/unist/-/unist-3.0.3.tgz"
  integrity sha512-ko/gIFJRv177XgZsZcBwnqJN5x/Gien8qNOn0D5bQU/zAzVf9Zt3BlcUiLqhV9y4ARk0GbT3tnUiPNgnTXzc/Q==

"@types/unist@^2":
  version "2.0.11"
  resolved "https://registry.npmjs.org/@types/unist/-/unist-2.0.11.tgz"
  integrity sha512-CmBKiL6NNo/OqgmMn95Fk9Whlp2mtvIv+KNpQKN2F4SjvrEesubTRWGYSg+BnWZOnlCaSTU1sMpsBOzgbYhnsA==

"@types/unist@^2.0.0":
  version "2.0.11"
  resolved "https://registry.npmjs.org/@types/unist/-/unist-2.0.11.tgz"
  integrity sha512-CmBKiL6NNo/OqgmMn95Fk9Whlp2mtvIv+KNpQKN2F4SjvrEesubTRWGYSg+BnWZOnlCaSTU1sMpsBOzgbYhnsA==

"@types/ws@^8.5.10":
  version "8.18.1"
  resolved "https://registry.npmjs.org/@types/ws/-/ws-8.18.1.tgz"
  integrity sha512-ThVF6DCVhA8kUGy+aazFQ4kXQ7E1Ty7A3ypFOe0IcJV8O/M511G99AW24irKrW56Wt44yG9+ij8FaqoBGkuBXg==
  dependencies:
    "@types/node" "*"

"@types/yargs-parser@*":
  version "21.0.3"
  resolved "https://registry.npmjs.org/@types/yargs-parser/-/yargs-parser-21.0.3.tgz"
  integrity sha512-I4q9QU9MQv4oEOz4tAHJtNz1cwuLxn2F3xcc2iV5WdqLPpUnj30aUuxt1mAxYTG+oe8CZMV/+6rU4S4gRDzqtQ==

"@types/yargs@^17.0.8":
  version "17.0.33"
  resolved "https://registry.npmjs.org/@types/yargs/-/yargs-17.0.33.tgz"
  integrity sha512-WpxBCKWPLr4xSsHgz511rFJAM+wS28w2zEO1QDNY5zM/S8ok70NNfztH0xwhqKyaK0OHCbN98LDAZuy1ctxDkA==
  dependencies:
    "@types/yargs-parser" "*"

"@typescript-eslint/eslint-plugin@^5.4.2 || ^6.0.0 || ^7.0.0 || ^8.0.0", "@typescript-eslint/eslint-plugin@8.31.1":
  version "8.31.1"
  resolved "https://registry.npmjs.org/@typescript-eslint/eslint-plugin/-/eslint-plugin-8.31.1.tgz"
  integrity sha512-oUlH4h1ABavI4F0Xnl8/fOtML/eu8nI2A1nYd+f+55XI0BLu+RIqKoCiZKNo6DtqZBEQm5aNKA20G3Z5w3R6GQ==
  dependencies:
    "@eslint-community/regexpp" "^4.10.0"
    "@typescript-eslint/scope-manager" "8.31.1"
    "@typescript-eslint/type-utils" "8.31.1"
    "@typescript-eslint/utils" "8.31.1"
    "@typescript-eslint/visitor-keys" "8.31.1"
    graphemer "^1.4.0"
    ignore "^5.3.1"
    natural-compare "^1.4.0"
    ts-api-utils "^2.0.1"

"@typescript-eslint/parser@^5.4.2 || ^6.0.0 || ^7.0.0 || ^8.0.0", "@typescript-eslint/parser@8.31.1":
  version "8.31.1"
  resolved "https://registry.npmjs.org/@typescript-eslint/parser/-/parser-8.31.1.tgz"
  integrity sha512-oU/OtYVydhXnumd0BobL9rkJg7wFJ9bFFPmSmB/bf/XWN85hlViji59ko6bSKBXyseT9V8l+CN1nwmlbiN0G7Q==
  dependencies:
    "@typescript-eslint/scope-manager" "8.31.1"
    "@typescript-eslint/types" "8.31.1"
    "@typescript-eslint/typescript-estree" "8.31.1"
    "@typescript-eslint/visitor-keys" "8.31.1"
    debug "^4.3.4"

"@typescript-eslint/scope-manager@8.31.1":
  version "8.31.1"
  resolved "https://registry.npmjs.org/@typescript-eslint/scope-manager/-/scope-manager-8.31.1.tgz"
  integrity sha512-BMNLOElPxrtNQMIsFHE+3P0Yf1z0dJqV9zLdDxN/xLlWMlXK/ApEsVEKzpizg9oal8bAT5Sc7+ocal7AC1HCVw==
  dependencies:
    "@typescript-eslint/types" "8.31.1"
    "@typescript-eslint/visitor-keys" "8.31.1"

"@typescript-eslint/type-utils@8.31.1":
  version "8.31.1"
  resolved "https://registry.npmjs.org/@typescript-eslint/type-utils/-/type-utils-8.31.1.tgz"
  integrity sha512-fNaT/m9n0+dpSp8G/iOQ05GoHYXbxw81x+yvr7TArTuZuCA6VVKbqWYVZrV5dVagpDTtj/O8k5HBEE/p/HM5LA==
  dependencies:
    "@typescript-eslint/typescript-estree" "8.31.1"
    "@typescript-eslint/utils" "8.31.1"
    debug "^4.3.4"
    ts-api-utils "^2.0.1"

"@typescript-eslint/types@8.31.1":
  version "8.31.1"
  resolved "https://registry.npmjs.org/@typescript-eslint/types/-/types-8.31.1.tgz"
  integrity sha512-SfepaEFUDQYRoA70DD9GtytljBePSj17qPxFHA/h3eg6lPTqGJ5mWOtbXCk1YrVU1cTJRd14nhaXWFu0l2troQ==

"@typescript-eslint/typescript-estree@8.31.1":
  version "8.31.1"
  resolved "https://registry.npmjs.org/@typescript-eslint/typescript-estree/-/typescript-estree-8.31.1.tgz"
  integrity sha512-kaA0ueLe2v7KunYOyWYtlf/QhhZb7+qh4Yw6Ni5kgukMIG+iP773tjgBiLWIXYumWCwEq3nLW+TUywEp8uEeag==
  dependencies:
    "@typescript-eslint/types" "8.31.1"
    "@typescript-eslint/visitor-keys" "8.31.1"
    debug "^4.3.4"
    fast-glob "^3.3.2"
    is-glob "^4.0.3"
    minimatch "^9.0.4"
    semver "^7.6.0"
    ts-api-utils "^2.0.1"

"@typescript-eslint/utils@8.31.1":
  version "8.31.1"
  resolved "https://registry.npmjs.org/@typescript-eslint/utils/-/utils-8.31.1.tgz"
  integrity sha512-2DSI4SNfF5T4oRveQ4nUrSjUqjMND0nLq9rEkz0gfGr3tg0S5KB6DhwR+WZPCjzkZl3cH+4x2ce3EsL50FubjQ==
  dependencies:
    "@eslint-community/eslint-utils" "^4.4.0"
    "@typescript-eslint/scope-manager" "8.31.1"
    "@typescript-eslint/types" "8.31.1"
    "@typescript-eslint/typescript-estree" "8.31.1"

"@typescript-eslint/visitor-keys@8.31.1":
  version "8.31.1"
  resolved "https://registry.npmjs.org/@typescript-eslint/visitor-keys/-/visitor-keys-8.31.1.tgz"
  integrity sha512-I+/rgqOVBn6f0o7NDTmAPWWC6NuqhV174lfYvAm9fUaWeiefLdux9/YI3/nLugEn9L8fcSi0XmpKi/r5u0nmpw==
  dependencies:
    "@typescript-eslint/types" "8.31.1"
    eslint-visitor-keys "^4.2.0"

"@ungap/structured-clone@^1.0.0":
  version "1.3.0"
  resolved "https://registry.npmjs.org/@ungap/structured-clone/-/structured-clone-1.3.0.tgz"
  integrity sha512-WmoN8qaIAo7WTYWbAZuG8PYEhn5fkz7dZrqTBZ7dtt//lL2Gwms1IcnQ5yHqjDfX8Ft5j4YzDM23f87zBfDe9g==

"@unrs/resolver-binding-darwin-arm64@1.7.2":
  version "1.7.2"
  resolved "https://registry.npmjs.org/@unrs/resolver-binding-darwin-arm64/-/resolver-binding-darwin-arm64-1.7.2.tgz"
  integrity sha512-vxtBno4xvowwNmO/ASL0Y45TpHqmNkAaDtz4Jqb+clmcVSSl8XCG/PNFFkGsXXXS6AMjP+ja/TtNCFFa1QwLRg==

"@xobotyi/scrollbar-width@^1.9.5":
  version "1.9.5"
  resolved "https://registry.npmjs.org/@xobotyi/scrollbar-width/-/scrollbar-width-1.9.5.tgz"
  integrity sha512-N8tkAACJx2ww8vFMneJmaAgmjAG1tnVBZJRLRcx061tmsLRZHSEZSLuGWnwPtunsSLvSqXQ2wfp7Mgqg1I+2dQ==

abab@^2.0.6:
  version "2.0.6"
  resolved "https://registry.npmjs.org/abab/-/abab-2.0.6.tgz"
  integrity sha512-j2afSsaIENvHZN2B8GOpF566vZ5WVk5opAiMTvWgaQT8DkbOqsTfvNAvHoRGU2zzP8cPoqys+xHTRDWW8L+/BA==

abort-controller@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/abort-controller/-/abort-controller-3.0.0.tgz"
  integrity sha512-h8lQ8tacZYnR3vNQTgibj+tODHI5/+l06Au2Pcriv/Gmet0eaj4TwWH41sO9wnHDiQsEj19q0drzdWdeAHtweg==
  dependencies:
    event-target-shim "^5.0.0"

accepts@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/accepts/-/accepts-2.0.0.tgz"
  integrity sha512-5cvg6CtKwfgdmVqY1WIiXKc3Q1bkRqGLi+2W/6ao+6Y7gu/RCwRuAhGEzh5B4KlszSuTLgZYuqFqo5bImjNKng==
  dependencies:
    mime-types "^3.0.0"
    negotiator "^1.0.0"

acorn-globals@^7.0.0:
  version "7.0.1"
  resolved "https://registry.npmjs.org/acorn-globals/-/acorn-globals-7.0.1.tgz"
  integrity sha512-umOSDSDrfHbTNPuNpC2NSnnA3LUrqpevPb4T9jRx4MagXNS0rs+gwiTcAvqCRmsD6utzsrzNt+ebm00SNWiC3Q==
  dependencies:
    acorn "^8.1.0"
    acorn-walk "^8.0.2"

acorn-import-attributes@^1.9.5:
  version "1.9.5"
  resolved "https://registry.npmjs.org/acorn-import-attributes/-/acorn-import-attributes-1.9.5.tgz"
  integrity sha512-n02Vykv5uA3eHGM/Z2dQrcD56kL8TyDb2p1+0P83PClMnC/nc+anbQRhIOWnSq4Ke/KvDPrY3C9hDtC/A3eHnQ==

acorn-jsx@^5.3.2:
  version "5.3.2"
  resolved "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-5.3.2.tgz"
  integrity sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==

acorn-walk@^8.0.2, acorn-walk@^8.1.1:
  version "8.3.4"
  resolved "https://registry.npmjs.org/acorn-walk/-/acorn-walk-8.3.4.tgz"
  integrity sha512-ueEepnujpqee2o5aIYnvHU6C0A42MNdsIDeqy5BydrkuC5R1ZuUFnm27EeFJGoEHJQgn3uleRvmTXaJgfXbt4g==
  dependencies:
    acorn "^8.11.0"

acorn@^8.1.0, acorn@^8.11.0, acorn@^8.14.0, acorn@^8.4.1, acorn@^8.8.1:
  version "8.14.1"
  resolved "https://registry.npmjs.org/acorn/-/acorn-8.14.1.tgz"
  integrity sha512-OvQ/2pUDKmgfCg++xsTX1wGxfTaszcHVcTctW4UJB4hibJx2HXxxO5UmVgyjMa+ZDsiaf5wWLXYpRWMmBI0QHg==

agent-base@^7.1.2:
  version "7.1.3"
  resolved "https://registry.npmjs.org/agent-base/-/agent-base-7.1.3.tgz"
  integrity sha512-jRR5wdylq8CkOe6hei19GGZnxM6rBGwFl3Bg0YItGDimvjGtAvdZk4Pu6Cl4u4Igsws4a1fd1Vq3ezrhn4KmFw==

agent-base@6:
  version "6.0.2"
  resolved "https://registry.npmjs.org/agent-base/-/agent-base-6.0.2.tgz"
  integrity sha512-RZNwNclF7+MS/8bDg70amg32dyeZGZxiDuQmZxKLAlQjr3jGyLx+4Kkk58UO7D2QdgFIQCovuSuZESne6RG6XQ==
  dependencies:
    debug "4"

agentkeepalive@^4.2.1:
  version "4.6.0"
  resolved "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-4.6.0.tgz"
  integrity sha512-kja8j7PjmncONqaTsB8fQ+wE2mSU2DJ9D4XKoJ5PFWIdRMa6SLSN1ff4mOr4jCbfRSsxR4keIiySJU0N9T5hIQ==
  dependencies:
    humanize-ms "^1.2.1"

ai@^4.1.45:
  version "4.3.13"
  resolved "https://registry.npmjs.org/ai/-/ai-4.3.13.tgz"
  integrity sha512-cC5HXItuOwGykSMacCPzNp6+NMTxeuTjOenztVgSJhdC9Z4OrzBxwkyeDAf4h1QP938ZFi7IBdq3u4lxVoVmvw==
  dependencies:
    "@ai-sdk/provider" "1.1.3"
    "@ai-sdk/provider-utils" "2.2.7"
    "@ai-sdk/react" "1.2.11"
    "@ai-sdk/ui-utils" "1.2.10"
    "@opentelemetry/api" "1.9.0"
    jsondiffpatch "0.6.0"

ajv@^6.12.4:
  version "6.12.6"
  resolved "https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz"
  integrity sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ansi-escapes@^4.2.1:
  version "4.3.2"
  resolved "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-4.3.2.tgz"
  integrity sha512-gKXj5ALrKWQLsYG9jlTRmR/xKluxHV+Z9QEwNIgCfM1/uwPMCuzVVnh5mwTd+OuBZcwSIMbqssNWRm1lE51QaQ==
  dependencies:
    type-fest "^0.21.3"

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz"
  integrity sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==

ansi-regex@^6.0.1:
  version "6.1.0"
  resolved "https://registry.npmjs.org/ansi-regex/-/ansi-regex-6.1.0.tgz"
  integrity sha512-7HSX4QQb4CspciLpVFwyRe79O3xsIZDDLER21kERQ71oaPodF8jL725AgJMFAYbooIqolJoRLuM81SpeUkpkvA==

ansi-styles@^4.0.0, ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz"
  integrity sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==
  dependencies:
    color-convert "^2.0.1"

ansi-styles@^5.0.0:
  version "5.2.0"
  resolved "https://registry.npmjs.org/ansi-styles/-/ansi-styles-5.2.0.tgz"
  integrity sha512-Cxwpt2SfTzTtXcfOlzGEee8O+c+MmUgGrNiBcXnuWxuFJHe6a5Hz7qwhwe5OgaSYI0IJvkLqWX1ASG+cJOkEiA==

ansi-styles@^6.1.0:
  version "6.2.1"
  resolved "https://registry.npmjs.org/ansi-styles/-/ansi-styles-6.2.1.tgz"
  integrity sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==

any-promise@^1.0.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/any-promise/-/any-promise-1.3.0.tgz"
  integrity sha512-7UvmKalWRt1wgjL1RrGxoSJW/0QZFIegpeGvZG9kjp8vrRu55XTHbwnqq2GpXm9uLbcuhxm3IqX9OB4MZR1b2A==

anymatch@^3.0.3, anymatch@~3.1.2:
  version "3.1.3"
  resolved "https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz"
  integrity sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

arg@^4.1.0:
  version "4.1.3"
  resolved "https://registry.npmjs.org/arg/-/arg-4.1.3.tgz"
  integrity sha512-58S9QDqG0Xx27YwPSt9fJxivjYl432YCwfDMfZ+71RAqUrZef7LrKQZ3LHLOwCS4FLNBplP533Zx895SeOCHvA==

arg@^5.0.2:
  version "5.0.2"
  resolved "https://registry.npmjs.org/arg/-/arg-5.0.2.tgz"
  integrity sha512-PYjyFOLKQ9y57JvQ6QLo8dAgNqswh8M1RMJYdQduT6xbWSgK36P/Z/v+p888pM69jMMfS8Xd8F6I1kQ/I9HUGg==

argparse@^1.0.7:
  version "1.0.10"
  resolved "https://registry.npmjs.org/argparse/-/argparse-1.0.10.tgz"
  integrity sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==
  dependencies:
    sprintf-js "~1.0.2"

argparse@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz"
  integrity sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==

aria-hidden@^1.2.4:
  version "1.2.4"
  resolved "https://registry.npmjs.org/aria-hidden/-/aria-hidden-1.2.4.tgz"
  integrity sha512-y+CcFFwelSXpLZk/7fMB2mUbGtX9lKycf1MWJ7CaTIERyitVlyQx6C+sxcROU2BAJ24OiZyK+8wj2i8AlBoS3A==
  dependencies:
    tslib "^2.0.0"

aria-query@^5.0.0, aria-query@^5.3.2:
  version "5.3.2"
  resolved "https://registry.npmjs.org/aria-query/-/aria-query-5.3.2.tgz"
  integrity sha512-COROpnaoap1E2F000S62r6A60uHZnmlvomhfyT2DlTcrY1OrBKn2UhH7qn5wTC9zMvD0AY7csdPSNwKP+7WiQw==

aria-query@5.1.3:
  version "5.1.3"
  resolved "https://registry.npmjs.org/aria-query/-/aria-query-5.1.3.tgz"
  integrity sha512-R5iJ5lkuHybztUfuOAznmboyjWq8O6sqNqtK7CLOqdydi54VNbORp49mb14KbWgG1QD3JFO9hJdZ+y4KutfdOQ==
  dependencies:
    deep-equal "^2.0.5"

array-buffer-byte-length@^1.0.0, array-buffer-byte-length@^1.0.1, array-buffer-byte-length@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/array-buffer-byte-length/-/array-buffer-byte-length-1.0.2.tgz"
  integrity sha512-LHE+8BuR7RYGDKvnrmcuSq3tDcKv9OFEXQt/HpbZhY7V6h0zlUXutnAD82GiFx9rdieCMjkvtcsPqBwgUl1Iiw==
  dependencies:
    call-bound "^1.0.3"
    is-array-buffer "^3.0.5"

array-includes@^3.1.6, array-includes@^3.1.8:
  version "3.1.8"
  resolved "https://registry.npmjs.org/array-includes/-/array-includes-3.1.8.tgz"
  integrity sha512-itaWrbYbqpGXkGhZPGUulwnhVf5Hpy1xiCFsGqyIGglbBxmG5vSjxQen3/WGOjPpNEv1RtBLKxbmVXm8HpJStQ==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-object-atoms "^1.0.0"
    get-intrinsic "^1.2.4"
    is-string "^1.0.7"

array.prototype.findlast@^1.2.5:
  version "1.2.5"
  resolved "https://registry.npmjs.org/array.prototype.findlast/-/array.prototype.findlast-1.2.5.tgz"
  integrity sha512-CVvd6FHg1Z3POpBLxO6E6zr+rSKEQ9L6rZHAaY7lLfhKsWYUBBOuMs0e9o24oopj6H+geRCX0YJ+TJLBK2eHyQ==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    es-shim-unscopables "^1.0.2"

array.prototype.findlastindex@^1.2.5:
  version "1.2.6"
  resolved "https://registry.npmjs.org/array.prototype.findlastindex/-/array.prototype.findlastindex-1.2.6.tgz"
  integrity sha512-F/TKATkzseUExPlfvmwQKGITM3DGTK+vkAsCZoDc5daVygbJBnjEUCbgkAvVFsgfXfX4YIqZ/27G3k3tdXrTxQ==
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.4"
    define-properties "^1.2.1"
    es-abstract "^1.23.9"
    es-errors "^1.3.0"
    es-object-atoms "^1.1.1"
    es-shim-unscopables "^1.1.0"

array.prototype.flat@^1.3.1, array.prototype.flat@^1.3.2:
  version "1.3.3"
  resolved "https://registry.npmjs.org/array.prototype.flat/-/array.prototype.flat-1.3.3.tgz"
  integrity sha512-rwG/ja1neyLqCuGZ5YYrznA62D4mZXg0i1cIskIUKSiqF3Cje9/wXAls9B9s1Wa2fomMsIv8czB8jZcPmxCXFg==
  dependencies:
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-abstract "^1.23.5"
    es-shim-unscopables "^1.0.2"

array.prototype.flatmap@^1.3.2, array.prototype.flatmap@^1.3.3:
  version "1.3.3"
  resolved "https://registry.npmjs.org/array.prototype.flatmap/-/array.prototype.flatmap-1.3.3.tgz"
  integrity sha512-Y7Wt51eKJSyi80hFrJCePGGNo5ktJCslFuboqJsbf57CCPcm5zztluPlc4/aD8sWsKvlwatezpV4U1efk8kpjg==
  dependencies:
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-abstract "^1.23.5"
    es-shim-unscopables "^1.0.2"

array.prototype.tosorted@^1.1.4:
  version "1.1.4"
  resolved "https://registry.npmjs.org/array.prototype.tosorted/-/array.prototype.tosorted-1.1.4.tgz"
  integrity sha512-p6Fx8B7b7ZhL/gmUsAy0D15WhvDccw3mnGNbZpi3pmeJdxtWsj2jEaI4Y6oo3XiHfzuSgPwKc04MYt6KgvC/wA==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.3"
    es-errors "^1.3.0"
    es-shim-unscopables "^1.0.2"

arraybuffer.prototype.slice@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmjs.org/arraybuffer.prototype.slice/-/arraybuffer.prototype.slice-1.0.4.tgz"
  integrity sha512-BNoCY6SXXPQ7gF2opIP4GBE+Xw7U+pHMYKuzjgCN3GwiaIR09UUeKfheyIry77QtrCBlC0KK0q5/TER/tYh3PQ==
  dependencies:
    array-buffer-byte-length "^1.0.1"
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-abstract "^1.23.5"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.6"
    is-array-buffer "^3.0.4"

ast-types-flow@^0.0.8:
  version "0.0.8"
  resolved "https://registry.npmjs.org/ast-types-flow/-/ast-types-flow-0.0.8.tgz"
  integrity sha512-OH/2E5Fg20h2aPrbe+QL8JZQFko0YZaF+j4mnQ7BGhfavO7OpSLa8a0y9sBwomHdSbkhTS8TQNayBfnW5DwbvQ==

async-function@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/async-function/-/async-function-1.0.0.tgz"
  integrity sha512-hsU18Ae8CDTR6Kgu9DYf0EbCr/a5iGL0rytQDobUcdpYOKokk8LEjVphnXkDkgpi0wYVsqrXuP0bZxJaTqdgoA==

async@^3.2.3:
  version "3.2.6"
  resolved "https://registry.npmjs.org/async/-/async-3.2.6.tgz"
  integrity sha512-htCUDlxyyCLMgaM3xXg0C0LW2xqfuQ6p05pCEIsXuyQ+a1koYKTuBMzRNwmybfLgvJDMd0r1LTn4+E0Ti6C2AA==

asynckit@^0.4.0:
  version "0.4.0"
  resolved "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz"
  integrity sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==

atomic-sleep@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/atomic-sleep/-/atomic-sleep-1.0.0.tgz"
  integrity sha512-kNOjDqAh7px0XWNI+4QbzoiR/nTkHAWNud2uvnJquD1/x5a7EQZMJT0AczqK0Qn67oY/TTQ1LbUKajZpp3I9tQ==

available-typed-arrays@^1.0.7:
  version "1.0.7"
  resolved "https://registry.npmjs.org/available-typed-arrays/-/available-typed-arrays-1.0.7.tgz"
  integrity sha512-wvUjBtSGN7+7SjNpq/9M2Tg350UZD3q62IFZLbRAR1bSMlCo1ZaeW+BJ+D090e4hIIZLBcTDWe4Mh4jvUDajzQ==
  dependencies:
    possible-typed-array-names "^1.0.0"

axe-core@^4.10.0:
  version "4.10.3"
  resolved "https://registry.npmjs.org/axe-core/-/axe-core-4.10.3.tgz"
  integrity sha512-Xm7bpRXnDSX2YE2YFfBk2FnF0ep6tmG7xPh8iHee8MIcrgq762Nkce856dYtJYLkuIoYZvGfTs/PbZhideTcEg==

axobject-query@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/axobject-query/-/axobject-query-4.1.0.tgz"
  integrity sha512-qIj0G9wZbMGNLjLmg1PT6v2mE9AH2zlnADJD/2tC6E00hgmhUOfEB6greHPAfLRSufHqROIUTkw6E+M3lH0PTQ==

babel-jest@^29.7.0:
  version "29.7.0"
  resolved "https://registry.npmjs.org/babel-jest/-/babel-jest-29.7.0.tgz"
  integrity sha512-BrvGY3xZSwEcCzKvKsCi2GgHqDqsYkOP4/by5xCgIwGXQxIEh+8ew3gmrE1y7XRR6LHZIj6yLYnUi/mm2KXKBg==
  dependencies:
    "@jest/transform" "^29.7.0"
    "@types/babel__core" "^7.1.14"
    babel-plugin-istanbul "^6.1.1"
    babel-preset-jest "^29.6.3"
    chalk "^4.0.0"
    graceful-fs "^4.2.9"
    slash "^3.0.0"

babel-plugin-istanbul@^6.1.1:
  version "6.1.1"
  resolved "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-6.1.1.tgz"
  integrity sha512-Y1IQok9821cC9onCx5otgFfRm7Lm+I+wwxOx738M/WLPZ9Q42m4IG5W0FNX8WLL2gYMZo3JkuXIH2DOpWM+qwA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@istanbuljs/load-nyc-config" "^1.0.0"
    "@istanbuljs/schema" "^0.1.2"
    istanbul-lib-instrument "^5.0.4"
    test-exclude "^6.0.0"

babel-plugin-jest-hoist@^29.6.3:
  version "29.6.3"
  resolved "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-29.6.3.tgz"
  integrity sha512-ESAc/RJvGTFEzRwOTT4+lNDk/GNHMkKbNzsvT0qKRfDyyYTskxB5rnU2njIDYVxXCBHHEI1c0YwHob3WaYujOg==
  dependencies:
    "@babel/template" "^7.3.3"
    "@babel/types" "^7.3.3"
    "@types/babel__core" "^7.1.14"
    "@types/babel__traverse" "^7.0.6"

babel-plugin-polyfill-corejs2@^0.4.10:
  version "0.4.13"
  resolved "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.4.13.tgz"
  integrity sha512-3sX/eOms8kd3q2KZ6DAhKPc0dgm525Gqq5NtWKZ7QYYZEv57OQ54KtblzJzH1lQF/eQxO8KjWGIK9IPUJNus5g==
  dependencies:
    "@babel/compat-data" "^7.22.6"
    "@babel/helper-define-polyfill-provider" "^0.6.4"
    semver "^6.3.1"

babel-plugin-polyfill-corejs3@^0.11.0:
  version "0.11.1"
  resolved "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.11.1.tgz"
  integrity sha512-yGCqvBT4rwMczo28xkH/noxJ6MZ4nJfkVYdoDaC/utLtWrXxv27HVrzAeSbqR8SxDsp46n0YF47EbHoixy6rXQ==
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.6.3"
    core-js-compat "^3.40.0"

babel-plugin-polyfill-regenerator@^0.6.1:
  version "0.6.4"
  resolved "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.6.4.tgz"
  integrity sha512-7gD3pRadPrbjhjLyxebmx/WrFYcuSjZ0XbdUujQMZ/fcE9oeewk2U/7PCvez84UeuK3oSjmPZ0Ch0dlupQvGzw==
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.6.4"

babel-preset-current-node-syntax@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/babel-preset-current-node-syntax/-/babel-preset-current-node-syntax-1.1.0.tgz"
  integrity sha512-ldYss8SbBlWva1bs28q78Ju5Zq1F+8BrqBZZ0VFhLBvhh6lCpC2o3gDJi/5DRLs9FgYZCnmPYIVFU4lRXCkyUw==
  dependencies:
    "@babel/plugin-syntax-async-generators" "^7.8.4"
    "@babel/plugin-syntax-bigint" "^7.8.3"
    "@babel/plugin-syntax-class-properties" "^7.12.13"
    "@babel/plugin-syntax-class-static-block" "^7.14.5"
    "@babel/plugin-syntax-import-attributes" "^7.24.7"
    "@babel/plugin-syntax-import-meta" "^7.10.4"
    "@babel/plugin-syntax-json-strings" "^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"
    "@babel/plugin-syntax-top-level-await" "^7.14.5"

babel-preset-jest@^29.6.3:
  version "29.6.3"
  resolved "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-29.6.3.tgz"
  integrity sha512-0B3bhxR6snWXJZtR/RliHTDPRgn1sNHOR0yVtq/IiQFyuOVjFS+wuio/R4gSNkyYmKmJB4wGZv2NZanmKmTnNA==
  dependencies:
    babel-plugin-jest-hoist "^29.6.3"
    babel-preset-current-node-syntax "^1.0.0"

bail@^2.0.0:
  version "2.0.2"
  resolved "https://registry.npmjs.org/bail/-/bail-2.0.2.tgz"
  integrity sha512-0xO6mYd7JB2YesxDKplafRpsiOzPt9V02ddPCLbY1xYGPOX24NTyN50qnUxgCPcSoYMhKpAuBTjQoRZCAkUDRw==

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz"
  integrity sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==

base64-js@^1.5.1:
  version "1.5.1"
  resolved "https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz"
  integrity sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==

bignumber.js@^9.0.0:
  version "9.3.0"
  resolved "https://registry.npmjs.org/bignumber.js/-/bignumber.js-9.3.0.tgz"
  integrity sha512-EM7aMFTXbptt/wZdMlBv2t8IViwQL+h6SLHosp8Yf0dqJMTnY6iL32opnAB6kAdL0SZPuvcAzFr31o0c/R3/RA==

bin-links@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/bin-links/-/bin-links-5.0.0.tgz"
  integrity sha512-sdleLVfCjBtgO5cNjA2HVRvWBJAHs4zwenaCPMNJAJU0yNxpzj80IpjOIimkpkr+mhlA+how5poQtt53PygbHA==
  dependencies:
    cmd-shim "^7.0.0"
    npm-normalize-package-bin "^4.0.0"
    proc-log "^5.0.0"
    read-cmd-shim "^5.0.0"
    write-file-atomic "^6.0.0"

binary-extensions@^2.0.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.3.0.tgz"
  integrity sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==

body-parser@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/body-parser/-/body-parser-2.2.0.tgz"
  integrity sha512-02qvAaxv8tp7fBa/mw1ga98OGm+eCbqzJOKoRt70sLmfEEi+jyBYVTDGfCL/k06/4EMk/z01gCe7HoCH/f2LTg==
  dependencies:
    bytes "^3.1.2"
    content-type "^1.0.5"
    debug "^4.4.0"
    http-errors "^2.0.0"
    iconv-lite "^0.6.3"
    on-finished "^2.4.1"
    qs "^6.14.0"
    raw-body "^3.0.0"
    type-is "^2.0.0"

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz"
  integrity sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

brace-expansion@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/brace-expansion/-/brace-expansion-2.0.1.tgz"
  integrity sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==
  dependencies:
    balanced-match "^1.0.0"

braces@^3.0.3, braces@~3.0.2:
  version "3.0.3"
  resolved "https://registry.npmjs.org/braces/-/braces-3.0.3.tgz"
  integrity sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==
  dependencies:
    fill-range "^7.1.1"

browserslist@^4.24.0, browserslist@^4.24.4:
  version "4.24.5"
  resolved "https://registry.npmjs.org/browserslist/-/browserslist-4.24.5.tgz"
  integrity sha512-FDToo4Wo82hIdgc1CQ+NQD0hEhmpPjrZ3hiUgwgOG6IuTdlpr8jdjyG24P6cNP1yJpTLzS5OcGgSw0xmDU1/Tw==
  dependencies:
    caniuse-lite "^1.0.30001716"
    electron-to-chromium "^1.5.149"
    node-releases "^2.0.19"
    update-browserslist-db "^1.1.3"

bs-logger@^0.2.6:
  version "0.2.6"
  resolved "https://registry.npmjs.org/bs-logger/-/bs-logger-0.2.6.tgz"
  integrity sha512-pd8DCoxmbgc7hyPKOvxtqNcjYoOsABPQdcCUjGp3d42VR2CX1ORhk2A87oqqu5R1kk+76nsxZupkmyd+MVtCog==
  dependencies:
    fast-json-stable-stringify "2.x"

bser@2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/bser/-/bser-2.1.1.tgz"
  integrity sha512-gQxTNE/GAfIIrmHLUE3oJyp5FO6HRBfhjnw4/wMmA63ZGDJnWBmgY/lyQBpnDUkGmAhbSe39tx2d/iTOAfglwQ==
  dependencies:
    node-int64 "^0.4.0"

buffer-from@^1.0.0:
  version "1.1.2"
  resolved "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.2.tgz"
  integrity sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==

busboy@1.6.0:
  version "1.6.0"
  resolved "https://registry.npmjs.org/busboy/-/busboy-1.6.0.tgz"
  integrity sha512-8SFQbg/0hQ9xy3UNTB0YEnsNBbWfhf7RtnzpL7TkBiTBRfrQ9Fxcnz7VJsleJpyp6rVLvXiuORqjlHi5q+PYuA==
  dependencies:
    streamsearch "^1.1.0"

byline@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/byline/-/byline-5.0.0.tgz"
  integrity sha512-s6webAy+R4SR8XVuJWt2V2rGvhnrhxN+9S15GNuTK3wKPOXFF6RNc+8ug2XhH+2s4f+uudG4kUVYmYOQWL2g0Q==

bytes@^3.1.2, bytes@3.1.2:
  version "3.1.2"
  resolved "https://registry.npmjs.org/bytes/-/bytes-3.1.2.tgz"
  integrity sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg==

call-bind-apply-helpers@^1.0.0, call-bind-apply-helpers@^1.0.1, call-bind-apply-helpers@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz"
  integrity sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==
  dependencies:
    es-errors "^1.3.0"
    function-bind "^1.1.2"

call-bind@^1.0.2, call-bind@^1.0.5, call-bind@^1.0.7, call-bind@^1.0.8:
  version "1.0.8"
  resolved "https://registry.npmjs.org/call-bind/-/call-bind-1.0.8.tgz"
  integrity sha512-oKlSFMcMwpUg2ednkhQ454wfWiU/ul3CkJe/PEHcTKuiX6RpbehUiFMXu13HalGZxfUwCQzZG747YXBn1im9ww==
  dependencies:
    call-bind-apply-helpers "^1.0.0"
    es-define-property "^1.0.0"
    get-intrinsic "^1.2.4"
    set-function-length "^1.2.2"

call-bound@^1.0.2, call-bound@^1.0.3, call-bound@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmjs.org/call-bound/-/call-bound-1.0.4.tgz"
  integrity sha512-+ys997U96po4Kx/ABpBCqhA9EuxJaQWDQg7295H4hBphv3IZg0boBKuwYpt4YXp6MZ5AmZQnU/tyMTlRpaSejg==
  dependencies:
    call-bind-apply-helpers "^1.0.2"
    get-intrinsic "^1.3.0"

callsites@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz"
  integrity sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==

camelcase-css@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/camelcase-css/-/camelcase-css-2.0.1.tgz"
  integrity sha512-QOSvevhslijgYwRx6Rv7zKdMF8lbRmx+uQGx2+vDc+KI/eBnsy9kit5aj23AgGu3pa4t9AgwbnXWqS+iOY+2aA==

camelcase@^5.3.1:
  version "5.3.1"
  resolved "https://registry.npmjs.org/camelcase/-/camelcase-5.3.1.tgz"
  integrity sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg==

camelcase@^6.2.0:
  version "6.3.0"
  resolved "https://registry.npmjs.org/camelcase/-/camelcase-6.3.0.tgz"
  integrity sha512-Gmy6FhYlCY7uOElZUSbxo2UCDH8owEk996gkbrpsgGtrJLM3J7jGxl9Ic7Qwwj4ivOE5AWZWRMecDdF7hqGjFA==

caniuse-lite@^1.0.30001579, caniuse-lite@^1.0.30001716:
  version "1.0.30001716"
  resolved "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001716.tgz"
  integrity sha512-49/c1+x3Kwz7ZIWt+4DvK3aMJy9oYXXG6/97JKsnjdCk/6n9vVyWL8NAwVt95Lwt9eigI10Hl782kDfZUUlRXw==

ccount@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/ccount/-/ccount-2.0.1.tgz"
  integrity sha512-eyrF0jiFpY+3drT6383f1qhkbGsLSifNAjA61IUjZjmLCWjItY6LB9ft9YhoDgwfmclB2zhu51Lc7+95b8NRAg==

chalk@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/chalk/-/chalk-3.0.0.tgz"
  integrity sha512-4D3B6Wf41KOYRFdszmDqMCGq5VV/uMAB273JILmO+3jAlh8X4qDtdtgCR3fxtbLEMzSx22QdhnDcJvu2u1fVwg==
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chalk@^4.0.0, chalk@^4.0.2, chalk@^4.1.0:
  version "4.1.2"
  resolved "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz"
  integrity sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chalk@^5.3.0:
  version "5.4.1"
  resolved "https://registry.npmjs.org/chalk/-/chalk-5.4.1.tgz"
  integrity sha512-zgVZuo2WcZgfUEmsn6eO3kINexW8RAE4maiQ8QNs8CtpPCSyMiYsULR3HQYkm3w8FIA3SberyMJMSldGsW+U3w==

chalk@3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/chalk/-/chalk-3.0.0.tgz"
  integrity sha512-4D3B6Wf41KOYRFdszmDqMCGq5VV/uMAB273JILmO+3jAlh8X4qDtdtgCR3fxtbLEMzSx22QdhnDcJvu2u1fVwg==
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

char-regex@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/char-regex/-/char-regex-1.0.2.tgz"
  integrity sha512-kWWXztvZ5SBQV+eRgKFeh8q5sLuZY2+8WUIzlxWVTg+oGwY14qylx1KbKzHd8P6ZYkAg0xyIDU9JMHhyJMZ1jw==

character-entities-html4@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/character-entities-html4/-/character-entities-html4-2.1.0.tgz"
  integrity sha512-1v7fgQRj6hnSwFpq1Eu0ynr/CDEw0rXo2B61qXrLNdHZmPKgb7fqS1a2JwF0rISo9q77jDI8VMEHoApn8qDoZA==

character-entities-legacy@^1.0.0:
  version "1.1.4"
  resolved "https://registry.npmjs.org/character-entities-legacy/-/character-entities-legacy-1.1.4.tgz"
  integrity sha512-3Xnr+7ZFS1uxeiUDvV02wQ+QDbc55o97tIV5zHScSPJpcLm/r0DFPcoY3tYRp+VZukxuMeKgXYmsXQHO05zQeA==

character-entities-legacy@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/character-entities-legacy/-/character-entities-legacy-3.0.0.tgz"
  integrity sha512-RpPp0asT/6ufRm//AJVwpViZbGM/MkjQFxJccQRHmISF/22NBtsHqAWmL+/pmkPWoIUJdWyeVleTl1wydHATVQ==

character-entities@^1.0.0:
  version "1.2.4"
  resolved "https://registry.npmjs.org/character-entities/-/character-entities-1.2.4.tgz"
  integrity sha512-iBMyeEHxfVnIakwOuDXpVkc54HijNgCyQB2w0VfGQThle6NXn50zU6V/u+LDhxHcDUPojn6Kpga3PTAD8W1bQw==

character-entities@^2.0.0:
  version "2.0.2"
  resolved "https://registry.npmjs.org/character-entities/-/character-entities-2.0.2.tgz"
  integrity sha512-shx7oQ0Awen/BRIdkjkvz54PnEEI/EjwXDSIZp86/KKdbafHh1Df/RYGBhn4hbe2+uKC9FnT5UCEdyPz3ai9hQ==

character-reference-invalid@^1.0.0:
  version "1.1.4"
  resolved "https://registry.npmjs.org/character-reference-invalid/-/character-reference-invalid-1.1.4.tgz"
  integrity sha512-mKKUkUbhPpQlCOfIuZkvSEgktjPFIsZKRRbC6KWVEMvlzblj3i3asQv5ODsrwt0N3pHAEvjP8KTQPHkp0+6jOg==

character-reference-invalid@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/character-reference-invalid/-/character-reference-invalid-2.0.1.tgz"
  integrity sha512-iBZ4F4wRbyORVsu0jPV7gXkOsGYjGHPmAyv+HiHG8gi5PtC9KI2j1+v8/tlibRvjoWX027ypmG/n0HtO5t7unw==

chokidar@^3.5.3, chokidar@^3.6.0:
  version "3.6.0"
  resolved "https://registry.npmjs.org/chokidar/-/chokidar-3.6.0.tgz"
  integrity sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

chownr@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/chownr/-/chownr-3.0.0.tgz"
  integrity sha512-+IxzY9BZOQd/XuYPRmrvEVjF/nqj5kgT4kEq7VofrDoM1MxoRjEWkrCC3EtLi59TVawxTAn+orJwFQcrqEN1+g==

ci-info@^3.2.0:
  version "3.9.0"
  resolved "https://registry.npmjs.org/ci-info/-/ci-info-3.9.0.tgz"
  integrity sha512-NIxF55hv4nSqQswkAeiOi1r83xy8JldOFDTWiug55KBu9Jnblncd2U6ViHmYgHf01TPZS77NJBhBMKdWj9HQMQ==

cjs-module-lexer@^1.0.0, cjs-module-lexer@^1.2.2:
  version "1.4.3"
  resolved "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-1.4.3.tgz"
  integrity sha512-9z8TZaGM1pfswYeXrUpzPrkx8UnWYdhJclsiYMm6x/w5+nN+8Tf/LnAgfLGQCm59qAOxU8WwHEq2vNwF6i4j+Q==

class-variance-authority@^0.7.1:
  version "0.7.1"
  resolved "https://registry.npmjs.org/class-variance-authority/-/class-variance-authority-0.7.1.tgz"
  integrity sha512-Ka+9Trutv7G8M6WT6SeiRWz792K5qEqIGEGzXKhAE6xOWAY6pPH8U+9IY3oCMv6kqTmLsv7Xh/2w2RigkePMsg==
  dependencies:
    clsx "^2.1.1"

client-only@0.0.1:
  version "0.0.1"
  resolved "https://registry.npmjs.org/client-only/-/client-only-0.0.1.tgz"
  integrity sha512-IV3Ou0jSMzZrd3pZ48nLkT9DA7Ag1pnPzaiQhpW7c3RbcqqzvzzVu+L8gfqMp/8IM2MQtSiqaCxrrcfu8I8rMA==

cliui@^8.0.1:
  version "8.0.1"
  resolved "https://registry.npmjs.org/cliui/-/cliui-8.0.1.tgz"
  integrity sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.1"
    wrap-ansi "^7.0.0"

clsx@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/clsx/-/clsx-2.1.1.tgz"
  integrity sha512-eYm0QWBtUrBWZWG0d386OGAw16Z995PiOVo2B7bjWSbHedGl5e0ZWaq65kOGgUSNesEIDkB9ISbTg/JK9dhCZA==

cmd-shim@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmjs.org/cmd-shim/-/cmd-shim-7.0.0.tgz"
  integrity sha512-rtpaCbr164TPPh+zFdkWpCyZuKkjpAzODfaZCf/SVJZzJN+4bHQb/LP3Jzq5/+84um3XXY8r548XiWKSborwVw==

cmdk@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/cmdk/-/cmdk-1.1.1.tgz"
  integrity sha512-Vsv7kFaXm+ptHDMZ7izaRsP70GgrW9NBNGswt9OZaVBLlE0SNpDq8eu/VGXyF9r7M0azK3Wy7OlYXsuyYLFzHg==
  dependencies:
    "@radix-ui/react-compose-refs" "^1.1.1"
    "@radix-ui/react-dialog" "^1.1.6"
    "@radix-ui/react-id" "^1.1.0"
    "@radix-ui/react-primitive" "^2.0.2"

co@^4.6.0:
  version "4.6.0"
  resolved "https://registry.npmjs.org/co/-/co-4.6.0.tgz"
  integrity sha512-QVb0dM5HvG+uaxitm8wONl7jltx8dqhfU33DcqtOZcLSVIKSDDLDi7+0LbAKiyI8hD9u42m2YxXSkMGWThaecQ==

collect-v8-coverage@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npmjs.org/collect-v8-coverage/-/collect-v8-coverage-1.0.2.tgz"
  integrity sha512-lHl4d5/ONEbLlJvaJNtsF/Lz+WvB07u2ycqTYbdrq7UypDXailES4valYb2eWiJFxZlVmpGekfqoxQhzyFdT4Q==

color-convert@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz"
  integrity sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==
  dependencies:
    color-name "~1.1.4"

color-name@^1.0.0, color-name@~1.1.4:
  version "1.1.4"
  resolved "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz"
  integrity sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==

color-string@^1.9.0:
  version "1.9.1"
  resolved "https://registry.npmjs.org/color-string/-/color-string-1.9.1.tgz"
  integrity sha512-shrVawQFojnZv6xM40anx4CkoDP+fZsw/ZerEMsW/pyzsRbElpsL/DBVW7q3ExxwusdNXI3lXpuhEZkzs8p5Eg==
  dependencies:
    color-name "^1.0.0"
    simple-swizzle "^0.2.2"

color@^4.2.3:
  version "4.2.3"
  resolved "https://registry.npmjs.org/color/-/color-4.2.3.tgz"
  integrity sha512-1rXeuUUiGGrykh+CeBdu5Ie7OJwinCgQY0bc7GCRxy5xVHy+moaqkpL/jqQq0MtQOeYcrqEz4abc5f0KtU7W4A==
  dependencies:
    color-convert "^2.0.1"
    color-string "^1.9.0"

colorette@^2.0.7:
  version "2.0.20"
  resolved "https://registry.npmjs.org/colorette/-/colorette-2.0.20.tgz"
  integrity sha512-IfEDxwoWIjkeXL1eXcDiow4UbKjhLdq6/EuSVR9GMN7KVH3r9gQ83e73hsz1Nd1T3ijd5xv1wcWRYO+D6kCI2w==

combined-stream@^1.0.8:
  version "1.0.8"
  resolved "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz"
  integrity sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==
  dependencies:
    delayed-stream "~1.0.0"

comma-separated-tokens@^1.0.0:
  version "1.0.8"
  resolved "https://registry.npmjs.org/comma-separated-tokens/-/comma-separated-tokens-1.0.8.tgz"
  integrity sha512-GHuDRO12Sypu2cV70d1dkA2EUmXHgntrzbpvOB+Qy+49ypNfGgFQIC2fhhXbnyrJRynDCAARsT7Ou0M6hirpfw==

comma-separated-tokens@^2.0.0:
  version "2.0.3"
  resolved "https://registry.npmjs.org/comma-separated-tokens/-/comma-separated-tokens-2.0.3.tgz"
  integrity sha512-Fu4hJdvzeylCfQPp9SGWidpzrMs7tTrlu6Vb8XGaRGck8QSNZJJp538Wrb60Lax4fPwR64ViY468OIUTbRlGZg==

commander@^4.0.0:
  version "4.1.1"
  resolved "https://registry.npmjs.org/commander/-/commander-4.1.1.tgz"
  integrity sha512-NOKm8xhkzAjzFx8B2v5OAHT+u5pRQc2UCa2Vq9jYL/31o2wi9mxBA7LIFs3sV5VSC49z6pEhfbMULvShKj26WA==

commondir@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/commondir/-/commondir-1.0.1.tgz"
  integrity sha512-W9pAhw0ja1Edb5GVdIF1mjZw/ASI0AlShXM83UUGe2DVr5TdAPEA1OA8m/g8zWp9x6On7gqufY+FatDbC3MDQg==

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz"
  integrity sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==

content-disposition@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/content-disposition/-/content-disposition-1.0.0.tgz"
  integrity sha512-Au9nRL8VNUut/XSzbQA38+M78dzP4D+eqg3gfJHMIHHYa3bg067xj1KxMUWj+VULbiZMowKngFFbKczUrNJ1mg==
  dependencies:
    safe-buffer "5.2.1"

content-type@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmjs.org/content-type/-/content-type-1.0.5.tgz"
  integrity sha512-nTjqfcBFEipKdXCv4YDQWCfmcLZKm81ldF0pAopTvyrFGVbcR6P/VAAd5G7N+0tTr8QqiU0tFadD6FK4NtJwOA==

convert-source-map@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/convert-source-map/-/convert-source-map-2.0.0.tgz"
  integrity sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==

cookie-signature@^1.2.1:
  version "1.2.2"
  resolved "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.2.2.tgz"
  integrity sha512-D76uU73ulSXrD1UXF4KE2TMxVVwhsnCgfAyTg9k8P6KGZjlXKrOLe4dJQKI3Bxi5wjesZoFXJWElNWBjPZMbhg==

cookie@^0.7.0, cookie@^0.7.1:
  version "0.7.2"
  resolved "https://registry.npmjs.org/cookie/-/cookie-0.7.2.tgz"
  integrity sha512-yki5XnKuf750l50uGTllt6kKILY4nQ1eNIQatoXEByZ5dWgnKqbnqmTrBE5B4N7lrMJKQ2ytWMiTO2o0v6Ew/w==

copy-to-clipboard@^3.3.1:
  version "3.3.3"
  resolved "https://registry.npmjs.org/copy-to-clipboard/-/copy-to-clipboard-3.3.3.tgz"
  integrity sha512-2KV8NhB5JqC3ky0r9PMCAZKbUHSwtEo4CwCs0KXgruG43gX5PMqDEBbVU4OUzw2MuAWUfsuFmWvEKG5QRfSnJA==
  dependencies:
    toggle-selection "^1.0.6"

core-js-compat@^3.40.0:
  version "3.42.0"
  resolved "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.42.0.tgz"
  integrity sha512-bQasjMfyDGyaeWKBIu33lHh9qlSR0MFE/Nmc6nMjf/iU9b3rSMdAYz1Baxrv4lPdGUsTqZudHA4jIGSJy0SWZQ==
  dependencies:
    browserslist "^4.24.4"

cors@^2.8.5:
  version "2.8.5"
  resolved "https://registry.npmjs.org/cors/-/cors-2.8.5.tgz"
  integrity sha512-KIHbLJqu73RGr/hnbrO9uBeixNGuvSQjul/jdFvS/KFSIH1hWVd1ng7zOHx+YrEfInLG7q4n6GHQ9cDtxv/P6g==
  dependencies:
    object-assign "^4"
    vary "^1"

create-jest@^29.7.0:
  version "29.7.0"
  resolved "https://registry.npmjs.org/create-jest/-/create-jest-29.7.0.tgz"
  integrity sha512-Adz2bdH0Vq3F53KEMJOoftQFutWCukm6J24wbPWRO4k1kMY7gS7ds/uoJkNuV8wDCtWWnuwGcJwpWcih+zEW1Q==
  dependencies:
    "@jest/types" "^29.6.3"
    chalk "^4.0.0"
    exit "^0.1.2"
    graceful-fs "^4.2.9"
    jest-config "^29.7.0"
    jest-util "^29.7.0"
    prompts "^2.0.1"

create-require@^1.1.0:
  version "1.1.1"
  resolved "https://registry.npmjs.org/create-require/-/create-require-1.1.1.tgz"
  integrity sha512-dcKFX3jn0MpIaXjisoRvexIJVEKzaq7z2rZKxf+MSr9TkdmHmsU4m2lcLojrj/FHl8mk5VxMmYA+ftRkP/3oKQ==

cross-spawn@^7.0.3, cross-spawn@^7.0.6:
  version "7.0.6"
  resolved "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.6.tgz"
  integrity sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

css-in-js-utils@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/css-in-js-utils/-/css-in-js-utils-3.1.0.tgz"
  integrity sha512-fJAcud6B3rRu+KHYk+Bwf+WFL2MDCJJ1XG9x137tJQ0xYxor7XziQtuGFbWNdqrvF4Tk26O3H73nfVqXt/fW1A==
  dependencies:
    hyphenate-style-name "^1.0.3"

css-tree@^1.1.2:
  version "1.1.3"
  resolved "https://registry.npmjs.org/css-tree/-/css-tree-1.1.3.tgz"
  integrity sha512-tRpdppF7TRazZrjJ6v3stzv93qxRcSsFmW6cX0Zm2NVKpxE1WV1HblnghVv9TreireHkqI/VDEsfolRF1p6y7Q==
  dependencies:
    mdn-data "2.0.14"
    source-map "^0.6.1"

css.escape@^1.5.1:
  version "1.5.1"
  resolved "https://registry.npmjs.org/css.escape/-/css.escape-1.5.1.tgz"
  integrity sha512-YUifsXXuknHlUsmlgyY0PKzgPOr7/FjCePfHNt0jxm83wHZi44VDMQ7/fGNkjY3/jV1MC+1CmZbaHzugyeRtpg==

cssesc@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/cssesc/-/cssesc-3.0.0.tgz"
  integrity sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==

cssom@^0.5.0:
  version "0.5.0"
  resolved "https://registry.npmjs.org/cssom/-/cssom-0.5.0.tgz"
  integrity sha512-iKuQcq+NdHqlAcwUY0o/HL69XQrUaQdMjmStJ8JFmUaiiQErlhrmuigkg/CU4E2J0IyUKUrMAgl36TvN67MqTw==

cssom@~0.3.6:
  version "0.3.8"
  resolved "https://registry.npmjs.org/cssom/-/cssom-0.3.8.tgz"
  integrity sha512-b0tGHbfegbhPJpxpiBPU2sCkigAqtM9O121le6bbOlgyV+NyGyCmVfJ6QW9eRjz8CpNfWEOYBIMIGRYkLwsIYg==

cssstyle@^2.3.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/cssstyle/-/cssstyle-2.3.0.tgz"
  integrity sha512-AZL67abkUzIuvcHqk7c09cezpGNcxUxU4Ioi/05xHk4DQeTkWmGYftIE6ctU6AEt+Gn4n1lDStOtj7FKycP71A==
  dependencies:
    cssom "~0.3.6"

csstype@^3.0.2, csstype@^3.1.2:
  version "3.1.3"
  resolved "https://registry.npmjs.org/csstype/-/csstype-3.1.3.tgz"
  integrity sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==

damerau-levenshtein@^1.0.8:
  version "1.0.8"
  resolved "https://registry.npmjs.org/damerau-levenshtein/-/damerau-levenshtein-1.0.8.tgz"
  integrity sha512-sdQSFB7+llfUcQHUQO3+B8ERRj0Oa4w9POWMI/puGtuf7gFywGmkaLCElnudfTiKZV+NvHqL0ifzdrI8Ro7ESA==

data-uri-to-buffer@^4.0.0:
  version "4.0.1"
  resolved "https://registry.npmjs.org/data-uri-to-buffer/-/data-uri-to-buffer-4.0.1.tgz"
  integrity sha512-0R9ikRb668HB7QDxT1vkpuUBtqc53YyAwMwGeUFKRojY/NWKvdZ+9UYtRfGmhqNbRkTSVpMbmyhXipFFv2cb/A==

data-urls@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npmjs.org/data-urls/-/data-urls-3.0.2.tgz"
  integrity sha512-Jy/tj3ldjZJo63sVAvg6LHt2mHvl4V6AgRAmNDtLdm7faqtsx+aJG42rsyCo9JCoRVKwPFzKlIPx3DIibwSIaQ==
  dependencies:
    abab "^2.0.6"
    whatwg-mimetype "^3.0.0"
    whatwg-url "^11.0.0"

data-view-buffer@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/data-view-buffer/-/data-view-buffer-1.0.2.tgz"
  integrity sha512-EmKO5V3OLXh1rtK2wgXRansaK1/mtVdTUEiEI0W8RkvgT05kfxaH29PliLnpLP73yYO6142Q72QNa8Wx/A5CqQ==
  dependencies:
    call-bound "^1.0.3"
    es-errors "^1.3.0"
    is-data-view "^1.0.2"

data-view-byte-length@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/data-view-byte-length/-/data-view-byte-length-1.0.2.tgz"
  integrity sha512-tuhGbE6CfTM9+5ANGf+oQb72Ky/0+s3xKUpHvShfiz2RxMFgFPjsXuRLBVMtvMs15awe45SRb83D6wH4ew6wlQ==
  dependencies:
    call-bound "^1.0.3"
    es-errors "^1.3.0"
    is-data-view "^1.0.2"

data-view-byte-offset@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/data-view-byte-offset/-/data-view-byte-offset-1.0.1.tgz"
  integrity sha512-BS8PfmtDGnrgYdOonGZQdLZslWIeCGFP9tpan0hi1Co2Zr2NKADsvGYA8XxuG/4UWgJ6Cjtv+YJnB6MM69QGlQ==
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    is-data-view "^1.0.1"

date-fns@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/date-fns/-/date-fns-4.1.0.tgz"
  integrity sha512-Ukq0owbQXxa/U3EGtsdVBkR1w7KOQ5gIBqdH2hkvknzZPYvBxb/aa6E8L7tmjFtkwZBu3UXBbjIgPo/Ez4xaNg==

dateformat@^4.6.3:
  version "4.6.3"
  resolved "https://registry.npmjs.org/dateformat/-/dateformat-4.6.3.tgz"
  integrity sha512-2P0p0pFGzHS5EMnhdxQi7aJN+iMheud0UhG4dlE1DLAlvL8JHjJJTX/CSm4JXwV0Ka5nGk3zC5mcb5bUQUxxMA==

debug@^3.2.7:
  version "3.2.7"
  resolved "https://registry.npmjs.org/debug/-/debug-3.2.7.tgz"
  integrity sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==
  dependencies:
    ms "^2.1.1"

debug@^4.0.0, debug@^4.1.0, debug@^4.1.1, debug@^4.3.1, debug@^4.3.2, debug@^4.3.4, debug@^4.3.5, debug@^4.4.0, debug@4:
  version "4.4.0"
  resolved "https://registry.npmjs.org/debug/-/debug-4.4.0.tgz"
  integrity sha512-6WTZ/IxCY/T6BALoZHaE4ctp9xm+Z5kY/pzYaCHRFeyVhojxlrm+46y68HA6hr0TcwEssoxNiDEUJQjfPZ/RYA==
  dependencies:
    ms "^2.1.3"

decimal.js@^10.4.2:
  version "10.5.0"
  resolved "https://registry.npmjs.org/decimal.js/-/decimal.js-10.5.0.tgz"
  integrity sha512-8vDa8Qxvr/+d94hSh5P3IJwI5t8/c0KsMp+g8bNw9cY2icONa5aPfvKeieW1WlG0WQYwwhJ7mjui2xtiePQSXw==

decode-named-character-reference@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/decode-named-character-reference/-/decode-named-character-reference-1.1.0.tgz"
  integrity sha512-Wy+JTSbFThEOXQIR2L6mxJvEs+veIzpmqD7ynWxMXGpnk3smkHQOp6forLdHsKpAMW9iJpaBBIxz285t1n1C3w==
  dependencies:
    character-entities "^2.0.0"

dedent@^1.0.0:
  version "1.6.0"
  resolved "https://registry.npmjs.org/dedent/-/dedent-1.6.0.tgz"
  integrity sha512-F1Z+5UCFpmQUzJa11agbyPVMbpgT/qA3/SKyJ1jyBgm7dUcUEa8v9JwDkerSQXfakBwFljIxhOJqGkjUwZ9FSA==

deep-equal@^2.0.5:
  version "2.2.3"
  resolved "https://registry.npmjs.org/deep-equal/-/deep-equal-2.2.3.tgz"
  integrity sha512-ZIwpnevOurS8bpT4192sqAowWM76JDKSHYzMLty3BZGSswgq6pBaH3DhCSW5xVAZICZyKdOBPjwww5wfgT/6PA==
  dependencies:
    array-buffer-byte-length "^1.0.0"
    call-bind "^1.0.5"
    es-get-iterator "^1.1.3"
    get-intrinsic "^1.2.2"
    is-arguments "^1.1.1"
    is-array-buffer "^3.0.2"
    is-date-object "^1.0.5"
    is-regex "^1.1.4"
    is-shared-array-buffer "^1.0.2"
    isarray "^2.0.5"
    object-is "^1.1.5"
    object-keys "^1.1.1"
    object.assign "^4.1.4"
    regexp.prototype.flags "^1.5.1"
    side-channel "^1.0.4"
    which-boxed-primitive "^1.0.2"
    which-collection "^1.0.1"
    which-typed-array "^1.1.13"

deep-is@^0.1.3:
  version "0.1.4"
  resolved "https://registry.npmjs.org/deep-is/-/deep-is-0.1.4.tgz"
  integrity sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==

deepmerge@^4.2.2:
  version "4.3.1"
  resolved "https://registry.npmjs.org/deepmerge/-/deepmerge-4.3.1.tgz"
  integrity sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A==

define-data-property@^1.0.1, define-data-property@^1.1.4:
  version "1.1.4"
  resolved "https://registry.npmjs.org/define-data-property/-/define-data-property-1.1.4.tgz"
  integrity sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    gopd "^1.0.1"

define-properties@^1.1.3, define-properties@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/define-properties/-/define-properties-1.2.1.tgz"
  integrity sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg==
  dependencies:
    define-data-property "^1.0.1"
    has-property-descriptors "^1.0.0"
    object-keys "^1.1.1"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz"
  integrity sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==

depd@^2.0.0, depd@2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/depd/-/depd-2.0.0.tgz"
  integrity sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==

dequal@^2.0.0, dequal@^2.0.3:
  version "2.0.3"
  resolved "https://registry.npmjs.org/dequal/-/dequal-2.0.3.tgz"
  integrity sha512-0je+qPKHEMohvfRTCEo3CrPG6cAzAYgmzKyxRiYSSDkS6eGJdyVJm7WaYA5ECaAD9wLB2T4EEeymA5aFVcYXCA==

detect-libc@^2.0.3:
  version "2.0.4"
  resolved "https://registry.npmjs.org/detect-libc/-/detect-libc-2.0.4.tgz"
  integrity sha512-3UDv+G9CsCKO1WKMGw9fwq/SWJYbI0c5Y7LU1AXYoDdbhE2AHQ6N6Nb34sG8Fj7T5APy8qXDCKuuIHd1BR0tVA==

detect-newline@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/detect-newline/-/detect-newline-3.1.0.tgz"
  integrity sha512-TLz+x/vEXm/Y7P7wn1EJFNLxYpUD4TgMosxY6fAVJUnJMbupHBOncxyWUG9OpTaH9EBD7uFI5LfEgmMOc54DsA==

detect-node-es@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/detect-node-es/-/detect-node-es-1.1.0.tgz"
  integrity sha512-ypdmJU/TbBby2Dxibuv7ZLW3Bs1QEmM7nHjEANfohJLvE0XVujisn1qPJcZxg+qDucsr+bP6fLD1rPS3AhJ7EQ==

devlop@^1.0.0, devlop@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/devlop/-/devlop-1.1.0.tgz"
  integrity sha512-RWmIqhcFf1lRYBvNmr7qTNuyCt/7/ns2jbpp1+PalgE/rDQcBT0fioSMUpJ93irlUhC5hrg4cYqe6U+0ImW0rA==
  dependencies:
    dequal "^2.0.0"

didyoumean@^1.2.2:
  version "1.2.2"
  resolved "https://registry.npmjs.org/didyoumean/-/didyoumean-1.2.2.tgz"
  integrity sha512-gxtyfqMg7GKyhQmb056K7M3xszy/myH8w+B4RT+QXBQsvAOdc3XymqDDPHx1BgPgsdAA5SIifona89YtRATDzw==

diff-match-patch@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmjs.org/diff-match-patch/-/diff-match-patch-1.0.5.tgz"
  integrity sha512-IayShXAgj/QMXgB0IWmKx+rOPuGMhqm5w6jvFxmVenXKIzRqTAAsbBPT3kWQeGANj3jGgvcvv4yK6SxqYmikgw==

diff-sequences@^29.6.3:
  version "29.6.3"
  resolved "https://registry.npmjs.org/diff-sequences/-/diff-sequences-29.6.3.tgz"
  integrity sha512-EjePK1srD3P08o2j4f0ExnylqRs5B9tJjcp9t1krH2qRi8CCdsYfwe9JgSLurFBWwq4uOlipzfk5fHNvwFKr8Q==

diff@^4.0.1:
  version "4.0.2"
  resolved "https://registry.npmjs.org/diff/-/diff-4.0.2.tgz"
  integrity sha512-58lmxKSA4BNyLz+HHMUzlOEpg09FV+ev6ZMe3vJihgdxzgcwZ8VoEEPmALCZG9LmqfVoNMMKpttIYTVG6uDY7A==

dlv@^1.1.3:
  version "1.1.3"
  resolved "https://registry.npmjs.org/dlv/-/dlv-1.1.3.tgz"
  integrity sha512-+HlytyjlPKnIG8XuRG8WvmBP8xs8P71y+SKKS6ZXWoEgLuePxtDoUEiH7WkdePWrQ5JBpE6aoVqfZfJUQkjXwA==

doctrine@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/doctrine/-/doctrine-2.1.0.tgz"
  integrity sha512-35mSku4ZXK0vfCuHEDAwt55dg2jNajHZ1odvF+8SSr82EsZY4QmXfuWso8oEd8zRhVObSN18aM0CjSdoBX7zIw==
  dependencies:
    esutils "^2.0.2"

dom-accessibility-api@^0.5.9:
  version "0.5.16"
  resolved "https://registry.npmjs.org/dom-accessibility-api/-/dom-accessibility-api-0.5.16.tgz"
  integrity sha512-X7BJ2yElsnOJ30pZF4uIIDfBEVgF4XEBxL9Bxhy6dnrm5hkzqmsWHGTiHqRiITNhMyFLyAiWndIJP7Z1NTteDg==

dom-accessibility-api@^0.6.3:
  version "0.6.3"
  resolved "https://registry.npmjs.org/dom-accessibility-api/-/dom-accessibility-api-0.6.3.tgz"
  integrity sha512-7ZgogeTnjuHbo+ct10G9Ffp0mif17idi0IyWNVA/wcwcm7NPOD/WEHVP3n7n3MhXqxoIYm8d6MuZohYWIZ4T3w==

domexception@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/domexception/-/domexception-4.0.0.tgz"
  integrity sha512-A2is4PLG+eeSfoTMA95/s4pvAoSo2mKtiM5jlHkAVewmiO8ISFTFKZjH7UAM1Atli/OT/7JHOrJRJiMKUZKYBw==
  dependencies:
    webidl-conversions "^7.0.0"

dotenv@^16.3.1:
  version "16.5.0"
  resolved "https://registry.npmjs.org/dotenv/-/dotenv-16.5.0.tgz"
  integrity sha512-m/C+AwOAr9/W1UOIZUo232ejMNnJAJtYQjUbHoNTBNTJSvqzzDh7vnrei3o3r3m9blf6ZoDkvcw0VmozNRFJxg==

dset@^3.1.2:
  version "3.1.4"
  resolved "https://registry.npmjs.org/dset/-/dset-3.1.4.tgz"
  integrity sha512-2QF/g9/zTaPDc3BjNcVTGoBbXBgYfMTTceLaYcFJ/W9kggFUkhxD/hMEeuLKbugyef9SqAx8cpgwlIP/jinUTA==

dunder-proto@^1.0.0, dunder-proto@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/dunder-proto/-/dunder-proto-1.0.1.tgz"
  integrity sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==
  dependencies:
    call-bind-apply-helpers "^1.0.1"
    es-errors "^1.3.0"
    gopd "^1.2.0"

eastasianwidth@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/eastasianwidth/-/eastasianwidth-0.2.0.tgz"
  integrity sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==

ee-first@1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/ee-first/-/ee-first-1.1.1.tgz"
  integrity sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow==

ejs@^3.1.10:
  version "3.1.10"
  resolved "https://registry.npmjs.org/ejs/-/ejs-3.1.10.tgz"
  integrity sha512-UeJmFfOrAQS8OJWPZ4qtgHyWExa088/MtK5UEyoJGFH67cDEXkZSviOiKRCZ4Xij0zxI3JECgYs3oKx+AizQBA==
  dependencies:
    jake "^10.8.5"

electron-to-chromium@^1.5.149:
  version "1.5.149"
  resolved "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.5.149.tgz"
  integrity sha512-UyiO82eb9dVOx8YO3ajDf9jz2kKyt98DEITRdeLPstOEuTlLzDA4Gyq5K9he71TQziU5jUVu2OAu5N48HmQiyQ==

emittery@^0.13.1:
  version "0.13.1"
  resolved "https://registry.npmjs.org/emittery/-/emittery-0.13.1.tgz"
  integrity sha512-DeWwawk6r5yR9jFgnDKYt4sLS0LmHJJi3ZOnb5/JdbYwj3nW+FxQnHIjhBKz8YLC7oRNPVM9NQ47I3CVx34eqQ==

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz"
  integrity sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==

emoji-regex@^9.2.2:
  version "9.2.2"
  resolved "https://registry.npmjs.org/emoji-regex/-/emoji-regex-9.2.2.tgz"
  integrity sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==

emojilib@^2.4.0:
  version "2.4.0"
  resolved "https://registry.npmjs.org/emojilib/-/emojilib-2.4.0.tgz"
  integrity sha512-5U0rVMU5Y2n2+ykNLQqMoqklN9ICBT/KsvC1Gz6vqHbz2AXXGkG+Pm5rMWk/8Vjrr/mY9985Hi8DYzn1F09Nyw==

encodeurl@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/encodeurl/-/encodeurl-2.0.0.tgz"
  integrity sha512-Q0n9HRi4m6JuGIV1eFlmvJB7ZEVxu93IrMyiMsGC0lrMJMWzRgx6WGquyfQgZVb31vhGgXnfmPNNXmxnOkRBrg==

end-of-stream@^1.1.0:
  version "1.4.4"
  resolved "https://registry.npmjs.org/end-of-stream/-/end-of-stream-1.4.4.tgz"
  integrity sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q==
  dependencies:
    once "^1.4.0"

entities@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmjs.org/entities/-/entities-6.0.0.tgz"
  integrity sha512-aKstq2TDOndCn4diEyp9Uq/Flu2i1GlLkc6XIDQSDMuaFE3OPW5OphLCyQ5SpSJZTb4reN+kTcYru5yIfXoRPw==

error-ex@^1.3.1:
  version "1.3.2"
  resolved "https://registry.npmjs.org/error-ex/-/error-ex-1.3.2.tgz"
  integrity sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==
  dependencies:
    is-arrayish "^0.2.1"

error-stack-parser@^2.0.3, error-stack-parser@^2.0.6:
  version "2.1.4"
  resolved "https://registry.npmjs.org/error-stack-parser/-/error-stack-parser-2.1.4.tgz"
  integrity sha512-Sk5V6wVazPhq5MhpO+AUxJn5x7XSXGl1R93Vn7i+zS15KDVxQijejNCrz8340/2bgLBjR9GtEG8ZVKONDjcqGQ==
  dependencies:
    stackframe "^1.3.4"

es-abstract@^1.17.5, es-abstract@^1.23.2, es-abstract@^1.23.3, es-abstract@^1.23.5, es-abstract@^1.23.6, es-abstract@^1.23.9:
  version "1.23.9"
  resolved "https://registry.npmjs.org/es-abstract/-/es-abstract-1.23.9.tgz"
  integrity sha512-py07lI0wjxAC/DcfK1S6G7iANonniZwTISvdPzk9hzeH0IZIshbuuFxLIU96OyF89Yb9hiqWn8M/bY83KY5vzA==
  dependencies:
    array-buffer-byte-length "^1.0.2"
    arraybuffer.prototype.slice "^1.0.4"
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    data-view-buffer "^1.0.2"
    data-view-byte-length "^1.0.2"
    data-view-byte-offset "^1.0.1"
    es-define-property "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    es-set-tostringtag "^2.1.0"
    es-to-primitive "^1.3.0"
    function.prototype.name "^1.1.8"
    get-intrinsic "^1.2.7"
    get-proto "^1.0.0"
    get-symbol-description "^1.1.0"
    globalthis "^1.0.4"
    gopd "^1.2.0"
    has-property-descriptors "^1.0.2"
    has-proto "^1.2.0"
    has-symbols "^1.1.0"
    hasown "^2.0.2"
    internal-slot "^1.1.0"
    is-array-buffer "^3.0.5"
    is-callable "^1.2.7"
    is-data-view "^1.0.2"
    is-regex "^1.2.1"
    is-shared-array-buffer "^1.0.4"
    is-string "^1.1.1"
    is-typed-array "^1.1.15"
    is-weakref "^1.1.0"
    math-intrinsics "^1.1.0"
    object-inspect "^1.13.3"
    object-keys "^1.1.1"
    object.assign "^4.1.7"
    own-keys "^1.0.1"
    regexp.prototype.flags "^1.5.3"
    safe-array-concat "^1.1.3"
    safe-push-apply "^1.0.0"
    safe-regex-test "^1.1.0"
    set-proto "^1.0.0"
    string.prototype.trim "^1.2.10"
    string.prototype.trimend "^1.0.9"
    string.prototype.trimstart "^1.0.8"
    typed-array-buffer "^1.0.3"
    typed-array-byte-length "^1.0.3"
    typed-array-byte-offset "^1.0.4"
    typed-array-length "^1.0.7"
    unbox-primitive "^1.1.0"
    which-typed-array "^1.1.18"

es-define-property@^1.0.0, es-define-property@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.1.tgz"
  integrity sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==

es-errors@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/es-errors/-/es-errors-1.3.0.tgz"
  integrity sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==

es-get-iterator@^1.1.3:
  version "1.1.3"
  resolved "https://registry.npmjs.org/es-get-iterator/-/es-get-iterator-1.1.3.tgz"
  integrity sha512-sPZmqHBe6JIiTfN5q2pEi//TwxmAFHwj/XEuYjTuse78i8KxaqMTTzxPoFKuzRpDpTJ+0NAbpfenkmH2rePtuw==
  dependencies:
    call-bind "^1.0.2"
    get-intrinsic "^1.1.3"
    has-symbols "^1.0.3"
    is-arguments "^1.1.1"
    is-map "^2.0.2"
    is-set "^2.0.2"
    is-string "^1.0.7"
    isarray "^2.0.5"
    stop-iteration-iterator "^1.0.0"

es-iterator-helpers@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/es-iterator-helpers/-/es-iterator-helpers-1.2.1.tgz"
  integrity sha512-uDn+FE1yrDzyC0pCo961B2IHbdM8y/ACZsKD4dG6WqrjV53BADjwa7D+1aom2rsNVfLyDgU/eigvlJGJ08OQ4w==
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    es-abstract "^1.23.6"
    es-errors "^1.3.0"
    es-set-tostringtag "^2.0.3"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.6"
    globalthis "^1.0.4"
    gopd "^1.2.0"
    has-property-descriptors "^1.0.2"
    has-proto "^1.2.0"
    has-symbols "^1.1.0"
    internal-slot "^1.1.0"
    iterator.prototype "^1.1.4"
    safe-array-concat "^1.1.3"

es-object-atoms@^1.0.0, es-object-atoms@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.1.1.tgz"
  integrity sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==
  dependencies:
    es-errors "^1.3.0"

es-set-tostringtag@^2.0.3, es-set-tostringtag@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/es-set-tostringtag/-/es-set-tostringtag-2.1.0.tgz"
  integrity sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA==
  dependencies:
    es-errors "^1.3.0"
    get-intrinsic "^1.2.6"
    has-tostringtag "^1.0.2"
    hasown "^2.0.2"

es-shim-unscopables@^1.0.2, es-shim-unscopables@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/es-shim-unscopables/-/es-shim-unscopables-1.1.0.tgz"
  integrity sha512-d9T8ucsEhh8Bi1woXCf+TIKDIROLG5WCkxg8geBCbvk22kzwC5G2OnXVMO6FUsvQlgUUXQ2itephWDLqDzbeCw==
  dependencies:
    hasown "^2.0.2"

es-to-primitive@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/es-to-primitive/-/es-to-primitive-1.3.0.tgz"
  integrity sha512-w+5mJ3GuFL+NjVtJlvydShqE1eN3h3PbI7/5LAsYJP/2qtuMXjfL2LpHSRqo4b4eSF5K/DH1JXKUAHSB2UW50g==
  dependencies:
    is-callable "^1.2.7"
    is-date-object "^1.0.5"
    is-symbol "^1.0.4"

escalade@^3.1.1, escalade@^3.2.0:
  version "3.2.0"
  resolved "https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz"
  integrity sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==

escape-html@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/escape-html/-/escape-html-1.0.3.tgz"
  integrity sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow==

escape-string-regexp@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-2.0.0.tgz"
  integrity sha512-UpzcLCXolUWcNu5HtVMHYdXJjArjsF9C0aNnquZYY4uW/Vu0miy5YoWvbV345HauVvcAUnpRuhMMcqTcGOY2+w==

escape-string-regexp@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz"
  integrity sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==

escape-string-regexp@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-5.0.0.tgz"
  integrity sha512-/veY75JbMK4j1yjvuUxuVsiS/hr/4iHs9FTT6cgTexxdE0Ly/glccBAkloH/DofkjRbZU3bnoj38mOmhkZ0lHw==

escodegen@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/escodegen/-/escodegen-2.1.0.tgz"
  integrity sha512-2NlIDTwUWJN0mRPQOdtQBzbUHvdGY2P1VXSyU83Q3xKxM7WHX2Ql8dKq782Q9TgQUNOLEzEYu9bzLNj1q88I5w==
  dependencies:
    esprima "^4.0.1"
    estraverse "^5.2.0"
    esutils "^2.0.2"
  optionalDependencies:
    source-map "~0.6.1"

eslint-config-next@15.1.7:
  version "15.1.7"
  resolved "https://registry.npmjs.org/eslint-config-next/-/eslint-config-next-15.1.7.tgz"
  integrity sha512-zXoMnYUIy3XHaAoOhrcYkT9UQWvXqWju2K7NNsmb5wd/7XESDwof61eUdW4QhERr3eJ9Ko/vnXqIrj8kk/drYw==
  dependencies:
    "@next/eslint-plugin-next" "15.1.7"
    "@rushstack/eslint-patch" "^1.10.3"
    "@typescript-eslint/eslint-plugin" "^5.4.2 || ^6.0.0 || ^7.0.0 || ^8.0.0"
    "@typescript-eslint/parser" "^5.4.2 || ^6.0.0 || ^7.0.0 || ^8.0.0"
    eslint-import-resolver-node "^0.3.6"
    eslint-import-resolver-typescript "^3.5.2"
    eslint-plugin-import "^2.31.0"
    eslint-plugin-jsx-a11y "^6.10.0"
    eslint-plugin-react "^7.37.0"
    eslint-plugin-react-hooks "^5.0.0"

eslint-import-resolver-node@^0.3.6, eslint-import-resolver-node@^0.3.9:
  version "0.3.9"
  resolved "https://registry.npmjs.org/eslint-import-resolver-node/-/eslint-import-resolver-node-0.3.9.tgz"
  integrity sha512-WFj2isz22JahUv+B788TlO3N6zL3nNJGU8CcZbPZvVEkBPaJdCV4vy5wyghty5ROFbCRnm132v8BScu5/1BQ8g==
  dependencies:
    debug "^3.2.7"
    is-core-module "^2.13.0"
    resolve "^1.22.4"

eslint-import-resolver-typescript@^3.5.2:
  version "3.10.1"
  resolved "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-3.10.1.tgz"
  integrity sha512-A1rHYb06zjMGAxdLSkN2fXPBwuSaQ0iO5M/hdyS0Ajj1VBaRp0sPD3dn1FhME3c/JluGFbwSxyCfqdSbtQLAHQ==
  dependencies:
    "@nolyfill/is-core-module" "1.0.39"
    debug "^4.4.0"
    get-tsconfig "^4.10.0"
    is-bun-module "^2.0.0"
    stable-hash "^0.0.5"
    tinyglobby "^0.2.13"
    unrs-resolver "^1.6.2"

eslint-module-utils@^2.12.0:
  version "2.12.0"
  resolved "https://registry.npmjs.org/eslint-module-utils/-/eslint-module-utils-2.12.0.tgz"
  integrity sha512-wALZ0HFoytlyh/1+4wuZ9FJCD/leWHQzzrxJ8+rebyReSLk7LApMyd3WJaLVoN+D5+WIdJyDK1c6JnE65V4Zyg==
  dependencies:
    debug "^3.2.7"

eslint-plugin-import@^2.31.0:
  version "2.31.0"
  resolved "https://registry.npmjs.org/eslint-plugin-import/-/eslint-plugin-import-2.31.0.tgz"
  integrity sha512-ixmkI62Rbc2/w8Vfxyh1jQRTdRTF52VxwRVHl/ykPAmqG+Nb7/kNn+byLP0LxPgI7zWA16Jt82SybJInmMia3A==
  dependencies:
    "@rtsao/scc" "^1.1.0"
    array-includes "^3.1.8"
    array.prototype.findlastindex "^1.2.5"
    array.prototype.flat "^1.3.2"
    array.prototype.flatmap "^1.3.2"
    debug "^3.2.7"
    doctrine "^2.1.0"
    eslint-import-resolver-node "^0.3.9"
    eslint-module-utils "^2.12.0"
    hasown "^2.0.2"
    is-core-module "^2.15.1"
    is-glob "^4.0.3"
    minimatch "^3.1.2"
    object.fromentries "^2.0.8"
    object.groupby "^1.0.3"
    object.values "^1.2.0"
    semver "^6.3.1"
    string.prototype.trimend "^1.0.8"
    tsconfig-paths "^3.15.0"

eslint-plugin-jsx-a11y@^6.10.0:
  version "6.10.2"
  resolved "https://registry.npmjs.org/eslint-plugin-jsx-a11y/-/eslint-plugin-jsx-a11y-6.10.2.tgz"
  integrity sha512-scB3nz4WmG75pV8+3eRUQOHZlNSUhFNq37xnpgRkCCELU3XMvXAxLk1eqWWyE22Ki4Q01Fnsw9BA3cJHDPgn2Q==
  dependencies:
    aria-query "^5.3.2"
    array-includes "^3.1.8"
    array.prototype.flatmap "^1.3.2"
    ast-types-flow "^0.0.8"
    axe-core "^4.10.0"
    axobject-query "^4.1.0"
    damerau-levenshtein "^1.0.8"
    emoji-regex "^9.2.2"
    hasown "^2.0.2"
    jsx-ast-utils "^3.3.5"
    language-tags "^1.0.9"
    minimatch "^3.1.2"
    object.fromentries "^2.0.8"
    safe-regex-test "^1.0.3"
    string.prototype.includes "^2.0.1"

eslint-plugin-react-hooks@^5.0.0:
  version "5.2.0"
  resolved "https://registry.npmjs.org/eslint-plugin-react-hooks/-/eslint-plugin-react-hooks-5.2.0.tgz"
  integrity sha512-+f15FfK64YQwZdJNELETdn5ibXEUQmW1DZL6KXhNnc2heoy/sg9VJJeT7n8TlMWouzWqSWavFkIhHyIbIAEapg==

eslint-plugin-react@^7.37.0, eslint-plugin-react@^7.37.4:
  version "7.37.5"
  resolved "https://registry.npmjs.org/eslint-plugin-react/-/eslint-plugin-react-7.37.5.tgz"
  integrity sha512-Qteup0SqU15kdocexFNAJMvCJEfa2xUKNV4CC1xsVMrIIqEy3SQ/rqyxCWNzfrd3/ldy6HMlD2e0JDVpDg2qIA==
  dependencies:
    array-includes "^3.1.8"
    array.prototype.findlast "^1.2.5"
    array.prototype.flatmap "^1.3.3"
    array.prototype.tosorted "^1.1.4"
    doctrine "^2.1.0"
    es-iterator-helpers "^1.2.1"
    estraverse "^5.3.0"
    hasown "^2.0.2"
    jsx-ast-utils "^2.4.1 || ^3.0.0"
    minimatch "^3.1.2"
    object.entries "^1.1.9"
    object.fromentries "^2.0.8"
    object.values "^1.2.1"
    prop-types "^15.8.1"
    resolve "^2.0.0-next.5"
    semver "^6.3.1"
    string.prototype.matchall "^4.0.12"
    string.prototype.repeat "^1.0.0"

eslint-scope@^8.3.0:
  version "8.3.0"
  resolved "https://registry.npmjs.org/eslint-scope/-/eslint-scope-8.3.0.tgz"
  integrity sha512-pUNxi75F8MJ/GdeKtVLSbYg4ZI34J6C0C7sbL4YOp2exGwen7ZsuBqKzUhXd0qMQ362yET3z+uPwKeg/0C2XCQ==
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^5.2.0"

eslint-visitor-keys@^3.4.3:
  version "3.4.3"
  resolved "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz"
  integrity sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag==

eslint-visitor-keys@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-4.2.0.tgz"
  integrity sha512-UyLnSehNt62FFhSwjZlHmeokpRK59rcz29j+F1/aDgbkbRTk7wIc9XzdoasMUbRNKDM0qQt/+BJ4BrpFeABemw==

eslint@^9.22.0:
  version "9.26.0"
  resolved "https://registry.npmjs.org/eslint/-/eslint-9.26.0.tgz"
  integrity sha512-Hx0MOjPh6uK9oq9nVsATZKE/Wlbai7KFjfCuw9UHaguDW3x+HF0O5nIi3ud39TWgrTjTO5nHxmL3R1eANinWHQ==
  dependencies:
    "@eslint-community/eslint-utils" "^4.2.0"
    "@eslint-community/regexpp" "^4.12.1"
    "@eslint/config-array" "^0.20.0"
    "@eslint/config-helpers" "^0.2.1"
    "@eslint/core" "^0.13.0"
    "@eslint/eslintrc" "^3.3.1"
    "@eslint/js" "9.26.0"
    "@eslint/plugin-kit" "^0.2.8"
    "@humanfs/node" "^0.16.6"
    "@humanwhocodes/module-importer" "^1.0.1"
    "@humanwhocodes/retry" "^0.4.2"
    "@modelcontextprotocol/sdk" "^1.8.0"
    "@types/estree" "^1.0.6"
    "@types/json-schema" "^7.0.15"
    ajv "^6.12.4"
    chalk "^4.0.0"
    cross-spawn "^7.0.6"
    debug "^4.3.2"
    escape-string-regexp "^4.0.0"
    eslint-scope "^8.3.0"
    eslint-visitor-keys "^4.2.0"
    espree "^10.3.0"
    esquery "^1.5.0"
    esutils "^2.0.2"
    fast-deep-equal "^3.1.3"
    file-entry-cache "^8.0.0"
    find-up "^5.0.0"
    glob-parent "^6.0.2"
    ignore "^5.2.0"
    imurmurhash "^0.1.4"
    is-glob "^4.0.0"
    json-stable-stringify-without-jsonify "^1.0.1"
    lodash.merge "^4.6.2"
    minimatch "^3.1.2"
    natural-compare "^1.4.0"
    optionator "^0.9.3"
    zod "^3.24.2"

espree@^10.0.1, espree@^10.3.0:
  version "10.3.0"
  resolved "https://registry.npmjs.org/espree/-/espree-10.3.0.tgz"
  integrity sha512-0QYC8b24HWY8zjRnDTL6RiHfDbAWn63qb4LMj1Z4b076A4une81+z03Kg7l7mn/48PUTqoLptSXez8oknU8Clg==
  dependencies:
    acorn "^8.14.0"
    acorn-jsx "^5.3.2"
    eslint-visitor-keys "^4.2.0"

esprima@^4.0.0, esprima@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmjs.org/esprima/-/esprima-4.0.1.tgz"
  integrity sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==

esquery@^1.5.0:
  version "1.6.0"
  resolved "https://registry.npmjs.org/esquery/-/esquery-1.6.0.tgz"
  integrity sha512-ca9pw9fomFcKPvFLXhBKUK90ZvGibiGOvRJNbjljY7s7uq/5YO4BOzcYtJqExdx99rF6aAcnRxHmcUHcz6sQsg==
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.3.0:
  version "4.3.0"
  resolved "https://registry.npmjs.org/esrecurse/-/esrecurse-4.3.0.tgz"
  integrity sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==
  dependencies:
    estraverse "^5.2.0"

estraverse@^5.1.0, estraverse@^5.2.0, estraverse@^5.3.0:
  version "5.3.0"
  resolved "https://registry.npmjs.org/estraverse/-/estraverse-5.3.0.tgz"
  integrity sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==

estree-util-is-identifier-name@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/estree-util-is-identifier-name/-/estree-util-is-identifier-name-3.0.0.tgz"
  integrity sha512-hFtqIDZTIUZ9BXLb8y4pYGyk6+wekIivNVTcmvk8NoOh+VeRn5y6cEHzbURrWbfp1fIqdVipilzj+lfaadNZmg==

estree-walker@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/estree-walker/-/estree-walker-2.0.2.tgz"
  integrity sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w==

esutils@^2.0.2:
  version "2.0.3"
  resolved "https://registry.npmjs.org/esutils/-/esutils-2.0.3.tgz"
  integrity sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==

etag@^1.8.1:
  version "1.8.1"
  resolved "https://registry.npmjs.org/etag/-/etag-1.8.1.tgz"
  integrity sha512-aIL5Fx7mawVa300al2BnEE4iNvo1qETxLrPI/o05L7z6go7fCw1J6EQmbK4FmJ2AS7kgVF/KEZWufBfdClMcPg==

event-target-shim@^5.0.0:
  version "5.0.1"
  resolved "https://registry.npmjs.org/event-target-shim/-/event-target-shim-5.0.1.tgz"
  integrity sha512-i/2XbnSz/uxRCU6+NdVJgKWDTM427+MqYbkQzD321DuCQJUqOuJKIA0IM2+W2xtYHdKOmZ4dR6fExsd4SXL+WQ==

eventsource-parser@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/eventsource-parser/-/eventsource-parser-3.0.1.tgz"
  integrity sha512-VARTJ9CYeuQYb0pZEPbzi740OWFgpHe7AYJ2WFZVnUDUQp5Dk2yJUgF36YsZ81cOyxT0QxmXD2EQpapAouzWVA==

eventsource@^3.0.2:
  version "3.0.6"
  resolved "https://registry.npmjs.org/eventsource/-/eventsource-3.0.6.tgz"
  integrity sha512-l19WpE2m9hSuyP06+FbuUUf1G+R0SFLrtQfbRb9PRr+oimOfxQhgGCbVaXg5IvZyyTThJsxh6L/srkMiCeBPDA==
  dependencies:
    eventsource-parser "^3.0.1"

execa@^5.0.0:
  version "5.1.1"
  resolved "https://registry.npmjs.org/execa/-/execa-5.1.1.tgz"
  integrity sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg==
  dependencies:
    cross-spawn "^7.0.3"
    get-stream "^6.0.0"
    human-signals "^2.1.0"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.1"
    onetime "^5.1.2"
    signal-exit "^3.0.3"
    strip-final-newline "^2.0.0"

exit@^0.1.2:
  version "0.1.2"
  resolved "https://registry.npmjs.org/exit/-/exit-0.1.2.tgz"
  integrity sha512-Zk/eNKV2zbjpKzrsQ+n1G6poVbErQxJ0LBOJXaKZ1EViLzH+hrLu9cdXI4zw9dBQJslwBEpbQ2P1oS7nDxs6jQ==

expect@^29.0.0, expect@^29.7.0:
  version "29.7.0"
  resolved "https://registry.npmjs.org/expect/-/expect-29.7.0.tgz"
  integrity sha512-2Zks0hf1VLFYI1kbh0I5jP3KHHyCHpkfyHBzsSXRFgl/Bg9mWYfMW8oD+PdMPlEwy5HNsR9JutYy6pMeOh61nw==
  dependencies:
    "@jest/expect-utils" "^29.7.0"
    jest-get-type "^29.6.3"
    jest-matcher-utils "^29.7.0"
    jest-message-util "^29.7.0"
    jest-util "^29.7.0"

express-rate-limit@^7.5.0:
  version "7.5.0"
  resolved "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-7.5.0.tgz"
  integrity sha512-eB5zbQh5h+VenMPM3fh+nw1YExi5nMr6HUCR62ELSP11huvxm/Uir1H1QEyTkk5QX6A58pX6NmaTMceKZ0Eodg==

express@^5.0.1:
  version "5.1.0"
  resolved "https://registry.npmjs.org/express/-/express-5.1.0.tgz"
  integrity sha512-DT9ck5YIRU+8GYzzU5kT3eHGA5iL+1Zd0EutOmTE9Dtk+Tvuzd23VBU+ec7HPNSTxXYO55gPV/hq4pSBJDjFpA==
  dependencies:
    accepts "^2.0.0"
    body-parser "^2.2.0"
    content-disposition "^1.0.0"
    content-type "^1.0.5"
    cookie "^0.7.1"
    cookie-signature "^1.2.1"
    debug "^4.4.0"
    encodeurl "^2.0.0"
    escape-html "^1.0.3"
    etag "^1.8.1"
    finalhandler "^2.1.0"
    fresh "^2.0.0"
    http-errors "^2.0.0"
    merge-descriptors "^2.0.0"
    mime-types "^3.0.0"
    on-finished "^2.4.1"
    once "^1.4.0"
    parseurl "^1.3.3"
    proxy-addr "^2.0.7"
    qs "^6.14.0"
    range-parser "^1.2.1"
    router "^2.2.0"
    send "^1.1.0"
    serve-static "^2.2.0"
    statuses "^2.0.1"
    type-is "^2.0.1"
    vary "^1.1.2"

extend@^3.0.0, extend@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npmjs.org/extend/-/extend-3.0.2.tgz"
  integrity sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==

fast-copy@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npmjs.org/fast-copy/-/fast-copy-3.0.2.tgz"
  integrity sha512-dl0O9Vhju8IrcLndv2eU4ldt1ftXMqqfgN4H1cpmGV7P6jeB9FwpN9a2c8DPGE1Ys88rNUJVYDHq73CGAGOPfQ==

fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
  version "3.1.3"
  resolved "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz"
  integrity sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==

fast-glob@^3.3.2:
  version "3.3.3"
  resolved "https://registry.npmjs.org/fast-glob/-/fast-glob-3.3.3.tgz"
  integrity sha512-7MptL8U0cqcFdzIzwOTHoilX9x5BrNqye7Z/LuC7kCMRio1EMSyqRK3BEAUD7sXRq4iT4AzTVuZdhgQ2TCvYLg==
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.8"

fast-glob@3.3.1:
  version "3.3.1"
  resolved "https://registry.npmjs.org/fast-glob/-/fast-glob-3.3.1.tgz"
  integrity sha512-kNFPyjhh5cKjrUltxs+wFx+ZkbRaxxmZ+X0ZU31SOsxCEtP9VPgtq2teZw1DebupL5GmDaNQ6yKMMVcM41iqDg==
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.4"

fast-json-stable-stringify@^2.0.0, fast-json-stable-stringify@^2.1.0, fast-json-stable-stringify@2.x:
  version "2.1.0"
  resolved "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz"
  integrity sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==

fast-levenshtein@^2.0.6:
  version "2.0.6"
  resolved "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz"
  integrity sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==

fast-redact@^3.1.1:
  version "3.5.0"
  resolved "https://registry.npmjs.org/fast-redact/-/fast-redact-3.5.0.tgz"
  integrity sha512-dwsoQlS7h9hMeYUq1W++23NDcBLV4KqONnITDV9DjfS3q1SgDGVrBdvvTLUotWtPSD7asWDV9/CmsZPy8Hf70A==

fast-safe-stringify@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/fast-safe-stringify/-/fast-safe-stringify-2.1.1.tgz"
  integrity sha512-W+KJc2dmILlPplD/H4K9l9LcAHAfPtP6BY84uVLXQ6Evcz9Lcg33Y2z1IVblT6xdY54PXYVHEv+0Wpq8Io6zkA==

fast-shallow-equal@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/fast-shallow-equal/-/fast-shallow-equal-1.0.0.tgz"
  integrity sha512-HPtaa38cPgWvaCFmRNhlc6NG7pv6NUHqjPgVAkWGoB9mQMwYB27/K0CvOM5Czy+qpT3e8XJ6Q4aPAnzpNpzNaw==

fastest-stable-stringify@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/fastest-stable-stringify/-/fastest-stable-stringify-2.0.2.tgz"
  integrity sha512-bijHueCGd0LqqNK9b5oCMHc0MluJAx0cwqASgbWMvkO01lCYgIhacVRLcaDz3QnyYIRNJRDwMb41VuT6pHJ91Q==

fastq@^1.6.0:
  version "1.19.1"
  resolved "https://registry.npmjs.org/fastq/-/fastq-1.19.1.tgz"
  integrity sha512-GwLTyxkCXjXbxqIhTsMI2Nui8huMPtnxg7krajPJAjnEG/iiOS7i+zCtWGZR9G0NBKbXKh6X9m9UIsYX/N6vvQ==
  dependencies:
    reusify "^1.0.4"

fault@^1.0.0:
  version "1.0.4"
  resolved "https://registry.npmjs.org/fault/-/fault-1.0.4.tgz"
  integrity sha512-CJ0HCB5tL5fYTEA7ToAq5+kTwd++Borf1/bifxd9iT70QcXr4MRrO3Llf8Ifs70q+SJcGHFtnIE/Nw6giCtECA==
  dependencies:
    format "^0.2.0"

fb-watchman@^2.0.0:
  version "2.0.2"
  resolved "https://registry.npmjs.org/fb-watchman/-/fb-watchman-2.0.2.tgz"
  integrity sha512-p5161BqbuCaSnB8jIbzQHOlpgsPmK5rJVDfDKO91Axs5NC1uu3HRQm6wt9cd9/+GtQQIO53JdGXXoyDpTAsgYA==
  dependencies:
    bser "2.1.1"

fdir@^6.2.0:
  version "6.4.4"
  resolved "https://registry.npmjs.org/fdir/-/fdir-6.4.4.tgz"
  integrity sha512-1NZP+GK4GfuAv3PqKvxQRDMjdSRZjnkq7KfhlNrCNNlZ0ygQFpebfrnfnq/W7fpUnAv9aGWmY1zKx7FYL3gwhg==

fdir@^6.4.4:
  version "6.4.4"
  resolved "https://registry.npmjs.org/fdir/-/fdir-6.4.4.tgz"
  integrity sha512-1NZP+GK4GfuAv3PqKvxQRDMjdSRZjnkq7KfhlNrCNNlZ0ygQFpebfrnfnq/W7fpUnAv9aGWmY1zKx7FYL3gwhg==

fetch-blob@^3.1.2, fetch-blob@^3.1.4:
  version "3.2.0"
  resolved "https://registry.npmjs.org/fetch-blob/-/fetch-blob-3.2.0.tgz"
  integrity sha512-7yAQpD2UMJzLi1Dqv7qFYnPbaPx7ZfFK6PiIxQ4PfkGPyNyl2Ugx+a/umUonmKqjhM4DnfbMvdX6otXq83soQQ==
  dependencies:
    node-domexception "^1.0.0"
    web-streams-polyfill "^3.0.3"

file-entry-cache@^8.0.0:
  version "8.0.0"
  resolved "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-8.0.0.tgz"
  integrity sha512-XXTUwCvisa5oacNGRP9SfNtYBNAMi+RPwBFmblZEF7N7swHYQS6/Zfk7SRwx4D5j3CH211YNRco1DEMNVfZCnQ==
  dependencies:
    flat-cache "^4.0.0"

filelist@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmjs.org/filelist/-/filelist-1.0.4.tgz"
  integrity sha512-w1cEuf3S+DrLCQL7ET6kz+gmlJdbq9J7yXCSjK/OZCPA+qEN1WyF4ZAf0YYJa4/shHJra2t/d/r8SV4Ji+x+8Q==
  dependencies:
    minimatch "^5.0.1"

fill-range@^7.1.1:
  version "7.1.1"
  resolved "https://registry.npmjs.org/fill-range/-/fill-range-7.1.1.tgz"
  integrity sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==
  dependencies:
    to-regex-range "^5.0.1"

finalhandler@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/finalhandler/-/finalhandler-2.1.0.tgz"
  integrity sha512-/t88Ty3d5JWQbWYgaOGCCYfXRwV1+be02WqYYlL6h0lEiUAMPM8o8qKGO01YIkOHzka2up08wvgYD0mDiI+q3Q==
  dependencies:
    debug "^4.4.0"
    encodeurl "^2.0.0"
    escape-html "^1.0.3"
    on-finished "^2.4.1"
    parseurl "^1.3.3"
    statuses "^2.0.1"

find-up@^4.0.0, find-up@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz"
  integrity sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

find-up@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/find-up/-/find-up-5.0.0.tgz"
  integrity sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==
  dependencies:
    locate-path "^6.0.0"
    path-exists "^4.0.0"

flat-cache@^4.0.0:
  version "4.0.1"
  resolved "https://registry.npmjs.org/flat-cache/-/flat-cache-4.0.1.tgz"
  integrity sha512-f7ccFPK3SXFHpx15UIGyRJ/FJQctuKZ0zVuN3frBo4HnK3cay9VEW0R6yPYFHC0AgqhukPzKjq22t5DmAyqGyw==
  dependencies:
    flatted "^3.2.9"
    keyv "^4.5.4"

flatted@^3.2.9:
  version "3.3.3"
  resolved "https://registry.npmjs.org/flatted/-/flatted-3.3.3.tgz"
  integrity sha512-GX+ysw4PBCz0PzosHDepZGANEuFCMLrnRTiEy9McGjmkCQYwRq4A/X786G/fjM/+OjsWSU1ZrY5qyARZmO/uwg==

for-each@^0.3.3, for-each@^0.3.5:
  version "0.3.5"
  resolved "https://registry.npmjs.org/for-each/-/for-each-0.3.5.tgz"
  integrity sha512-dKx12eRCVIzqCxFGplyFKJMPvLEWgmNtUrpTiJIR5u97zEhRG8ySrtboPHZXx7daLxQVrl643cTzbab2tkQjxg==
  dependencies:
    is-callable "^1.2.7"

foreground-child@^3.1.0:
  version "3.3.1"
  resolved "https://registry.npmjs.org/foreground-child/-/foreground-child-3.3.1.tgz"
  integrity sha512-gIXjKqtFuWEgzFRJA9WCQeSJLZDjgJUOMCMzxtvFq/37KojM1BFGufqsCy0r4qSQmYLsZYMeyRqzIWOMup03sw==
  dependencies:
    cross-spawn "^7.0.6"
    signal-exit "^4.0.1"

form-data-encoder@1.7.2:
  version "1.7.2"
  resolved "https://registry.npmjs.org/form-data-encoder/-/form-data-encoder-1.7.2.tgz"
  integrity sha512-qfqtYan3rxrnCk1VYaA4H+Ms9xdpPqvLZa6xmMgFvhO32x7/3J/ExcTd6qpxM0vH2GdMI+poehyBZvqfMTto8A==

form-data@^4.0.0:
  version "4.0.2"
  resolved "https://registry.npmjs.org/form-data/-/form-data-4.0.2.tgz"
  integrity sha512-hGfm/slu0ZabnNt4oaRZ6uREyfCj6P4fT/n6A1rGV+Z0VdGXjfOhVUpkn6qVQONHGIFwmveGXyDs75+nr6FM8w==
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    es-set-tostringtag "^2.1.0"
    mime-types "^2.1.12"

format@^0.2.0:
  version "0.2.2"
  resolved "https://registry.npmjs.org/format/-/format-0.2.2.tgz"
  integrity sha512-wzsgA6WOq+09wrU1tsJ09udeR/YZRaeArL9e1wPbFg3GG2yDnC2ldKpxs4xunpFF9DgqCqOIra3bc1HWrJ37Ww==

formdata-node@^4.3.2:
  version "4.4.1"
  resolved "https://registry.npmjs.org/formdata-node/-/formdata-node-4.4.1.tgz"
  integrity sha512-0iirZp3uVDjVGt9p49aTaqjk84TrglENEDuqfdlZQ1roC9CWlPk6Avf8EEnZNcAqPonwkG35x4n3ww/1THYAeQ==
  dependencies:
    node-domexception "1.0.0"
    web-streams-polyfill "4.0.0-beta.3"

formdata-polyfill@^4.0.10:
  version "4.0.10"
  resolved "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-4.0.10.tgz"
  integrity sha512-buewHzMvYL29jdeQTVILecSaZKnt/RJWjoZCF5OW60Z67/GmSLBkOFM7qh1PI3zFNtJbaZL5eQu1vLfazOwj4g==
  dependencies:
    fetch-blob "^3.1.2"

forwarded-parse@2.1.2:
  version "2.1.2"
  resolved "https://registry.npmjs.org/forwarded-parse/-/forwarded-parse-2.1.2.tgz"
  integrity sha512-alTFZZQDKMporBH77856pXgzhEzaUVmLCDk+egLgIgHst3Tpndzz8MnKe+GzRJRfvVdn69HhpW7cmXzvtLvJAw==

forwarded@0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/forwarded/-/forwarded-0.2.0.tgz"
  integrity sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow==

framer-motion@^12.4.10:
  version "12.9.4"
  resolved "https://registry.npmjs.org/framer-motion/-/framer-motion-12.9.4.tgz"
  integrity sha512-yaeGDmGQ3eCQEwZ95/pRQMaSh/Q4E2CK6JYOclG/PdjyQad0MULJ+JFVV8911Fl5a6tF6o0wgW8Dpl5Qx4Adjg==
  dependencies:
    motion-dom "^12.9.4"
    motion-utils "^12.9.4"
    tslib "^2.4.0"

fresh@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/fresh/-/fresh-2.0.0.tgz"
  integrity sha512-Rx/WycZ60HOaqLKAi6cHRKKI7zxWbJ31MhntmtwMoaTeF7XFH9hhBp8vITaMidfljRQ6eYWCKkaTK+ykVJHP2A==

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz"
  integrity sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==

fsevents@^2.3.2, fsevents@~2.3.2:
  version "2.3.3"
  resolved "https://registry.npmjs.org/fsevents/-/fsevents-2.3.3.tgz"
  integrity sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==

function-bind@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz"
  integrity sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==

function.prototype.name@^1.1.6, function.prototype.name@^1.1.8:
  version "1.1.8"
  resolved "https://registry.npmjs.org/function.prototype.name/-/function.prototype.name-1.1.8.tgz"
  integrity sha512-e5iwyodOHhbMr/yNrc7fDYG4qlbIvI5gajyzPnb5TCwyhjApznQh1BMFou9b30SevY43gCJKXycoCBjMbsuW0Q==
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    functions-have-names "^1.2.3"
    hasown "^2.0.2"
    is-callable "^1.2.7"

functions-have-names@^1.2.3:
  version "1.2.3"
  resolved "https://registry.npmjs.org/functions-have-names/-/functions-have-names-1.2.3.tgz"
  integrity sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ==

fuse.js@^7.1.0:
  version "7.1.0"
  resolved "https://registry.npmjs.org/fuse.js/-/fuse.js-7.1.0.tgz"
  integrity sha512-trLf4SzuuUxfusZADLINj+dE8clK1frKdmqiJNb1Es75fmI5oY6X2mxLVUciLLjxqw/xr72Dhy+lER6dGd02FQ==

gaxios@^6.1.1:
  version "6.7.1"
  resolved "https://registry.npmjs.org/gaxios/-/gaxios-6.7.1.tgz"
  integrity sha512-LDODD4TMYx7XXdpwxAVRAIAuB0bzv0s+ywFonY46k126qzQHT9ygyoa9tncmOiQmmDrik65UYsEkv3lbfqQ3yQ==
  dependencies:
    extend "^3.0.2"
    https-proxy-agent "^7.0.1"
    is-stream "^2.0.0"
    node-fetch "^2.6.9"
    uuid "^9.0.1"

gcp-metadata@^6.0.0:
  version "6.1.1"
  resolved "https://registry.npmjs.org/gcp-metadata/-/gcp-metadata-6.1.1.tgz"
  integrity sha512-a4tiq7E0/5fTjxPAaH4jpjkSv/uCaU2p5KC6HVGrvl0cDjA8iBZv4vv1gyzlmK0ZUKqwpOyQMKzZQe3lTit77A==
  dependencies:
    gaxios "^6.1.1"
    google-logging-utils "^0.0.2"
    json-bigint "^1.0.0"

gensync@^1.0.0-beta.2:
  version "1.0.0-beta.2"
  resolved "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz"
  integrity sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==

get-caller-file@^2.0.5:
  version "2.0.5"
  resolved "https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz"
  integrity sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==

get-intrinsic@^1.1.3, get-intrinsic@^1.2.2, get-intrinsic@^1.2.4, get-intrinsic@^1.2.5, get-intrinsic@^1.2.6, get-intrinsic@^1.2.7, get-intrinsic@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.3.0.tgz"
  integrity sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==
  dependencies:
    call-bind-apply-helpers "^1.0.2"
    es-define-property "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.1.1"
    function-bind "^1.1.2"
    get-proto "^1.0.1"
    gopd "^1.2.0"
    has-symbols "^1.1.0"
    hasown "^2.0.2"
    math-intrinsics "^1.1.0"

get-nonce@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/get-nonce/-/get-nonce-1.0.1.tgz"
  integrity sha512-FJhYRoDaiatfEkUK8HKlicmu/3SGFD51q3itKDGoSTysQJBnfOcxU5GxnhE1E6soB76MbT0MBtnKJuXyAx+96Q==

get-package-type@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npmjs.org/get-package-type/-/get-package-type-0.1.0.tgz"
  integrity sha512-pjzuKtY64GYfWizNAJ0fr9VqttZkNiK2iS430LtIHzjBEr6bX8Am2zm4sW4Ro5wjWW5cAlRL1qAMTcXbjNAO2Q==

get-proto@^1.0.0, get-proto@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/get-proto/-/get-proto-1.0.1.tgz"
  integrity sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==
  dependencies:
    dunder-proto "^1.0.1"
    es-object-atoms "^1.0.0"

get-stream@^6.0.0:
  version "6.0.1"
  resolved "https://registry.npmjs.org/get-stream/-/get-stream-6.0.1.tgz"
  integrity sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg==

get-symbol-description@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/get-symbol-description/-/get-symbol-description-1.1.0.tgz"
  integrity sha512-w9UMqWwJxHNOvoNzSJ2oPF5wvYcvP7jUvYzhp67yEhTi17ZDBBC1z9pTdGuzjD+EFIqLSYRweZjqfiPzQ06Ebg==
  dependencies:
    call-bound "^1.0.3"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.6"

get-tsconfig@^4.10.0:
  version "4.10.0"
  resolved "https://registry.npmjs.org/get-tsconfig/-/get-tsconfig-4.10.0.tgz"
  integrity sha512-kGzZ3LWWQcGIAmg6iWvXn0ei6WDtV26wzHRMwDSzmAbcXrTEXxHy6IehI6/4eT6VRKyMP1eF1VqwrVUmE/LR7A==
  dependencies:
    resolve-pkg-maps "^1.0.0"

glob-parent@^5.1.2, glob-parent@~5.1.2:
  version "5.1.2"
  resolved "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz"
  integrity sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==
  dependencies:
    is-glob "^4.0.1"

glob-parent@^6.0.2:
  version "6.0.2"
  resolved "https://registry.npmjs.org/glob-parent/-/glob-parent-6.0.2.tgz"
  integrity sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==
  dependencies:
    is-glob "^4.0.3"

glob@^10.3.10:
  version "10.4.5"
  resolved "https://registry.npmjs.org/glob/-/glob-10.4.5.tgz"
  integrity sha512-7Bv8RF0k6xjo7d4A/PxYLbUCfb6c+Vpd2/mB2yRDlew7Jb5hEXiCD9ibfO7wpk8i4sevK6DFny9h7EYbM3/sHg==
  dependencies:
    foreground-child "^3.1.0"
    jackspeak "^3.1.2"
    minimatch "^9.0.4"
    minipass "^7.1.2"
    package-json-from-dist "^1.0.0"
    path-scurry "^1.11.1"

glob@^7.1.3, glob@^7.1.4:
  version "7.2.3"
  resolved "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz"
  integrity sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.1.1"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

glob@^9.3.2:
  version "9.3.5"
  resolved "https://registry.npmjs.org/glob/-/glob-9.3.5.tgz"
  integrity sha512-e1LleDykUz2Iu+MTYdkSsuWX8lvAjAcs0Xef0lNIu0S2wOAzuTxCJtcd9S3cijlwYF18EsU3rzb8jPVobxDh9Q==
  dependencies:
    fs.realpath "^1.0.0"
    minimatch "^8.0.2"
    minipass "^4.2.4"
    path-scurry "^1.6.1"

globals@^11.1.0:
  version "11.12.0"
  resolved "https://registry.npmjs.org/globals/-/globals-11.12.0.tgz"
  integrity sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==

globals@^14.0.0:
  version "14.0.0"
  resolved "https://registry.npmjs.org/globals/-/globals-14.0.0.tgz"
  integrity sha512-oahGvuMGQlPw/ivIYBjVSrWAfWLBeku5tpPE2fOPLi+WHffIWbuh2tCjhyQhTBPMf5E9jDEH4FOmTYgYwbKwtQ==

globals@^16.0.0:
  version "16.0.0"
  resolved "https://registry.npmjs.org/globals/-/globals-16.0.0.tgz"
  integrity sha512-iInW14XItCXET01CQFqudPOWP2jYMl7T+QRQT+UNcR/iQncN/F0UNpgd76iFkBPgNQb4+X3LV9tLJYzwh+Gl3A==

globalthis@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmjs.org/globalthis/-/globalthis-1.0.4.tgz"
  integrity sha512-DpLKbNU4WylpxJykQujfCcwYWiV/Jhm50Goo0wrVILAv5jOr9d+H+UR3PhSCD2rCCEIg0uc+G+muBTwD54JhDQ==
  dependencies:
    define-properties "^1.2.1"
    gopd "^1.0.1"

google-logging-utils@^0.0.2:
  version "0.0.2"
  resolved "https://registry.npmjs.org/google-logging-utils/-/google-logging-utils-0.0.2.tgz"
  integrity sha512-NEgUnEcBiP5HrPzufUkBzJOD/Sxsco3rLNo1F1TNf7ieU8ryUzBhqba8r756CjLX7rn3fHl6iLEwPYuqpoKgQQ==

gopd@^1.0.1, gopd@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/gopd/-/gopd-1.2.0.tgz"
  integrity sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==

graceful-fs@^4.2.9:
  version "4.2.11"
  resolved "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz"
  integrity sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==

graphemer@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npmjs.org/graphemer/-/graphemer-1.4.0.tgz"
  integrity sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag==

harmony-reflect@^1.4.6:
  version "1.6.2"
  resolved "https://registry.npmjs.org/harmony-reflect/-/harmony-reflect-1.6.2.tgz"
  integrity sha512-HIp/n38R9kQjDEziXyDTuW3vvoxxyxjxFzXLrBr18uB47GnSt+G9D29fqrpM5ZkspMcPICud3XsBJQ4Y2URg8g==

has-bigints@^1.0.2:
  version "1.1.0"
  resolved "https://registry.npmjs.org/has-bigints/-/has-bigints-1.1.0.tgz"
  integrity sha512-R3pbpkcIqv2Pm3dUwgjclDRVmWpTJW2DcMzcIhEXEx1oh/CEMObMm3KLmRJOdvhM7o4uQBnwr8pzRK2sJWIqfg==

has-flag@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz"
  integrity sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==

has-property-descriptors@^1.0.0, has-property-descriptors@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz"
  integrity sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==
  dependencies:
    es-define-property "^1.0.0"

has-proto@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/has-proto/-/has-proto-1.2.0.tgz"
  integrity sha512-KIL7eQPfHQRC8+XluaIw7BHUwwqL19bQn4hzNgdr+1wXoU0KKj6rufu47lhY7KbJR2C6T6+PfyN0Ea7wkSS+qQ==
  dependencies:
    dunder-proto "^1.0.0"

has-symbols@^1.0.3, has-symbols@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/has-symbols/-/has-symbols-1.1.0.tgz"
  integrity sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==

has-tostringtag@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/has-tostringtag/-/has-tostringtag-1.0.2.tgz"
  integrity sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==
  dependencies:
    has-symbols "^1.0.3"

hasown@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz"
  integrity sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==
  dependencies:
    function-bind "^1.1.2"

hast-util-parse-selector@^2.0.0:
  version "2.2.5"
  resolved "https://registry.npmjs.org/hast-util-parse-selector/-/hast-util-parse-selector-2.2.5.tgz"
  integrity sha512-7j6mrk/qqkSehsM92wQjdIgWM2/BW61u/53G6xmC8i1OmEdKLHbk419QKQUjz6LglWsfqoiHmyMRkP1BGjecNQ==

hast-util-to-jsx-runtime@^2.0.0:
  version "2.3.6"
  resolved "https://registry.npmjs.org/hast-util-to-jsx-runtime/-/hast-util-to-jsx-runtime-2.3.6.tgz"
  integrity sha512-zl6s8LwNyo1P9uw+XJGvZtdFF1GdAkOg8ujOw+4Pyb76874fLps4ueHXDhXWdk6YHQ6OgUtinliG7RsYvCbbBg==
  dependencies:
    "@types/estree" "^1.0.0"
    "@types/hast" "^3.0.0"
    "@types/unist" "^3.0.0"
    comma-separated-tokens "^2.0.0"
    devlop "^1.0.0"
    estree-util-is-identifier-name "^3.0.0"
    hast-util-whitespace "^3.0.0"
    mdast-util-mdx-expression "^2.0.0"
    mdast-util-mdx-jsx "^3.0.0"
    mdast-util-mdxjs-esm "^2.0.0"
    property-information "^7.0.0"
    space-separated-tokens "^2.0.0"
    style-to-js "^1.0.0"
    unist-util-position "^5.0.0"
    vfile-message "^4.0.0"

hast-util-whitespace@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/hast-util-whitespace/-/hast-util-whitespace-3.0.0.tgz"
  integrity sha512-88JUN06ipLwsnv+dVn+OIYOvAuvBMy/Qoi6O7mQHxdPXpjy+Cd6xRkWwux7DKO+4sYILtLBRIKgsdpS2gQc7qw==
  dependencies:
    "@types/hast" "^3.0.0"

hastscript@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmjs.org/hastscript/-/hastscript-6.0.0.tgz"
  integrity sha512-nDM6bvd7lIqDUiYEiu5Sl/+6ReP0BMk/2f4U/Rooccxkj0P5nm+acM5PrGJ/t5I8qPGiqZSE6hVAwZEdZIvP4w==
  dependencies:
    "@types/hast" "^2.0.0"
    comma-separated-tokens "^1.0.0"
    hast-util-parse-selector "^2.0.0"
    property-information "^5.0.0"
    space-separated-tokens "^1.0.0"

help-me@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/help-me/-/help-me-5.0.0.tgz"
  integrity sha512-7xgomUX6ADmcYzFik0HzAxh/73YlKR9bmFzf51CZwR+b6YtzU2m0u49hQCqV6SvlqIqsaxovfwdvbnsw3b/zpg==

highlight.js@^10.4.1, highlight.js@~10.7.0:
  version "10.7.3"
  resolved "https://registry.npmjs.org/highlight.js/-/highlight.js-10.7.3.tgz"
  integrity sha512-tzcUFauisWKNHaRkN4Wjl/ZA07gENAjFl3J/c480dprkGTg5EQstgaNFqBfUqCq54kZRIEcreTsAgF/m2quD7A==

highlightjs-vue@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/highlightjs-vue/-/highlightjs-vue-1.0.0.tgz"
  integrity sha512-PDEfEF102G23vHmPhLyPboFCD+BkMGu+GuJe2d9/eH4FsCwvgBpnc9n0pGE+ffKdph38s6foEZiEjdgHdzp+IA==

hoist-non-react-statics@^3.3.2:
  version "3.3.2"
  resolved "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-3.3.2.tgz"
  integrity sha512-/gGivxi8JPKWNm/W0jSmzcMPpfpPLc3dY/6GxhX2hQ9iGj3aDfklV4ET7NjKpSinLpJ5vafa9iiGIEZg10SfBw==
  dependencies:
    react-is "^16.7.0"

html-encoding-sniffer@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/html-encoding-sniffer/-/html-encoding-sniffer-3.0.0.tgz"
  integrity sha512-oWv4T4yJ52iKrufjnyZPkrN0CH3QnrUqdB6In1g5Fe1mia8GmF36gnfNySxoZtxD5+NmYw1EElVXiBk93UeskA==
  dependencies:
    whatwg-encoding "^2.0.0"

html-escaper@^2.0.0:
  version "2.0.2"
  resolved "https://registry.npmjs.org/html-escaper/-/html-escaper-2.0.2.tgz"
  integrity sha512-H2iMtd0I4Mt5eYiapRdIDjp+XzelXQ0tFE4JS7YFwFevXXMmOp9myNrUvCg0D6ws8iqkRPBfKHgbwig1SmlLfg==

html-url-attributes@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmjs.org/html-url-attributes/-/html-url-attributes-3.0.1.tgz"
  integrity sha512-ol6UPyBWqsrO6EJySPz2O7ZSr856WDrEzM5zMqp+FJJLGMW35cLYmmZnl0vztAZxRUoNZJFTCohfjuIJ8I4QBQ==

http-errors@^2.0.0, http-errors@2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/http-errors/-/http-errors-2.0.0.tgz"
  integrity sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==
  dependencies:
    depd "2.0.0"
    inherits "2.0.4"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    toidentifier "1.0.1"

http-proxy-agent@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-5.0.0.tgz"
  integrity sha512-n2hY8YdoRE1i7r6M0w9DIw5GgZN0G25P8zLCRQ8rjXtTU3vsNFBI/vWK/UIeE6g5MUUz6avwAPXmL6Fy9D/90w==
  dependencies:
    "@tootallnate/once" "2"
    agent-base "6"
    debug "4"

https-proxy-agent@^5.0.0, https-proxy-agent@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-5.0.1.tgz"
  integrity sha512-dFcAjpTQFgoLMzC2VwU+C/CbS7uRL0lWmxDITmqm7C+7F0Odmj6s9l6alZc6AELXhrnggM2CeWSXHGOdX2YtwA==
  dependencies:
    agent-base "6"
    debug "4"

https-proxy-agent@^7.0.1:
  version "7.0.6"
  resolved "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-7.0.6.tgz"
  integrity sha512-vK9P5/iUfdl95AI+JVyUuIcVtd4ofvtrOr3HNtM2yxC9bnMbEdp3x01OhQNnjb8IJYi38VlTE3mBXwcfvywuSw==
  dependencies:
    agent-base "^7.1.2"
    debug "4"

https-proxy-agent@^7.0.2:
  version "7.0.6"
  resolved "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-7.0.6.tgz"
  integrity sha512-vK9P5/iUfdl95AI+JVyUuIcVtd4ofvtrOr3HNtM2yxC9bnMbEdp3x01OhQNnjb8IJYi38VlTE3mBXwcfvywuSw==
  dependencies:
    agent-base "^7.1.2"
    debug "4"

human-signals@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/human-signals/-/human-signals-2.1.0.tgz"
  integrity sha512-B4FFZ6q/T2jhhksgkbEW3HBvWIfDW85snkQgawt07S7J5QXTk6BkNV+0yAeZrM5QpMAdYlocGoljn0sJ/WQkFw==

humanize-ms@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/humanize-ms/-/humanize-ms-1.2.1.tgz"
  integrity sha512-Fl70vYtsAFb/C06PTS9dZBo7ihau+Tu/DNCk/OyHhea07S+aeMWpFFkUaXRa8fI+ScZbEI8dfSxwY7gxZ9SAVQ==
  dependencies:
    ms "^2.0.0"

hyphenate-style-name@^1.0.3:
  version "1.1.0"
  resolved "https://registry.npmjs.org/hyphenate-style-name/-/hyphenate-style-name-1.1.0.tgz"
  integrity sha512-WDC/ui2VVRrz3jOVi+XtjqkDjiVjTtFaAGiW37k6b+ohyQ5wYDOGkvCZa8+H0nx3gyvv0+BST9xuOgIyGQ00gw==

iconv-lite@^0.6.3, iconv-lite@0.6.3:
  version "0.6.3"
  resolved "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.6.3.tgz"
  integrity sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==
  dependencies:
    safer-buffer ">= 2.1.2 < 3.0.0"

identity-obj-proxy@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/identity-obj-proxy/-/identity-obj-proxy-3.0.0.tgz"
  integrity sha512-00n6YnVHKrinT9t0d9+5yZC6UBNJANpYEQvL2LlX6Ab9lnmxzIRcEmTPuyGScvl1+jKuCICX1Z0Ab1pPKKdikA==
  dependencies:
    harmony-reflect "^1.4.6"

ignore@^5.2.0, ignore@^5.3.1:
  version "5.3.2"
  resolved "https://registry.npmjs.org/ignore/-/ignore-5.3.2.tgz"
  integrity sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g==

import-fresh@^3.2.1:
  version "3.3.1"
  resolved "https://registry.npmjs.org/import-fresh/-/import-fresh-3.3.1.tgz"
  integrity sha512-TR3KfrTZTYLPB6jUjfx6MF9WcWrHL9su5TObK4ZkYgBdWKPOFoSoQIdEuTuR82pmtxH2spWG9h6etwfr1pLBqQ==
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

import-in-the-middle@^1.13.0, import-in-the-middle@^1.8.1:
  version "1.13.1"
  resolved "https://registry.npmjs.org/import-in-the-middle/-/import-in-the-middle-1.13.1.tgz"
  integrity sha512-k2V9wNm9B+ysuelDTHjI9d5KPc4l8zAZTGqj+pcynvWkypZd857ryzN8jNC7Pg2YZXNMJcHRPpaDyCBbNyVRpA==
  dependencies:
    acorn "^8.14.0"
    acorn-import-attributes "^1.9.5"
    cjs-module-lexer "^1.2.2"
    module-details-from-path "^1.0.3"

import-local@^3.0.2:
  version "3.2.0"
  resolved "https://registry.npmjs.org/import-local/-/import-local-3.2.0.tgz"
  integrity sha512-2SPlun1JUPWoM6t3F0dw0FkCF/jWY8kttcY4f599GLTSjh2OCuuhdTkJQsEcZzBqbXZGKMK2OqW1oZsjtf/gQA==
  dependencies:
    pkg-dir "^4.2.0"
    resolve-cwd "^3.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz"
  integrity sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==

indent-string@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/indent-string/-/indent-string-4.0.0.tgz"
  integrity sha512-EdDDZu4A2OyIK7Lr/2zG+w5jmbuk1DVBnEwREQvBzspBJkCEbRa8GxU1lghYcaGJCnRWibjDXlq779X1/y5xwg==

inflight@^1.0.4:
  version "1.0.6"
  resolved "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz"
  integrity sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@^2.0.4, inherits@2, inherits@2.0.4:
  version "2.0.4"
  resolved "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz"
  integrity sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==

inline-style-parser@0.2.4:
  version "0.2.4"
  resolved "https://registry.npmjs.org/inline-style-parser/-/inline-style-parser-0.2.4.tgz"
  integrity sha512-0aO8FkhNZlj/ZIbNi7Lxxr12obT7cL1moPfE4tg1LkX7LlLfC6DeX4l2ZEud1ukP9jNQyNnfzQVqwbwmAATY4Q==

inline-style-prefixer@^7.0.1:
  version "7.0.1"
  resolved "https://registry.npmjs.org/inline-style-prefixer/-/inline-style-prefixer-7.0.1.tgz"
  integrity sha512-lhYo5qNTQp3EvSSp3sRvXMbVQTLrvGV6DycRMJ5dm2BLMiJ30wpXKdDdgX+GmJZ5uQMucwRKHamXSst3Sj/Giw==
  dependencies:
    css-in-js-utils "^3.1.0"

internal-slot@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/internal-slot/-/internal-slot-1.1.0.tgz"
  integrity sha512-4gd7VpWNQNB4UKKCFFVcp1AVv+FMOgs9NKzjHKusc8jTMhd5eL1NqQqOpE0KzMds804/yHlglp3uxgluOqAPLw==
  dependencies:
    es-errors "^1.3.0"
    hasown "^2.0.2"
    side-channel "^1.1.0"

ipaddr.js@1.9.1:
  version "1.9.1"
  resolved "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.9.1.tgz"
  integrity sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g==

is-alphabetical@^1.0.0:
  version "1.0.4"
  resolved "https://registry.npmjs.org/is-alphabetical/-/is-alphabetical-1.0.4.tgz"
  integrity sha512-DwzsA04LQ10FHTZuL0/grVDk4rFoVH1pjAToYwBrHSxcrBIGQuXrQMtD5U1b0U2XVgKZCTLLP8u2Qxqhy3l2Vg==

is-alphabetical@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/is-alphabetical/-/is-alphabetical-2.0.1.tgz"
  integrity sha512-FWyyY60MeTNyeSRpkM2Iry0G9hpr7/9kD40mD/cGQEuilcZYS4okz8SN2Q6rLCJ8gbCt6fN+rC+6tMGS99LaxQ==

is-alphanumerical@^1.0.0:
  version "1.0.4"
  resolved "https://registry.npmjs.org/is-alphanumerical/-/is-alphanumerical-1.0.4.tgz"
  integrity sha512-UzoZUr+XfVz3t3v4KyGEniVL9BDRoQtY7tOyrRybkVNjDFWyo1yhXNGrrBTQxp3ib9BLAWs7k2YKBQsFRkZG9A==
  dependencies:
    is-alphabetical "^1.0.0"
    is-decimal "^1.0.0"

is-alphanumerical@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/is-alphanumerical/-/is-alphanumerical-2.0.1.tgz"
  integrity sha512-hmbYhX/9MUMF5uh7tOXyK/n0ZvWpad5caBA17GsC6vyuCqaWliRG5K1qS9inmUhEMaOBIW7/whAnSwveW/LtZw==
  dependencies:
    is-alphabetical "^2.0.0"
    is-decimal "^2.0.0"

is-arguments@^1.1.1:
  version "1.2.0"
  resolved "https://registry.npmjs.org/is-arguments/-/is-arguments-1.2.0.tgz"
  integrity sha512-7bVbi0huj/wrIAOzb8U1aszg9kdi3KN/CyU19CTI7tAoZYEZoL9yCDXpbXN+uPsuWnP02cyug1gleqq+TU+YCA==
  dependencies:
    call-bound "^1.0.2"
    has-tostringtag "^1.0.2"

is-array-buffer@^3.0.2, is-array-buffer@^3.0.4, is-array-buffer@^3.0.5:
  version "3.0.5"
  resolved "https://registry.npmjs.org/is-array-buffer/-/is-array-buffer-3.0.5.tgz"
  integrity sha512-DDfANUiiG2wC1qawP66qlTugJeL5HyzMpfr8lLK+jMQirGzNod0B12cFB/9q838Ru27sBwfw78/rdoU7RERz6A==
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    get-intrinsic "^1.2.6"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz"
  integrity sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg==

is-arrayish@^0.3.1:
  version "0.3.2"
  resolved "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.3.2.tgz"
  integrity sha512-eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ==

is-async-function@^2.0.0:
  version "2.1.1"
  resolved "https://registry.npmjs.org/is-async-function/-/is-async-function-2.1.1.tgz"
  integrity sha512-9dgM/cZBnNvjzaMYHVoxxfPj2QXt22Ev7SuuPrs+xav0ukGB0S6d4ydZdEiM48kLx5kDV+QBPrpVnFyefL8kkQ==
  dependencies:
    async-function "^1.0.0"
    call-bound "^1.0.3"
    get-proto "^1.0.1"
    has-tostringtag "^1.0.2"
    safe-regex-test "^1.1.0"

is-bigint@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/is-bigint/-/is-bigint-1.1.0.tgz"
  integrity sha512-n4ZT37wG78iz03xPRKJrHTdZbe3IicyucEtdRsV5yglwc3GyUfbAfpSeD0FJ41NbUNSt5wbhqfp1fS+BgnvDFQ==
  dependencies:
    has-bigints "^1.0.2"

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz"
  integrity sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==
  dependencies:
    binary-extensions "^2.0.0"

is-boolean-object@^1.2.1:
  version "1.2.2"
  resolved "https://registry.npmjs.org/is-boolean-object/-/is-boolean-object-1.2.2.tgz"
  integrity sha512-wa56o2/ElJMYqjCjGkXri7it5FbebW5usLw/nPmCMs5DeZ7eziSYZhSmPRn0txqeW4LnAmQQU7FgqLpsEFKM4A==
  dependencies:
    call-bound "^1.0.3"
    has-tostringtag "^1.0.2"

is-bun-module@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/is-bun-module/-/is-bun-module-2.0.0.tgz"
  integrity sha512-gNCGbnnnnFAUGKeZ9PdbyeGYJqewpmc2aKHUEMO5nQPWU9lOmv7jcmQIv+qHD8fXW6W7qfuCwX4rY9LNRjXrkQ==
  dependencies:
    semver "^7.7.1"

is-callable@^1.2.7:
  version "1.2.7"
  resolved "https://registry.npmjs.org/is-callable/-/is-callable-1.2.7.tgz"
  integrity sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA==

is-core-module@^2.13.0, is-core-module@^2.15.1, is-core-module@^2.16.0:
  version "2.16.1"
  resolved "https://registry.npmjs.org/is-core-module/-/is-core-module-2.16.1.tgz"
  integrity sha512-UfoeMA6fIJ8wTYFEUjelnaGI67v6+N7qXJEvQuIGa99l4xsCruSYOVSQ0uPANn4dAzm8lkYPaKLrrijLq7x23w==
  dependencies:
    hasown "^2.0.2"

is-data-view@^1.0.1, is-data-view@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/is-data-view/-/is-data-view-1.0.2.tgz"
  integrity sha512-RKtWF8pGmS87i2D6gqQu/l7EYRlVdfzemCJN/P3UOs//x1QE7mfhvzHIApBTRf7axvT6DMGwSwBXYCT0nfB9xw==
  dependencies:
    call-bound "^1.0.2"
    get-intrinsic "^1.2.6"
    is-typed-array "^1.1.13"

is-date-object@^1.0.5, is-date-object@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/is-date-object/-/is-date-object-1.1.0.tgz"
  integrity sha512-PwwhEakHVKTdRNVOw+/Gyh0+MzlCl4R6qKvkhuvLtPMggI1WAHt9sOwZxQLSGpUaDnrdyDsomoRgNnCfKNSXXg==
  dependencies:
    call-bound "^1.0.2"
    has-tostringtag "^1.0.2"

is-decimal@^1.0.0:
  version "1.0.4"
  resolved "https://registry.npmjs.org/is-decimal/-/is-decimal-1.0.4.tgz"
  integrity sha512-RGdriMmQQvZ2aqaQq3awNA6dCGtKpiDFcOzrTWrDAT2MiWrKQVPmxLGHl7Y2nNu6led0kEyoX0enY0qXYsv9zw==

is-decimal@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/is-decimal/-/is-decimal-2.0.1.tgz"
  integrity sha512-AAB9hiomQs5DXWcRB1rqsxGUstbRroFOPPVAomNk/3XHR5JyEZChOyTWe2oayKnsSsr/kcGqF+z6yuH6HHpN0A==

is-extglob@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz"
  integrity sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==

is-finalizationregistry@^1.1.0:
  version "1.1.1"
  resolved "https://registry.npmjs.org/is-finalizationregistry/-/is-finalizationregistry-1.1.1.tgz"
  integrity sha512-1pC6N8qWJbWoPtEjgcL2xyhQOP491EQjeUo3qTKcmV8YSDDJrOepfG8pcC7h/QgnQHYSv0mJ3Z/ZWxmatVrysg==
  dependencies:
    call-bound "^1.0.3"

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz"
  integrity sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==

is-generator-fn@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/is-generator-fn/-/is-generator-fn-2.1.0.tgz"
  integrity sha512-cTIB4yPYL/Grw0EaSzASzg6bBy9gqCofvWN8okThAYIxKJZC+udlRAmGbM0XLeniEJSs8uEgHPGuHSe1XsOLSQ==

is-generator-function@^1.0.10:
  version "1.1.0"
  resolved "https://registry.npmjs.org/is-generator-function/-/is-generator-function-1.1.0.tgz"
  integrity sha512-nPUB5km40q9e8UfN/Zc24eLlzdSf9OfKByBw9CIdw4H1giPMeA0OIJvbchsCu4npfI2QcMVBsGEBHKZ7wLTWmQ==
  dependencies:
    call-bound "^1.0.3"
    get-proto "^1.0.0"
    has-tostringtag "^1.0.2"
    safe-regex-test "^1.1.0"

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@^4.0.3, is-glob@~4.0.1:
  version "4.0.3"
  resolved "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz"
  integrity sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==
  dependencies:
    is-extglob "^2.1.1"

is-hexadecimal@^1.0.0:
  version "1.0.4"
  resolved "https://registry.npmjs.org/is-hexadecimal/-/is-hexadecimal-1.0.4.tgz"
  integrity sha512-gyPJuv83bHMpocVYoqof5VDiZveEoGoFL8m3BXNb2VW8Xs+rz9kqO8LOQ5DH6EsuvilT1ApazU0pyl+ytbPtlw==

is-hexadecimal@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/is-hexadecimal/-/is-hexadecimal-2.0.1.tgz"
  integrity sha512-DgZQp241c8oO6cA1SbTEWiXeoxV42vlcJxgH+B3hi1AiqqKruZR3ZGF8In3fj4+/y/7rHvlOZLZtgJ/4ttYGZg==

is-map@^2.0.2, is-map@^2.0.3:
  version "2.0.3"
  resolved "https://registry.npmjs.org/is-map/-/is-map-2.0.3.tgz"
  integrity sha512-1Qed0/Hr2m+YqxnM09CjA2d/i6YZNfF6R2oRAOj36eUdS6qIV/huPJNSEpKbupewFs+ZsJlxsjjPbc0/afW6Lw==

is-number-object@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/is-number-object/-/is-number-object-1.1.1.tgz"
  integrity sha512-lZhclumE1G6VYD8VHe35wFaIif+CTy5SJIi5+3y4psDgWu4wPDoBhF8NxUOinEc7pHgiTsT6MaBb92rKhhD+Xw==
  dependencies:
    call-bound "^1.0.3"
    has-tostringtag "^1.0.2"

is-number@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz"
  integrity sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==

is-plain-obj@^4.0.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/is-plain-obj/-/is-plain-obj-4.1.0.tgz"
  integrity sha512-+Pgi+vMuUNkJyExiMBt5IlFoMyKnr5zhJ4Uspz58WOhBF5QoIZkFyNHIbBAtHwzVAgk5RtndVNsDRN61/mmDqg==

is-potential-custom-element-name@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/is-potential-custom-element-name/-/is-potential-custom-element-name-1.0.1.tgz"
  integrity sha512-bCYeRA2rVibKZd+s2625gGnGF/t7DSqDs4dP7CrLA1m7jKWz6pps0LpYLJN8Q64HtmPKJ1hrN3nzPNKFEKOUiQ==

is-promise@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/is-promise/-/is-promise-4.0.0.tgz"
  integrity sha512-hvpoI6korhJMnej285dSg6nu1+e6uxs7zG3BYAm5byqDsgJNWwxzM6z6iZiAgQR4TJ30JmBTOwqZUw3WlyH3AQ==

is-reference@1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/is-reference/-/is-reference-1.2.1.tgz"
  integrity sha512-U82MsXXiFIrjCK4otLT+o2NA2Cd2g5MLoOVXUZjIOhLurrRxpEXzI8O0KZHr3IjLvlAH1kTPYSuqer5T9ZVBKQ==
  dependencies:
    "@types/estree" "*"

is-regex@^1.1.4, is-regex@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/is-regex/-/is-regex-1.2.1.tgz"
  integrity sha512-MjYsKHO5O7mCsmRGxWcLWheFqN9DJ/2TmngvjKXihe6efViPqc274+Fx/4fYj/r03+ESvBdTXK0V6tA3rgez1g==
  dependencies:
    call-bound "^1.0.2"
    gopd "^1.2.0"
    has-tostringtag "^1.0.2"
    hasown "^2.0.2"

is-set@^2.0.2, is-set@^2.0.3:
  version "2.0.3"
  resolved "https://registry.npmjs.org/is-set/-/is-set-2.0.3.tgz"
  integrity sha512-iPAjerrse27/ygGLxw+EBR9agv9Y6uLeYVJMu+QNCoouJ1/1ri0mGrcWpfCqFZuzzx3WjtwxG098X+n4OuRkPg==

is-shared-array-buffer@^1.0.2, is-shared-array-buffer@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmjs.org/is-shared-array-buffer/-/is-shared-array-buffer-1.0.4.tgz"
  integrity sha512-ISWac8drv4ZGfwKl5slpHG9OwPNty4jOWPRIhBpxOoD+hqITiwuipOQ2bNthAzwA3B4fIjO4Nln74N0S9byq8A==
  dependencies:
    call-bound "^1.0.3"

is-stream@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/is-stream/-/is-stream-2.0.1.tgz"
  integrity sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==

is-string@^1.0.7, is-string@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/is-string/-/is-string-1.1.1.tgz"
  integrity sha512-BtEeSsoaQjlSPBemMQIrY1MY0uM6vnS1g5fmufYOtnxLGUZM2178PKbhsk7Ffv58IX+ZtcvoGwccYsh0PglkAA==
  dependencies:
    call-bound "^1.0.3"
    has-tostringtag "^1.0.2"

is-symbol@^1.0.4, is-symbol@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/is-symbol/-/is-symbol-1.1.1.tgz"
  integrity sha512-9gGx6GTtCQM73BgmHQXfDmLtfjjTUDSyoxTCbp5WtoixAhfgsDirWIcVQ/IHpvI5Vgd5i/J5F7B9cN/WlVbC/w==
  dependencies:
    call-bound "^1.0.2"
    has-symbols "^1.1.0"
    safe-regex-test "^1.1.0"

is-typed-array@^1.1.13, is-typed-array@^1.1.14, is-typed-array@^1.1.15:
  version "1.1.15"
  resolved "https://registry.npmjs.org/is-typed-array/-/is-typed-array-1.1.15.tgz"
  integrity sha512-p3EcsicXjit7SaskXHs1hA91QxgTw46Fv6EFKKGS5DRFLD8yKnohjF3hxoju94b/OcMZoQukzpPpBE9uLVKzgQ==
  dependencies:
    which-typed-array "^1.1.16"

is-weakmap@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/is-weakmap/-/is-weakmap-2.0.2.tgz"
  integrity sha512-K5pXYOm9wqY1RgjpL3YTkF39tni1XajUIkawTLUo9EZEVUFga5gSQJF8nNS7ZwJQ02y+1YCNYcMh+HIf1ZqE+w==

is-weakref@^1.0.2, is-weakref@^1.1.0:
  version "1.1.1"
  resolved "https://registry.npmjs.org/is-weakref/-/is-weakref-1.1.1.tgz"
  integrity sha512-6i9mGWSlqzNMEqpCp93KwRS1uUOodk2OJ6b+sq7ZPDSy2WuI5NFIxp/254TytR8ftefexkWn5xNiHUNpPOfSew==
  dependencies:
    call-bound "^1.0.3"

is-weakset@^2.0.3:
  version "2.0.4"
  resolved "https://registry.npmjs.org/is-weakset/-/is-weakset-2.0.4.tgz"
  integrity sha512-mfcwb6IzQyOKTs84CQMrOwW4gQcaTOAWJ0zzJCl2WSPDrWk/OzDaImWFH3djXhb24g4eudZfLRozAvPGw4d9hQ==
  dependencies:
    call-bound "^1.0.3"
    get-intrinsic "^1.2.6"

isarray@^2.0.5:
  version "2.0.5"
  resolved "https://registry.npmjs.org/isarray/-/isarray-2.0.5.tgz"
  integrity sha512-xHjhDr3cNBK0BzdUJSPXZntQUx/mwMS5Rw4A7lPJ90XGAO6ISP/ePDNuo0vhqOZU+UD5JoodwCAAoZQd3FeAKw==

iserror@^0.0.2:
  version "0.0.2"
  resolved "https://registry.npmjs.org/iserror/-/iserror-0.0.2.tgz"
  integrity sha512-oKGGrFVaWwETimP3SiWwjDeY27ovZoyZPHtxblC4hCq9fXxed/jasx+ATWFFjCVSRZng8VTMsN1nDnGo6zMBSw==

isexe@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz"
  integrity sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==

istanbul-lib-coverage@^3.0.0, istanbul-lib-coverage@^3.2.0:
  version "3.2.2"
  resolved "https://registry.npmjs.org/istanbul-lib-coverage/-/istanbul-lib-coverage-3.2.2.tgz"
  integrity sha512-O8dpsF+r0WV/8MNRKfnmrtCWhuKjxrq2w+jpzBL5UZKTi2LeVWnWOmWRxFlesJONmc+wLAGvKQZEOanko0LFTg==

istanbul-lib-instrument@^5.0.4:
  version "5.2.1"
  resolved "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-5.2.1.tgz"
  integrity sha512-pzqtp31nLv/XFOzXGuvhCb8qhjmTVo5vjVk19XE4CRlSWz0KoeJ3bw9XsA7nOp9YBf4qHjwBxkDzKcME/J29Yg==
  dependencies:
    "@babel/core" "^7.12.3"
    "@babel/parser" "^7.14.7"
    "@istanbuljs/schema" "^0.1.2"
    istanbul-lib-coverage "^3.2.0"
    semver "^6.3.0"

istanbul-lib-instrument@^6.0.0:
  version "6.0.3"
  resolved "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-6.0.3.tgz"
  integrity sha512-Vtgk7L/R2JHyyGW07spoFlB8/lpjiOLTjMdms6AFMraYt3BaJauod/NGrfnVG/y4Ix1JEuMRPDPEj2ua+zz1/Q==
  dependencies:
    "@babel/core" "^7.23.9"
    "@babel/parser" "^7.23.9"
    "@istanbuljs/schema" "^0.1.3"
    istanbul-lib-coverage "^3.2.0"
    semver "^7.5.4"

istanbul-lib-report@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmjs.org/istanbul-lib-report/-/istanbul-lib-report-3.0.1.tgz"
  integrity sha512-GCfE1mtsHGOELCU8e/Z7YWzpmybrx/+dSTfLrvY8qRmaY6zXTKWn6WQIjaAFw069icm6GVMNkgu0NzI4iPZUNw==
  dependencies:
    istanbul-lib-coverage "^3.0.0"
    make-dir "^4.0.0"
    supports-color "^7.1.0"

istanbul-lib-source-maps@^4.0.0:
  version "4.0.1"
  resolved "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-4.0.1.tgz"
  integrity sha512-n3s8EwkdFIJCG3BPKBYvskgXGoy88ARzvegkitk60NxRdwltLOTaH7CUiMRXvwYorl0Q712iEjcWB+fK/MrWVw==
  dependencies:
    debug "^4.1.1"
    istanbul-lib-coverage "^3.0.0"
    source-map "^0.6.1"

istanbul-reports@^3.1.3:
  version "3.1.7"
  resolved "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-3.1.7.tgz"
  integrity sha512-BewmUXImeuRk2YY0PVbxgKAysvhRPUQE0h5QRM++nVWyubKGV0l8qQ5op8+B2DOmwSe63Jivj0BjkPQVf8fP5g==
  dependencies:
    html-escaper "^2.0.0"
    istanbul-lib-report "^3.0.0"

iterator.prototype@^1.1.4:
  version "1.1.5"
  resolved "https://registry.npmjs.org/iterator.prototype/-/iterator.prototype-1.1.5.tgz"
  integrity sha512-H0dkQoCa3b2VEeKQBOxFph+JAbcrQdE7KC0UkqwpLmv2EC4P41QXP+rqo9wYodACiG5/WM5s9oDApTU8utwj9g==
  dependencies:
    define-data-property "^1.1.4"
    es-object-atoms "^1.0.0"
    get-intrinsic "^1.2.6"
    get-proto "^1.0.0"
    has-symbols "^1.1.0"
    set-function-name "^2.0.2"

jackspeak@^3.1.2:
  version "3.4.3"
  resolved "https://registry.npmjs.org/jackspeak/-/jackspeak-3.4.3.tgz"
  integrity sha512-OGlZQpz2yfahA/Rd1Y8Cd9SIEsqvXkLVoSw/cgwhnhFMDbsQFeZYoJJ7bIZBS9BcamUW96asq/npPWugM+RQBw==
  dependencies:
    "@isaacs/cliui" "^8.0.2"
  optionalDependencies:
    "@pkgjs/parseargs" "^0.11.0"

jake@^10.8.5:
  version "10.9.2"
  resolved "https://registry.npmjs.org/jake/-/jake-10.9.2.tgz"
  integrity sha512-2P4SQ0HrLQ+fw6llpLnOaGAvN2Zu6778SJMrCUwns4fOoG9ayrTiZk3VV8sCPkVZF8ab0zksVpS8FDY5pRCNBA==
  dependencies:
    async "^3.2.3"
    chalk "^4.0.2"
    filelist "^1.0.4"
    minimatch "^3.1.2"

jest-changed-files@^29.7.0:
  version "29.7.0"
  resolved "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-29.7.0.tgz"
  integrity sha512-fEArFiwf1BpQ+4bXSprcDc3/x4HSzL4al2tozwVpDFpsxALjLYdyiIK4e5Vz66GQJIbXJ82+35PtysofptNX2w==
  dependencies:
    execa "^5.0.0"
    jest-util "^29.7.0"
    p-limit "^3.1.0"

jest-circus@^29.7.0:
  version "29.7.0"
  resolved "https://registry.npmjs.org/jest-circus/-/jest-circus-29.7.0.tgz"
  integrity sha512-3E1nCMgipcTkCocFwM90XXQab9bS+GMsjdpmPrlelaxwD93Ad8iVEjX/vvHPdLPnFf+L40u+5+iutRdA1N9myw==
  dependencies:
    "@jest/environment" "^29.7.0"
    "@jest/expect" "^29.7.0"
    "@jest/test-result" "^29.7.0"
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    chalk "^4.0.0"
    co "^4.6.0"
    dedent "^1.0.0"
    is-generator-fn "^2.0.0"
    jest-each "^29.7.0"
    jest-matcher-utils "^29.7.0"
    jest-message-util "^29.7.0"
    jest-runtime "^29.7.0"
    jest-snapshot "^29.7.0"
    jest-util "^29.7.0"
    p-limit "^3.1.0"
    pretty-format "^29.7.0"
    pure-rand "^6.0.0"
    slash "^3.0.0"
    stack-utils "^2.0.3"

jest-cli@^29.7.0:
  version "29.7.0"
  resolved "https://registry.npmjs.org/jest-cli/-/jest-cli-29.7.0.tgz"
  integrity sha512-OVVobw2IubN/GSYsxETi+gOe7Ka59EFMR/twOU3Jb2GnKKeMGJB5SGUUrEz3SFVmJASUdZUzy83sLNNQ2gZslg==
  dependencies:
    "@jest/core" "^29.7.0"
    "@jest/test-result" "^29.7.0"
    "@jest/types" "^29.6.3"
    chalk "^4.0.0"
    create-jest "^29.7.0"
    exit "^0.1.2"
    import-local "^3.0.2"
    jest-config "^29.7.0"
    jest-util "^29.7.0"
    jest-validate "^29.7.0"
    yargs "^17.3.1"

jest-config@^29.7.0:
  version "29.7.0"
  resolved "https://registry.npmjs.org/jest-config/-/jest-config-29.7.0.tgz"
  integrity sha512-uXbpfeQ7R6TZBqI3/TxCU4q4ttk3u0PJeC+E0zbfSoSjq6bJ7buBPxzQPL0ifrkY4DNu4JUdk0ImlBUYi840eQ==
  dependencies:
    "@babel/core" "^7.11.6"
    "@jest/test-sequencer" "^29.7.0"
    "@jest/types" "^29.6.3"
    babel-jest "^29.7.0"
    chalk "^4.0.0"
    ci-info "^3.2.0"
    deepmerge "^4.2.2"
    glob "^7.1.3"
    graceful-fs "^4.2.9"
    jest-circus "^29.7.0"
    jest-environment-node "^29.7.0"
    jest-get-type "^29.6.3"
    jest-regex-util "^29.6.3"
    jest-resolve "^29.7.0"
    jest-runner "^29.7.0"
    jest-util "^29.7.0"
    jest-validate "^29.7.0"
    micromatch "^4.0.4"
    parse-json "^5.2.0"
    pretty-format "^29.7.0"
    slash "^3.0.0"
    strip-json-comments "^3.1.1"

jest-diff@^29.7.0:
  version "29.7.0"
  resolved "https://registry.npmjs.org/jest-diff/-/jest-diff-29.7.0.tgz"
  integrity sha512-LMIgiIrhigmPrs03JHpxUh2yISK3vLFPkAodPeo0+BuF7wA2FoQbkEg1u8gBYBThncu7e1oEDUfIXVuTqLRUjw==
  dependencies:
    chalk "^4.0.0"
    diff-sequences "^29.6.3"
    jest-get-type "^29.6.3"
    pretty-format "^29.7.0"

jest-docblock@^29.7.0:
  version "29.7.0"
  resolved "https://registry.npmjs.org/jest-docblock/-/jest-docblock-29.7.0.tgz"
  integrity sha512-q617Auw3A612guyaFgsbFeYpNP5t2aoUNLwBUbc/0kD1R4t9ixDbyFTHd1nok4epoVFpr7PmeWHrhvuV3XaJ4g==
  dependencies:
    detect-newline "^3.0.0"

jest-each@^29.7.0:
  version "29.7.0"
  resolved "https://registry.npmjs.org/jest-each/-/jest-each-29.7.0.tgz"
  integrity sha512-gns+Er14+ZrEoC5fhOfYCY1LOHHr0TI+rQUHZS8Ttw2l7gl+80eHc/gFf2Ktkw0+SIACDTeWvpFcv3B04VembQ==
  dependencies:
    "@jest/types" "^29.6.3"
    chalk "^4.0.0"
    jest-get-type "^29.6.3"
    jest-util "^29.7.0"
    pretty-format "^29.7.0"

jest-environment-jsdom@^29.7.0:
  version "29.7.0"
  resolved "https://registry.npmjs.org/jest-environment-jsdom/-/jest-environment-jsdom-29.7.0.tgz"
  integrity sha512-k9iQbsf9OyOfdzWH8HDmrRT0gSIcX+FLNW7IQq94tFX0gynPwqDTW0Ho6iMVNjGz/nb+l/vW3dWM2bbLLpkbXA==
  dependencies:
    "@jest/environment" "^29.7.0"
    "@jest/fake-timers" "^29.7.0"
    "@jest/types" "^29.6.3"
    "@types/jsdom" "^20.0.0"
    "@types/node" "*"
    jest-mock "^29.7.0"
    jest-util "^29.7.0"
    jsdom "^20.0.0"

jest-environment-node@^29.7.0:
  version "29.7.0"
  resolved "https://registry.npmjs.org/jest-environment-node/-/jest-environment-node-29.7.0.tgz"
  integrity sha512-DOSwCRqXirTOyheM+4d5YZOrWcdu0LNZ87ewUoywbcb2XR4wKgqiG8vNeYwhjFMbEkfju7wx2GYH0P2gevGvFw==
  dependencies:
    "@jest/environment" "^29.7.0"
    "@jest/fake-timers" "^29.7.0"
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    jest-mock "^29.7.0"
    jest-util "^29.7.0"

jest-get-type@^29.6.3:
  version "29.6.3"
  resolved "https://registry.npmjs.org/jest-get-type/-/jest-get-type-29.6.3.tgz"
  integrity sha512-zrteXnqYxfQh7l5FHyL38jL39di8H8rHoecLH3JNxH3BwOrBsNeabdap5e0I23lD4HHI8W5VFBZqG4Eaq5LNcw==

jest-haste-map@^29.7.0:
  version "29.7.0"
  resolved "https://registry.npmjs.org/jest-haste-map/-/jest-haste-map-29.7.0.tgz"
  integrity sha512-fP8u2pyfqx0K1rGn1R9pyE0/KTn+G7PxktWidOBTqFPLYX0b9ksaMFkhK5vrS3DVun09pckLdlx90QthlW7AmA==
  dependencies:
    "@jest/types" "^29.6.3"
    "@types/graceful-fs" "^4.1.3"
    "@types/node" "*"
    anymatch "^3.0.3"
    fb-watchman "^2.0.0"
    graceful-fs "^4.2.9"
    jest-regex-util "^29.6.3"
    jest-util "^29.7.0"
    jest-worker "^29.7.0"
    micromatch "^4.0.4"
    walker "^1.0.8"
  optionalDependencies:
    fsevents "^2.3.2"

jest-leak-detector@^29.7.0:
  version "29.7.0"
  resolved "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-29.7.0.tgz"
  integrity sha512-kYA8IJcSYtST2BY9I+SMC32nDpBT3J2NvWJx8+JCuCdl/CR1I4EKUJROiP8XtCcxqgTTBGJNdbB1A8XRKbTetw==
  dependencies:
    jest-get-type "^29.6.3"
    pretty-format "^29.7.0"

jest-matcher-utils@^29.7.0:
  version "29.7.0"
  resolved "https://registry.npmjs.org/jest-matcher-utils/-/jest-matcher-utils-29.7.0.tgz"
  integrity sha512-sBkD+Xi9DtcChsI3L3u0+N0opgPYnCRPtGcQYrgXmR+hmt/fYfWAL0xRXYU8eWOdfuLgBe0YCW3AFtnRLagq/g==
  dependencies:
    chalk "^4.0.0"
    jest-diff "^29.7.0"
    jest-get-type "^29.6.3"
    pretty-format "^29.7.0"

jest-message-util@^29.7.0:
  version "29.7.0"
  resolved "https://registry.npmjs.org/jest-message-util/-/jest-message-util-29.7.0.tgz"
  integrity sha512-GBEV4GRADeP+qtB2+6u61stea8mGcOT4mCtrYISZwfu9/ISHFJ/5zOMXYbpBE9RsS5+Gb63DW4FgmnKJ79Kf6w==
  dependencies:
    "@babel/code-frame" "^7.12.13"
    "@jest/types" "^29.6.3"
    "@types/stack-utils" "^2.0.0"
    chalk "^4.0.0"
    graceful-fs "^4.2.9"
    micromatch "^4.0.4"
    pretty-format "^29.7.0"
    slash "^3.0.0"
    stack-utils "^2.0.3"

jest-mock@^29.7.0:
  version "29.7.0"
  resolved "https://registry.npmjs.org/jest-mock/-/jest-mock-29.7.0.tgz"
  integrity sha512-ITOMZn+UkYS4ZFh83xYAOzWStloNzJFO2s8DWrE4lhtGD+AorgnbkiKERe4wQVBydIGPx059g6riW5Btp6Llnw==
  dependencies:
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    jest-util "^29.7.0"

jest-pnp-resolver@^1.2.2:
  version "1.2.3"
  resolved "https://registry.npmjs.org/jest-pnp-resolver/-/jest-pnp-resolver-1.2.3.tgz"
  integrity sha512-+3NpwQEnRoIBtx4fyhblQDPgJI0H1IEIkX7ShLUjPGA7TtUTvI1oiKi3SR4oBR0hQhQR80l4WAe5RrXBwWMA8w==

jest-regex-util@^29.6.3:
  version "29.6.3"
  resolved "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-29.6.3.tgz"
  integrity sha512-KJJBsRCyyLNWCNBOvZyRDnAIfUiRJ8v+hOBQYGn8gDyF3UegwiP4gwRR3/SDa42g1YbVycTidUF3rKjyLFDWbg==

jest-resolve-dependencies@^29.7.0:
  version "29.7.0"
  resolved "https://registry.npmjs.org/jest-resolve-dependencies/-/jest-resolve-dependencies-29.7.0.tgz"
  integrity sha512-un0zD/6qxJ+S0et7WxeI3H5XSe9lTBBR7bOHCHXkKR6luG5mwDDlIzVQ0V5cZCuoTgEdcdwzTghYkTWfubi+nA==
  dependencies:
    jest-regex-util "^29.6.3"
    jest-snapshot "^29.7.0"

jest-resolve@^29.7.0:
  version "29.7.0"
  resolved "https://registry.npmjs.org/jest-resolve/-/jest-resolve-29.7.0.tgz"
  integrity sha512-IOVhZSrg+UvVAshDSDtHyFCCBUl/Q3AAJv8iZ6ZjnZ74xzvwuzLXid9IIIPgTnY62SJjfuupMKZsZQRsCvxEgA==
  dependencies:
    chalk "^4.0.0"
    graceful-fs "^4.2.9"
    jest-haste-map "^29.7.0"
    jest-pnp-resolver "^1.2.2"
    jest-util "^29.7.0"
    jest-validate "^29.7.0"
    resolve "^1.20.0"
    resolve.exports "^2.0.0"
    slash "^3.0.0"

jest-runner@^29.7.0:
  version "29.7.0"
  resolved "https://registry.npmjs.org/jest-runner/-/jest-runner-29.7.0.tgz"
  integrity sha512-fsc4N6cPCAahybGBfTRcq5wFR6fpLznMg47sY5aDpsoejOcVYFb07AHuSnR0liMcPTgBsA3ZJL6kFOjPdoNipQ==
  dependencies:
    "@jest/console" "^29.7.0"
    "@jest/environment" "^29.7.0"
    "@jest/test-result" "^29.7.0"
    "@jest/transform" "^29.7.0"
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    chalk "^4.0.0"
    emittery "^0.13.1"
    graceful-fs "^4.2.9"
    jest-docblock "^29.7.0"
    jest-environment-node "^29.7.0"
    jest-haste-map "^29.7.0"
    jest-leak-detector "^29.7.0"
    jest-message-util "^29.7.0"
    jest-resolve "^29.7.0"
    jest-runtime "^29.7.0"
    jest-util "^29.7.0"
    jest-watcher "^29.7.0"
    jest-worker "^29.7.0"
    p-limit "^3.1.0"
    source-map-support "0.5.13"

jest-runtime@^29.7.0:
  version "29.7.0"
  resolved "https://registry.npmjs.org/jest-runtime/-/jest-runtime-29.7.0.tgz"
  integrity sha512-gUnLjgwdGqW7B4LvOIkbKs9WGbn+QLqRQQ9juC6HndeDiezIwhDP+mhMwHWCEcfQ5RUXa6OPnFF8BJh5xegwwQ==
  dependencies:
    "@jest/environment" "^29.7.0"
    "@jest/fake-timers" "^29.7.0"
    "@jest/globals" "^29.7.0"
    "@jest/source-map" "^29.6.3"
    "@jest/test-result" "^29.7.0"
    "@jest/transform" "^29.7.0"
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    chalk "^4.0.0"
    cjs-module-lexer "^1.0.0"
    collect-v8-coverage "^1.0.0"
    glob "^7.1.3"
    graceful-fs "^4.2.9"
    jest-haste-map "^29.7.0"
    jest-message-util "^29.7.0"
    jest-mock "^29.7.0"
    jest-regex-util "^29.6.3"
    jest-resolve "^29.7.0"
    jest-snapshot "^29.7.0"
    jest-util "^29.7.0"
    slash "^3.0.0"
    strip-bom "^4.0.0"

jest-snapshot@^29.7.0:
  version "29.7.0"
  resolved "https://registry.npmjs.org/jest-snapshot/-/jest-snapshot-29.7.0.tgz"
  integrity sha512-Rm0BMWtxBcioHr1/OX5YCP8Uov4riHvKPknOGs804Zg9JGZgmIBkbtlxJC/7Z4msKYVbIJtfU+tKb8xlYNfdkw==
  dependencies:
    "@babel/core" "^7.11.6"
    "@babel/generator" "^7.7.2"
    "@babel/plugin-syntax-jsx" "^7.7.2"
    "@babel/plugin-syntax-typescript" "^7.7.2"
    "@babel/types" "^7.3.3"
    "@jest/expect-utils" "^29.7.0"
    "@jest/transform" "^29.7.0"
    "@jest/types" "^29.6.3"
    babel-preset-current-node-syntax "^1.0.0"
    chalk "^4.0.0"
    expect "^29.7.0"
    graceful-fs "^4.2.9"
    jest-diff "^29.7.0"
    jest-get-type "^29.6.3"
    jest-matcher-utils "^29.7.0"
    jest-message-util "^29.7.0"
    jest-util "^29.7.0"
    natural-compare "^1.4.0"
    pretty-format "^29.7.0"
    semver "^7.5.3"

jest-util@^29.0.0, jest-util@^29.7.0:
  version "29.7.0"
  resolved "https://registry.npmjs.org/jest-util/-/jest-util-29.7.0.tgz"
  integrity sha512-z6EbKajIpqGKU56y5KBUgy1dt1ihhQJgWzUlZHArA/+X2ad7Cb5iF+AK1EWVL/Bo7Rz9uurpqw6SiBCefUbCGA==
  dependencies:
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    chalk "^4.0.0"
    ci-info "^3.2.0"
    graceful-fs "^4.2.9"
    picomatch "^2.2.3"

jest-validate@^29.7.0:
  version "29.7.0"
  resolved "https://registry.npmjs.org/jest-validate/-/jest-validate-29.7.0.tgz"
  integrity sha512-ZB7wHqaRGVw/9hST/OuFUReG7M8vKeq0/J2egIGLdvjHCmYqGARhzXmtgi+gVeZ5uXFF219aOc3Ls2yLg27tkw==
  dependencies:
    "@jest/types" "^29.6.3"
    camelcase "^6.2.0"
    chalk "^4.0.0"
    jest-get-type "^29.6.3"
    leven "^3.1.0"
    pretty-format "^29.7.0"

jest-watcher@^29.7.0:
  version "29.7.0"
  resolved "https://registry.npmjs.org/jest-watcher/-/jest-watcher-29.7.0.tgz"
  integrity sha512-49Fg7WXkU3Vl2h6LbLtMQ/HyB6rXSIX7SqvBLQmssRBGN9I0PNvPmAmCWSOY6SOvrjhI/F7/bGAv9RtnsPA03g==
  dependencies:
    "@jest/test-result" "^29.7.0"
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    ansi-escapes "^4.2.1"
    chalk "^4.0.0"
    emittery "^0.13.1"
    jest-util "^29.7.0"
    string-length "^4.0.1"

jest-worker@^29.7.0:
  version "29.7.0"
  resolved "https://registry.npmjs.org/jest-worker/-/jest-worker-29.7.0.tgz"
  integrity sha512-eIz2msL/EzL9UFTFFx7jBTkeZfku0yUAyZZZmJ93H2TYEiroIx2PQjEXcwYtYl8zXCxb+PAmA2hLIt/6ZEkPHw==
  dependencies:
    "@types/node" "*"
    jest-util "^29.7.0"
    merge-stream "^2.0.0"
    supports-color "^8.0.0"

jest@^29.7.0:
  version "29.7.0"
  resolved "https://registry.npmjs.org/jest/-/jest-29.7.0.tgz"
  integrity sha512-NIy3oAFp9shda19hy4HK0HRTWKtPJmGdnvywu01nOqNC2vZg+Z+fvJDxpMQA88eb2I9EcafcdjYgsDthnYTvGw==
  dependencies:
    "@jest/core" "^29.7.0"
    "@jest/types" "^29.6.3"
    import-local "^3.0.2"
    jest-cli "^29.7.0"

jiti@^1.21.6:
  version "1.21.7"
  resolved "https://registry.npmjs.org/jiti/-/jiti-1.21.7.tgz"
  integrity sha512-/imKNG4EbWNrVjoNC/1H5/9GFy+tqjGBHCaSsN+P2RnPqjsLmv6UD3Ej+Kj8nBWaRAwyk7kK5ZUc+OEatnTR3A==

jose@^4.14.4:
  version "4.15.9"
  resolved "https://registry.npmjs.org/jose/-/jose-4.15.9.tgz"
  integrity sha512-1vUQX+IdDMVPj4k8kOxgUqlcK518yluMuGZwqlr44FS1ppZB/5GWh4rZG89erpOBOJjU/OBsnCVFfapsRz6nEA==

jotai@^2.11.1:
  version "2.12.4"
  resolved "https://registry.npmjs.org/jotai/-/jotai-2.12.4.tgz"
  integrity sha512-eFXLJol4oOLM8BS1+QV+XwaYQITG8n1tatBCFl4F5HE3zR5j2WIK8QpMt7VJIYmlogNUZfvB7wjwLoVk+umB9Q==

joycon@^3.1.1:
  version "3.1.1"
  resolved "https://registry.npmjs.org/joycon/-/joycon-3.1.1.tgz"
  integrity sha512-34wB/Y7MW7bzjKRjUKTa46I2Z7eV62Rkhva+KkopW7Qvv/OSWBqvkSY7vusOPrNuZcUG3tApvdVgNB8POj3SPw==

js-cookie@^2.2.1:
  version "2.2.1"
  resolved "https://registry.npmjs.org/js-cookie/-/js-cookie-2.2.1.tgz"
  integrity sha512-HvdH2LzI/EAZcUwA8+0nKNtWHqS+ZmijLA30RwZA0bo7ToCckjK5MkGhjED9KoRcXO6BaGI3I9UIzSA1FKFPOQ==

js-cookie@3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/js-cookie/-/js-cookie-3.0.1.tgz"
  integrity sha512-+0rgsUXZu4ncpPxRL+lNEptWMOWl9etvPHc/koSRp6MPwpRYAhmk0dUG00J4bxVV3r9uUzfo24wW0knS07SKSw==

js-tiktoken@^1.0.19:
  version "1.0.20"
  resolved "https://registry.npmjs.org/js-tiktoken/-/js-tiktoken-1.0.20.tgz"
  integrity sha512-Xlaqhhs8VfCd6Sh7a1cFkZHQbYTLCwVJJWiHVxBYzLPxW0XsoxBy1hitmjkdIjD3Aon5BXLHFwU5O8WUx6HH+A==
  dependencies:
    base64-js "^1.5.1"

"js-tokens@^3.0.0 || ^4.0.0", js-tokens@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz"
  integrity sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==

js-yaml@^3.13.1:
  version "3.14.1"
  resolved "https://registry.npmjs.org/js-yaml/-/js-yaml-3.14.1.tgz"
  integrity sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g==
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

js-yaml@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz"
  integrity sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==
  dependencies:
    argparse "^2.0.1"

jsdom@^20.0.0:
  version "20.0.3"
  resolved "https://registry.npmjs.org/jsdom/-/jsdom-20.0.3.tgz"
  integrity sha512-SYhBvTh89tTfCD/CRdSOm13mOBa42iTaTyfyEWBdKcGdPxPtLFBXuHR8XHb33YNYaP+lLbmSvBTsnoesCNJEsQ==
  dependencies:
    abab "^2.0.6"
    acorn "^8.8.1"
    acorn-globals "^7.0.0"
    cssom "^0.5.0"
    cssstyle "^2.3.0"
    data-urls "^3.0.2"
    decimal.js "^10.4.2"
    domexception "^4.0.0"
    escodegen "^2.0.0"
    form-data "^4.0.0"
    html-encoding-sniffer "^3.0.0"
    http-proxy-agent "^5.0.0"
    https-proxy-agent "^5.0.1"
    is-potential-custom-element-name "^1.0.1"
    nwsapi "^2.2.2"
    parse5 "^7.1.1"
    saxes "^6.0.0"
    symbol-tree "^3.2.4"
    tough-cookie "^4.1.2"
    w3c-xmlserializer "^4.0.0"
    webidl-conversions "^7.0.0"
    whatwg-encoding "^2.0.0"
    whatwg-mimetype "^3.0.0"
    whatwg-url "^11.0.0"
    ws "^8.11.0"
    xml-name-validator "^4.0.0"

jsesc@^3.0.2:
  version "3.1.0"
  resolved "https://registry.npmjs.org/jsesc/-/jsesc-3.1.0.tgz"
  integrity sha512-/sM3dO2FOzXjKQhJuo0Q173wf2KOo8t4I8vHy6lF9poUp7bKT0/NHE8fPX23PwfhnykfqnC2xRxOnVw5XuGIaA==

jsesc@~3.0.2:
  version "3.0.2"
  resolved "https://registry.npmjs.org/jsesc/-/jsesc-3.0.2.tgz"
  integrity sha512-xKqzzWXDttJuOcawBt4KnKHHIf5oQ/Cxax+0PWFG+DFDgHNAdi+TXECADI+RYiFUMmx8792xsMbbgXj4CwnP4g==

json-bigint@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/json-bigint/-/json-bigint-1.0.0.tgz"
  integrity sha512-SiPv/8VpZuWbvLSMtTDU8hEfrZWg/mH/nV/b4o0CYbSxu1UIQPLdwKOCIyLQX+VIPO5vrLX3i8qtqFyhdPSUSQ==
  dependencies:
    bignumber.js "^9.0.0"

json-buffer@3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/json-buffer/-/json-buffer-3.0.1.tgz"
  integrity sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==

json-parse-even-better-errors@^2.3.0:
  version "2.3.1"
  resolved "https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz"
  integrity sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz"
  integrity sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==

json-schema@^0.4.0:
  version "0.4.0"
  resolved "https://registry.npmjs.org/json-schema/-/json-schema-0.4.0.tgz"
  integrity sha512-es94M3nTIfsEPisRafak+HDLfHXnKBhV3vU5eqPcS3flIWqcxJWgXHXiey3YrpaNsanY5ei1VoYEbOzijuq9BA==

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz"
  integrity sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==

json5@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/json5/-/json5-1.0.2.tgz"
  integrity sha512-g1MWMLBiz8FKi1e4w0UyVL3w+iJceWAFBAaBnnGKOpNa5f8TLktkbre1+s6oICydWAm+HRUGTmI+//xv2hvXYA==
  dependencies:
    minimist "^1.2.0"

json5@^2.2.3:
  version "2.2.3"
  resolved "https://registry.npmjs.org/json5/-/json5-2.2.3.tgz"
  integrity sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==

jsondiffpatch@0.6.0:
  version "0.6.0"
  resolved "https://registry.npmjs.org/jsondiffpatch/-/jsondiffpatch-0.6.0.tgz"
  integrity sha512-3QItJOXp2AP1uv7waBkao5nCvhEv+QmJAd38Ybq7wNI74Q+BBmnLn4EDKz6yI9xGAIQoUF87qHt+kc1IVxB4zQ==
  dependencies:
    "@types/diff-match-patch" "^1.0.36"
    chalk "^5.3.0"
    diff-match-patch "^1.0.5"

"jsx-ast-utils@^2.4.1 || ^3.0.0", jsx-ast-utils@^3.3.5:
  version "3.3.5"
  resolved "https://registry.npmjs.org/jsx-ast-utils/-/jsx-ast-utils-3.3.5.tgz"
  integrity sha512-ZZow9HBI5O6EPgSJLUb8n2NKgmVWTwCvHGwFuJlMjvLFqlGG6pjirPhtdsseaLZjSibD8eegzmYpUZwoIlj2cQ==
  dependencies:
    array-includes "^3.1.6"
    array.prototype.flat "^1.3.1"
    object.assign "^4.1.4"
    object.values "^1.1.6"

keyv@^4.5.4:
  version "4.5.4"
  resolved "https://registry.npmjs.org/keyv/-/keyv-4.5.4.tgz"
  integrity sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw==
  dependencies:
    json-buffer "3.0.1"

kleur@^3.0.3:
  version "3.0.3"
  resolved "https://registry.npmjs.org/kleur/-/kleur-3.0.3.tgz"
  integrity sha512-eTIzlVOSUR+JxdDFepEYcBMtZ9Qqdef+rnzWdRZuMbOywu5tO2w2N7rqjoANZ5k9vywhL6Br1VRjUIgTQx4E8w==

language-subtag-registry@^0.3.20:
  version "0.3.23"
  resolved "https://registry.npmjs.org/language-subtag-registry/-/language-subtag-registry-0.3.23.tgz"
  integrity sha512-0K65Lea881pHotoGEa5gDlMxt3pctLi2RplBb7Ezh4rRdLEOtgi7n4EwK9lamnUCkKBqaeKRVebTq6BAxSkpXQ==

language-tags@^1.0.9:
  version "1.0.9"
  resolved "https://registry.npmjs.org/language-tags/-/language-tags-1.0.9.tgz"
  integrity sha512-MbjN408fEndfiQXbFQ1vnd+1NoLDsnQW41410oQBXiyXDMYH5z505juWa4KUE1LqxRC7DgOgZDbKLxHIwm27hA==
  dependencies:
    language-subtag-registry "^0.3.20"

leven@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/leven/-/leven-3.1.0.tgz"
  integrity sha512-qsda+H8jTaUaN/x5vzW2rzc+8Rw4TAQ/4KjB46IwK5VH+IlVeeeje/EoZRpiXvIqjFgK84QffqPztGI3VBLG1A==

levn@^0.4.1:
  version "0.4.1"
  resolved "https://registry.npmjs.org/levn/-/levn-0.4.1.tgz"
  integrity sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==
  dependencies:
    prelude-ls "^1.2.1"
    type-check "~0.4.0"

lilconfig@^3.0.0, lilconfig@^3.1.3:
  version "3.1.3"
  resolved "https://registry.npmjs.org/lilconfig/-/lilconfig-3.1.3.tgz"
  integrity sha512-/vlFKAoH5Cgt3Ie+JLhRbwOsCQePABiU3tJ1egGvyQ+33R/vcwM2Zl2QR/LzjsBeItPt3oSVXapn+m4nQDvpzw==

lines-and-columns@^1.1.6:
  version "1.2.4"
  resolved "https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.2.4.tgz"
  integrity sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==

locate-path@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/locate-path/-/locate-path-5.0.0.tgz"
  integrity sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==
  dependencies:
    p-locate "^4.1.0"

locate-path@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmjs.org/locate-path/-/locate-path-6.0.0.tgz"
  integrity sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==
  dependencies:
    p-locate "^5.0.0"

lodash.camelcase@^4.3.0:
  version "4.3.0"
  resolved "https://registry.npmjs.org/lodash.camelcase/-/lodash.camelcase-4.3.0.tgz"
  integrity sha512-TwuEnCnxbc3rAvhf/LbG7tJUDzhqXyFnv3dtzLOPgCG/hODL7WFnsbwktkD7yUV0RrreP/l1PALq/YSg6VvjlA==

lodash.debounce@^4.0.8:
  version "4.0.8"
  resolved "https://registry.npmjs.org/lodash.debounce/-/lodash.debounce-4.0.8.tgz"
  integrity sha512-FT1yDzDYEoYWhnSGnpE/4Kj1fLZkDFyqRb7fNt6FdYOSxlUWAtp42Eh6Wb0rGIv/m9Bgo7x4GhQbm5Ys4SG5ow==

lodash.memoize@^4.1.2:
  version "4.1.2"
  resolved "https://registry.npmjs.org/lodash.memoize/-/lodash.memoize-4.1.2.tgz"
  integrity sha512-t7j+NzmgnQzTAYXcsHYLgimltOV1MXHtlOWf6GjL9Kj8GK5FInw5JotxvbOs+IvV1/Dzo04/fCGfLVs7aXb4Ag==

lodash.merge@^4.6.2:
  version "4.6.2"
  resolved "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.6.2.tgz"
  integrity sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==

lodash@^4.17.21:
  version "4.17.21"
  resolved "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz"
  integrity sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==

long@^5.0.0:
  version "5.3.2"
  resolved "https://registry.npmjs.org/long/-/long-5.3.2.tgz"
  integrity sha512-mNAgZ1GmyNhD7AuqnTG3/VQ26o760+ZYBPKjPvugO8+nLbYfX6TVpJPseBvopbdY+qpZ/lKUnmEc1LeZYS3QAA==

longest-streak@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/longest-streak/-/longest-streak-3.1.0.tgz"
  integrity sha512-9Ri+o0JYgehTaVBBDoMqIl8GXtbWg711O3srftcHhZ0dqnETqLaoIK0x17fUw9rFSlK/0NlsKe0Ahhyl5pXE2g==

loose-envify@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz"
  integrity sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==
  dependencies:
    js-tokens "^3.0.0 || ^4.0.0"

lowlight@^1.17.0:
  version "1.20.0"
  resolved "https://registry.npmjs.org/lowlight/-/lowlight-1.20.0.tgz"
  integrity sha512-8Ktj+prEb1RoCPkEOrPMYUN/nCggB7qAWe3a7OpMjWQkh3l2RD5wKRQ+o8Q8YuI9RG/xs95waaI/E6ym/7NsTw==
  dependencies:
    fault "^1.0.0"
    highlight.js "~10.7.0"

lru-cache@^10.2.0:
  version "10.4.3"
  resolved "https://registry.npmjs.org/lru-cache/-/lru-cache-10.4.3.tgz"
  integrity sha512-JNAzZcXrCt42VGLuYz0zfAzDfAvJWW6AfYlDBQyDV5DClI2m5sAmK+OIO7s59XfsRsWHp02jAJrRadPRGTt6SQ==

lru-cache@^5.1.1:
  version "5.1.1"
  resolved "https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz"
  integrity sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==
  dependencies:
    yallist "^3.0.2"

lucide-react@^0.510.0:
  version "0.510.0"
  resolved "https://registry.npmjs.org/lucide-react/-/lucide-react-0.510.0.tgz"
  integrity sha512-p8SQRAMVh7NhsAIETokSqDrc5CHnDLbV29mMnzaXx+Vc/hnqQzwI2r0FMWCcoTXnbw2KEjy48xwpGdEL+ck06Q==

lz-string@^1.5.0:
  version "1.5.0"
  resolved "https://registry.npmjs.org/lz-string/-/lz-string-1.5.0.tgz"
  integrity sha512-h5bgJWpxJNswbU7qCrV0tIKQCaS3blPDrqKWx+QxzuzL1zGUzij9XCWLrSLsJPu5t+eWA/ycetzYAO5IOMcWAQ==

magic-string@^0.30.3:
  version "0.30.17"
  resolved "https://registry.npmjs.org/magic-string/-/magic-string-0.30.17.tgz"
  integrity sha512-sNPKHvyjVf7gyjwS4xGTaW/mCnF8wnjtifKBEhxfZ7E/S8tQ0rssrwGNn6q8JH/ohItJfSQp9mBtQYuTlH5QnA==
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.5.0"

magic-string@0.30.8:
  version "0.30.8"
  resolved "https://registry.npmjs.org/magic-string/-/magic-string-0.30.8.tgz"
  integrity sha512-ISQTe55T2ao7XtlAStud6qwYPZjE4GK1S/BeVPus4jrq6JuOnQ00YKQC581RWhR122W7msZV263KzVeLoqidyQ==
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.4.15"

make-dir@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/make-dir/-/make-dir-4.0.0.tgz"
  integrity sha512-hXdUTZYIVOt1Ex//jAQi+wTZZpUpwBj/0QsOzqegb3rGMMeJiSEu5xLHnYfBrRV4RH2+OCSOO95Is/7x1WJ4bw==
  dependencies:
    semver "^7.5.3"

make-error@^1.1.1, make-error@^1.3.6:
  version "1.3.6"
  resolved "https://registry.npmjs.org/make-error/-/make-error-1.3.6.tgz"
  integrity sha512-s8UhlNe7vPKomQhC1qFelMokr/Sc3AgNbso3n74mVPA5LTZwkB9NlXf4XPamLxJE8h0gh73rM94xvwRT2CVInw==

makeerror@1.0.12:
  version "1.0.12"
  resolved "https://registry.npmjs.org/makeerror/-/makeerror-1.0.12.tgz"
  integrity sha512-JmqCvUhmt43madlpFzG4BQzG2Z3m6tvQDNKdClZnO3VbIudJYmxsT0FNJMeiB2+JTSlTQTSbU8QdesVmwJcmLg==
  dependencies:
    tmpl "1.0.5"

markdown-table@^3.0.0:
  version "3.0.4"
  resolved "https://registry.npmjs.org/markdown-table/-/markdown-table-3.0.4.tgz"
  integrity sha512-wiYz4+JrLyb/DqW2hkFJxP7Vd7JuTDm77fvbM8VfEQdmSMqcImWeeRbHwZjBjIFki/VaMK2BhFi7oUUZeM5bqw==

math-intrinsics@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/math-intrinsics/-/math-intrinsics-1.1.0.tgz"
  integrity sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==

mdast-util-find-and-replace@^3.0.0:
  version "3.0.2"
  resolved "https://registry.npmjs.org/mdast-util-find-and-replace/-/mdast-util-find-and-replace-3.0.2.tgz"
  integrity sha512-Tmd1Vg/m3Xz43afeNxDIhWRtFZgM2VLyaf4vSTYwudTyeuTneoL3qtWMA5jeLyz/O1vDJmmV4QuScFCA2tBPwg==
  dependencies:
    "@types/mdast" "^4.0.0"
    escape-string-regexp "^5.0.0"
    unist-util-is "^6.0.0"
    unist-util-visit-parents "^6.0.0"

mdast-util-from-markdown@^2.0.0:
  version "2.0.2"
  resolved "https://registry.npmjs.org/mdast-util-from-markdown/-/mdast-util-from-markdown-2.0.2.tgz"
  integrity sha512-uZhTV/8NBuw0WHkPTrCqDOl0zVe1BIng5ZtHoDk49ME1qqcjYmmLmOf0gELgcRMxN4w2iuIeVso5/6QymSrgmA==
  dependencies:
    "@types/mdast" "^4.0.0"
    "@types/unist" "^3.0.0"
    decode-named-character-reference "^1.0.0"
    devlop "^1.0.0"
    mdast-util-to-string "^4.0.0"
    micromark "^4.0.0"
    micromark-util-decode-numeric-character-reference "^2.0.0"
    micromark-util-decode-string "^2.0.0"
    micromark-util-normalize-identifier "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"
    unist-util-stringify-position "^4.0.0"

mdast-util-gfm-autolink-literal@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/mdast-util-gfm-autolink-literal/-/mdast-util-gfm-autolink-literal-2.0.1.tgz"
  integrity sha512-5HVP2MKaP6L+G6YaxPNjuL0BPrq9orG3TsrZ9YXbA3vDw/ACI4MEsnoDpn6ZNm7GnZgtAcONJyPhOP8tNJQavQ==
  dependencies:
    "@types/mdast" "^4.0.0"
    ccount "^2.0.0"
    devlop "^1.0.0"
    mdast-util-find-and-replace "^3.0.0"
    micromark-util-character "^2.0.0"

mdast-util-gfm-footnote@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/mdast-util-gfm-footnote/-/mdast-util-gfm-footnote-2.1.0.tgz"
  integrity sha512-sqpDWlsHn7Ac9GNZQMeUzPQSMzR6Wv0WKRNvQRg0KqHh02fpTz69Qc1QSseNX29bhz1ROIyNyxExfawVKTm1GQ==
  dependencies:
    "@types/mdast" "^4.0.0"
    devlop "^1.1.0"
    mdast-util-from-markdown "^2.0.0"
    mdast-util-to-markdown "^2.0.0"
    micromark-util-normalize-identifier "^2.0.0"

mdast-util-gfm-strikethrough@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/mdast-util-gfm-strikethrough/-/mdast-util-gfm-strikethrough-2.0.0.tgz"
  integrity sha512-mKKb915TF+OC5ptj5bJ7WFRPdYtuHv0yTRxK2tJvi+BDqbkiG7h7u/9SI89nRAYcmap2xHQL9D+QG/6wSrTtXg==
  dependencies:
    "@types/mdast" "^4.0.0"
    mdast-util-from-markdown "^2.0.0"
    mdast-util-to-markdown "^2.0.0"

mdast-util-gfm-table@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/mdast-util-gfm-table/-/mdast-util-gfm-table-2.0.0.tgz"
  integrity sha512-78UEvebzz/rJIxLvE7ZtDd/vIQ0RHv+3Mh5DR96p7cS7HsBhYIICDBCu8csTNWNO6tBWfqXPWekRuj2FNOGOZg==
  dependencies:
    "@types/mdast" "^4.0.0"
    devlop "^1.0.0"
    markdown-table "^3.0.0"
    mdast-util-from-markdown "^2.0.0"
    mdast-util-to-markdown "^2.0.0"

mdast-util-gfm-task-list-item@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/mdast-util-gfm-task-list-item/-/mdast-util-gfm-task-list-item-2.0.0.tgz"
  integrity sha512-IrtvNvjxC1o06taBAVJznEnkiHxLFTzgonUdy8hzFVeDun0uTjxxrRGVaNFqkU1wJR3RBPEfsxmU6jDWPofrTQ==
  dependencies:
    "@types/mdast" "^4.0.0"
    devlop "^1.0.0"
    mdast-util-from-markdown "^2.0.0"
    mdast-util-to-markdown "^2.0.0"

mdast-util-gfm@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/mdast-util-gfm/-/mdast-util-gfm-3.1.0.tgz"
  integrity sha512-0ulfdQOM3ysHhCJ1p06l0b0VKlhU0wuQs3thxZQagjcjPrlFRqY215uZGHHJan9GEAXd9MbfPjFJz+qMkVR6zQ==
  dependencies:
    mdast-util-from-markdown "^2.0.0"
    mdast-util-gfm-autolink-literal "^2.0.0"
    mdast-util-gfm-footnote "^2.0.0"
    mdast-util-gfm-strikethrough "^2.0.0"
    mdast-util-gfm-table "^2.0.0"
    mdast-util-gfm-task-list-item "^2.0.0"
    mdast-util-to-markdown "^2.0.0"

mdast-util-mdx-expression@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/mdast-util-mdx-expression/-/mdast-util-mdx-expression-2.0.1.tgz"
  integrity sha512-J6f+9hUp+ldTZqKRSg7Vw5V6MqjATc+3E4gf3CFNcuZNWD8XdyI6zQ8GqH7f8169MM6P7hMBRDVGnn7oHB9kXQ==
  dependencies:
    "@types/estree-jsx" "^1.0.0"
    "@types/hast" "^3.0.0"
    "@types/mdast" "^4.0.0"
    devlop "^1.0.0"
    mdast-util-from-markdown "^2.0.0"
    mdast-util-to-markdown "^2.0.0"

mdast-util-mdx-jsx@^3.0.0:
  version "3.2.0"
  resolved "https://registry.npmjs.org/mdast-util-mdx-jsx/-/mdast-util-mdx-jsx-3.2.0.tgz"
  integrity sha512-lj/z8v0r6ZtsN/cGNNtemmmfoLAFZnjMbNyLzBafjzikOM+glrjNHPlf6lQDOTccj9n5b0PPihEBbhneMyGs1Q==
  dependencies:
    "@types/estree-jsx" "^1.0.0"
    "@types/hast" "^3.0.0"
    "@types/mdast" "^4.0.0"
    "@types/unist" "^3.0.0"
    ccount "^2.0.0"
    devlop "^1.1.0"
    mdast-util-from-markdown "^2.0.0"
    mdast-util-to-markdown "^2.0.0"
    parse-entities "^4.0.0"
    stringify-entities "^4.0.0"
    unist-util-stringify-position "^4.0.0"
    vfile-message "^4.0.0"

mdast-util-mdxjs-esm@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/mdast-util-mdxjs-esm/-/mdast-util-mdxjs-esm-2.0.1.tgz"
  integrity sha512-EcmOpxsZ96CvlP03NghtH1EsLtr0n9Tm4lPUJUBccV9RwUOneqSycg19n5HGzCf+10LozMRSObtVr3ee1WoHtg==
  dependencies:
    "@types/estree-jsx" "^1.0.0"
    "@types/hast" "^3.0.0"
    "@types/mdast" "^4.0.0"
    devlop "^1.0.0"
    mdast-util-from-markdown "^2.0.0"
    mdast-util-to-markdown "^2.0.0"

mdast-util-phrasing@^4.0.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/mdast-util-phrasing/-/mdast-util-phrasing-4.1.0.tgz"
  integrity sha512-TqICwyvJJpBwvGAMZjj4J2n0X8QWp21b9l0o7eXyVJ25YNWYbJDVIyD1bZXE6WtV6RmKJVYmQAKWa0zWOABz2w==
  dependencies:
    "@types/mdast" "^4.0.0"
    unist-util-is "^6.0.0"

mdast-util-to-hast@^13.0.0:
  version "13.2.0"
  resolved "https://registry.npmjs.org/mdast-util-to-hast/-/mdast-util-to-hast-13.2.0.tgz"
  integrity sha512-QGYKEuUsYT9ykKBCMOEDLsU5JRObWQusAolFMeko/tYPufNkRffBAQjIE+99jbA87xv6FgmjLtwjh9wBWajwAA==
  dependencies:
    "@types/hast" "^3.0.0"
    "@types/mdast" "^4.0.0"
    "@ungap/structured-clone" "^1.0.0"
    devlop "^1.0.0"
    micromark-util-sanitize-uri "^2.0.0"
    trim-lines "^3.0.0"
    unist-util-position "^5.0.0"
    unist-util-visit "^5.0.0"
    vfile "^6.0.0"

mdast-util-to-markdown@^2.0.0:
  version "2.1.2"
  resolved "https://registry.npmjs.org/mdast-util-to-markdown/-/mdast-util-to-markdown-2.1.2.tgz"
  integrity sha512-xj68wMTvGXVOKonmog6LwyJKrYXZPvlwabaryTjLh9LuvovB/KAH+kvi8Gjj+7rJjsFi23nkUxRQv1KqSroMqA==
  dependencies:
    "@types/mdast" "^4.0.0"
    "@types/unist" "^3.0.0"
    longest-streak "^3.0.0"
    mdast-util-phrasing "^4.0.0"
    mdast-util-to-string "^4.0.0"
    micromark-util-classify-character "^2.0.0"
    micromark-util-decode-string "^2.0.0"
    unist-util-visit "^5.0.0"
    zwitch "^2.0.0"

mdast-util-to-string@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/mdast-util-to-string/-/mdast-util-to-string-4.0.0.tgz"
  integrity sha512-0H44vDimn51F0YwvxSJSm0eCDOJTRlmN0R1yBh4HLj9wiV1Dn0QoXGbvFAWj2hSItVTlCmBF1hqKlIyUBVFLPg==
  dependencies:
    "@types/mdast" "^4.0.0"

mdn-data@2.0.14:
  version "2.0.14"
  resolved "https://registry.npmjs.org/mdn-data/-/mdn-data-2.0.14.tgz"
  integrity sha512-dn6wd0uw5GsdswPFfsgMp5NSB0/aDe6fK94YJV/AJDYXL6HVLWBsxeq7js7Ad+mU2K9LAlwpk6kN2D5mwCPVow==

media-typer@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/media-typer/-/media-typer-1.1.0.tgz"
  integrity sha512-aisnrDP4GNe06UcKFnV5bfMNPBUw4jsLGaWwWfnH3v02GnBuXX2MCVn5RbrWo0j3pczUilYblq7fQ7Nw2t5XKw==

merge-descriptors@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-2.0.0.tgz"
  integrity sha512-Snk314V5ayFLhp3fkUREub6WtjBfPdCPY1Ln8/8munuLuiYhsABgBVWsozAG+MWMbVEvcdcpbi9R7ww22l9Q3g==

merge-stream@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/merge-stream/-/merge-stream-2.0.0.tgz"
  integrity sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==

merge2@^1.3.0:
  version "1.4.1"
  resolved "https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz"
  integrity sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==

micromark-core-commonmark@^2.0.0:
  version "2.0.3"
  resolved "https://registry.npmjs.org/micromark-core-commonmark/-/micromark-core-commonmark-2.0.3.tgz"
  integrity sha512-RDBrHEMSxVFLg6xvnXmb1Ayr2WzLAWjeSATAoxwKYJV94TeNavgoIdA0a9ytzDSVzBy2YKFK+emCPOEibLeCrg==
  dependencies:
    decode-named-character-reference "^1.0.0"
    devlop "^1.0.0"
    micromark-factory-destination "^2.0.0"
    micromark-factory-label "^2.0.0"
    micromark-factory-space "^2.0.0"
    micromark-factory-title "^2.0.0"
    micromark-factory-whitespace "^2.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-chunked "^2.0.0"
    micromark-util-classify-character "^2.0.0"
    micromark-util-html-tag-name "^2.0.0"
    micromark-util-normalize-identifier "^2.0.0"
    micromark-util-resolve-all "^2.0.0"
    micromark-util-subtokenize "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-extension-gfm-autolink-literal@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/micromark-extension-gfm-autolink-literal/-/micromark-extension-gfm-autolink-literal-2.1.0.tgz"
  integrity sha512-oOg7knzhicgQ3t4QCjCWgTmfNhvQbDDnJeVu9v81r7NltNCVmhPy1fJRX27pISafdjL+SVc4d3l48Gb6pbRypw==
  dependencies:
    micromark-util-character "^2.0.0"
    micromark-util-sanitize-uri "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-extension-gfm-footnote@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/micromark-extension-gfm-footnote/-/micromark-extension-gfm-footnote-2.1.0.tgz"
  integrity sha512-/yPhxI1ntnDNsiHtzLKYnE3vf9JZ6cAisqVDauhp4CEHxlb4uoOTxOCJ+9s51bIB8U1N1FJ1RXOKTIlD5B/gqw==
  dependencies:
    devlop "^1.0.0"
    micromark-core-commonmark "^2.0.0"
    micromark-factory-space "^2.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-normalize-identifier "^2.0.0"
    micromark-util-sanitize-uri "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-extension-gfm-strikethrough@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/micromark-extension-gfm-strikethrough/-/micromark-extension-gfm-strikethrough-2.1.0.tgz"
  integrity sha512-ADVjpOOkjz1hhkZLlBiYA9cR2Anf8F4HqZUO6e5eDcPQd0Txw5fxLzzxnEkSkfnD0wziSGiv7sYhk/ktvbf1uw==
  dependencies:
    devlop "^1.0.0"
    micromark-util-chunked "^2.0.0"
    micromark-util-classify-character "^2.0.0"
    micromark-util-resolve-all "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-extension-gfm-table@^2.0.0:
  version "2.1.1"
  resolved "https://registry.npmjs.org/micromark-extension-gfm-table/-/micromark-extension-gfm-table-2.1.1.tgz"
  integrity sha512-t2OU/dXXioARrC6yWfJ4hqB7rct14e8f7m0cbI5hUmDyyIlwv5vEtooptH8INkbLzOatzKuVbQmAYcbWoyz6Dg==
  dependencies:
    devlop "^1.0.0"
    micromark-factory-space "^2.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-extension-gfm-tagfilter@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/micromark-extension-gfm-tagfilter/-/micromark-extension-gfm-tagfilter-2.0.0.tgz"
  integrity sha512-xHlTOmuCSotIA8TW1mDIM6X2O1SiX5P9IuDtqGonFhEK0qgRI4yeC6vMxEV2dgyr2TiD+2PQ10o+cOhdVAcwfg==
  dependencies:
    micromark-util-types "^2.0.0"

micromark-extension-gfm-task-list-item@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/micromark-extension-gfm-task-list-item/-/micromark-extension-gfm-task-list-item-2.1.0.tgz"
  integrity sha512-qIBZhqxqI6fjLDYFTBIa4eivDMnP+OZqsNwmQ3xNLE4Cxwc+zfQEfbs6tzAo2Hjq+bh6q5F+Z8/cksrLFYWQQw==
  dependencies:
    devlop "^1.0.0"
    micromark-factory-space "^2.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-extension-gfm@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/micromark-extension-gfm/-/micromark-extension-gfm-3.0.0.tgz"
  integrity sha512-vsKArQsicm7t0z2GugkCKtZehqUm31oeGBV/KVSorWSy8ZlNAv7ytjFhvaryUiCUJYqs+NoE6AFhpQvBTM6Q4w==
  dependencies:
    micromark-extension-gfm-autolink-literal "^2.0.0"
    micromark-extension-gfm-footnote "^2.0.0"
    micromark-extension-gfm-strikethrough "^2.0.0"
    micromark-extension-gfm-table "^2.0.0"
    micromark-extension-gfm-tagfilter "^2.0.0"
    micromark-extension-gfm-task-list-item "^2.0.0"
    micromark-util-combine-extensions "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-factory-destination@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/micromark-factory-destination/-/micromark-factory-destination-2.0.1.tgz"
  integrity sha512-Xe6rDdJlkmbFRExpTOmRj9N3MaWmbAgdpSrBQvCFqhezUn4AHqJHbaEnfbVYYiexVSs//tqOdY/DxhjdCiJnIA==
  dependencies:
    micromark-util-character "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-factory-label@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/micromark-factory-label/-/micromark-factory-label-2.0.1.tgz"
  integrity sha512-VFMekyQExqIW7xIChcXn4ok29YE3rnuyveW3wZQWWqF4Nv9Wk5rgJ99KzPvHjkmPXF93FXIbBp6YdW3t71/7Vg==
  dependencies:
    devlop "^1.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-factory-space@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/micromark-factory-space/-/micromark-factory-space-2.0.1.tgz"
  integrity sha512-zRkxjtBxxLd2Sc0d+fbnEunsTj46SWXgXciZmHq0kDYGnck/ZSGj9/wULTV95uoeYiK5hRXP2mJ98Uo4cq/LQg==
  dependencies:
    micromark-util-character "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-factory-title@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/micromark-factory-title/-/micromark-factory-title-2.0.1.tgz"
  integrity sha512-5bZ+3CjhAd9eChYTHsjy6TGxpOFSKgKKJPJxr293jTbfry2KDoWkhBb6TcPVB4NmzaPhMs1Frm9AZH7OD4Cjzw==
  dependencies:
    micromark-factory-space "^2.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-factory-whitespace@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/micromark-factory-whitespace/-/micromark-factory-whitespace-2.0.1.tgz"
  integrity sha512-Ob0nuZ3PKt/n0hORHyvoD9uZhr+Za8sFoP+OnMcnWK5lngSzALgQYKMr9RJVOWLqQYuyn6ulqGWSXdwf6F80lQ==
  dependencies:
    micromark-factory-space "^2.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-util-character@^2.0.0:
  version "2.1.1"
  resolved "https://registry.npmjs.org/micromark-util-character/-/micromark-util-character-2.1.1.tgz"
  integrity sha512-wv8tdUTJ3thSFFFJKtpYKOYiGP2+v96Hvk4Tu8KpCAsTMs6yi+nVmGh1syvSCsaxz45J6Jbw+9DD6g97+NV67Q==
  dependencies:
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-util-chunked@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/micromark-util-chunked/-/micromark-util-chunked-2.0.1.tgz"
  integrity sha512-QUNFEOPELfmvv+4xiNg2sRYeS/P84pTW0TCgP5zc9FpXetHY0ab7SxKyAQCNCc1eK0459uoLI1y5oO5Vc1dbhA==
  dependencies:
    micromark-util-symbol "^2.0.0"

micromark-util-classify-character@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/micromark-util-classify-character/-/micromark-util-classify-character-2.0.1.tgz"
  integrity sha512-K0kHzM6afW/MbeWYWLjoHQv1sgg2Q9EccHEDzSkxiP/EaagNzCm7T/WMKZ3rjMbvIpvBiZgwR3dKMygtA4mG1Q==
  dependencies:
    micromark-util-character "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-util-combine-extensions@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/micromark-util-combine-extensions/-/micromark-util-combine-extensions-2.0.1.tgz"
  integrity sha512-OnAnH8Ujmy59JcyZw8JSbK9cGpdVY44NKgSM7E9Eh7DiLS2E9RNQf0dONaGDzEG9yjEl5hcqeIsj4hfRkLH/Bg==
  dependencies:
    micromark-util-chunked "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-util-decode-numeric-character-reference@^2.0.0:
  version "2.0.2"
  resolved "https://registry.npmjs.org/micromark-util-decode-numeric-character-reference/-/micromark-util-decode-numeric-character-reference-2.0.2.tgz"
  integrity sha512-ccUbYk6CwVdkmCQMyr64dXz42EfHGkPQlBj5p7YVGzq8I7CtjXZJrubAYezf7Rp+bjPseiROqe7G6foFd+lEuw==
  dependencies:
    micromark-util-symbol "^2.0.0"

micromark-util-decode-string@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/micromark-util-decode-string/-/micromark-util-decode-string-2.0.1.tgz"
  integrity sha512-nDV/77Fj6eH1ynwscYTOsbK7rR//Uj0bZXBwJZRfaLEJ1iGBR6kIfNmlNqaqJf649EP0F3NWNdeJi03elllNUQ==
  dependencies:
    decode-named-character-reference "^1.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-decode-numeric-character-reference "^2.0.0"
    micromark-util-symbol "^2.0.0"

micromark-util-encode@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/micromark-util-encode/-/micromark-util-encode-2.0.1.tgz"
  integrity sha512-c3cVx2y4KqUnwopcO9b/SCdo2O67LwJJ/UyqGfbigahfegL9myoEFoDYZgkT7f36T0bLrM9hZTAaAyH+PCAXjw==

micromark-util-html-tag-name@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/micromark-util-html-tag-name/-/micromark-util-html-tag-name-2.0.1.tgz"
  integrity sha512-2cNEiYDhCWKI+Gs9T0Tiysk136SnR13hhO8yW6BGNyhOC4qYFnwF1nKfD3HFAIXA5c45RrIG1ub11GiXeYd1xA==

micromark-util-normalize-identifier@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/micromark-util-normalize-identifier/-/micromark-util-normalize-identifier-2.0.1.tgz"
  integrity sha512-sxPqmo70LyARJs0w2UclACPUUEqltCkJ6PhKdMIDuJ3gSf/Q+/GIe3WKl0Ijb/GyH9lOpUkRAO2wp0GVkLvS9Q==
  dependencies:
    micromark-util-symbol "^2.0.0"

micromark-util-resolve-all@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/micromark-util-resolve-all/-/micromark-util-resolve-all-2.0.1.tgz"
  integrity sha512-VdQyxFWFT2/FGJgwQnJYbe1jjQoNTS4RjglmSjTUlpUMa95Htx9NHeYW4rGDJzbjvCsl9eLjMQwGeElsqmzcHg==
  dependencies:
    micromark-util-types "^2.0.0"

micromark-util-sanitize-uri@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/micromark-util-sanitize-uri/-/micromark-util-sanitize-uri-2.0.1.tgz"
  integrity sha512-9N9IomZ/YuGGZZmQec1MbgxtlgougxTodVwDzzEouPKo3qFWvymFHWcnDi2vzV1ff6kas9ucW+o3yzJK9YB1AQ==
  dependencies:
    micromark-util-character "^2.0.0"
    micromark-util-encode "^2.0.0"
    micromark-util-symbol "^2.0.0"

micromark-util-subtokenize@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/micromark-util-subtokenize/-/micromark-util-subtokenize-2.1.0.tgz"
  integrity sha512-XQLu552iSctvnEcgXw6+Sx75GflAPNED1qx7eBJ+wydBb2KCbRZe+NwvIEEMM83uml1+2WSXpBAcp9IUCgCYWA==
  dependencies:
    devlop "^1.0.0"
    micromark-util-chunked "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-util-symbol@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/micromark-util-symbol/-/micromark-util-symbol-2.0.1.tgz"
  integrity sha512-vs5t8Apaud9N28kgCrRUdEed4UJ+wWNvicHLPxCa9ENlYuAY31M0ETy5y1vA33YoNPDFTghEbnh6efaE8h4x0Q==

micromark-util-types@^2.0.0:
  version "2.0.2"
  resolved "https://registry.npmjs.org/micromark-util-types/-/micromark-util-types-2.0.2.tgz"
  integrity sha512-Yw0ECSpJoViF1qTU4DC6NwtC4aWGt1EkzaQB8KPPyCRR8z9TWeV0HbEFGTO+ZY1wB22zmxnJqhPyTpOVCpeHTA==

micromark@^4.0.0:
  version "4.0.2"
  resolved "https://registry.npmjs.org/micromark/-/micromark-4.0.2.tgz"
  integrity sha512-zpe98Q6kvavpCr1NPVSCMebCKfD7CA2NqZ+rykeNhONIJBpc1tFKt9hucLGwha3jNTNI8lHpctWJWoimVF4PfA==
  dependencies:
    "@types/debug" "^4.0.0"
    debug "^4.0.0"
    decode-named-character-reference "^1.0.0"
    devlop "^1.0.0"
    micromark-core-commonmark "^2.0.0"
    micromark-factory-space "^2.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-chunked "^2.0.0"
    micromark-util-combine-extensions "^2.0.0"
    micromark-util-decode-numeric-character-reference "^2.0.0"
    micromark-util-encode "^2.0.0"
    micromark-util-normalize-identifier "^2.0.0"
    micromark-util-resolve-all "^2.0.0"
    micromark-util-sanitize-uri "^2.0.0"
    micromark-util-subtokenize "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromatch@^4.0.4, micromatch@^4.0.8:
  version "4.0.8"
  resolved "https://registry.npmjs.org/micromatch/-/micromatch-4.0.8.tgz"
  integrity sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==
  dependencies:
    braces "^3.0.3"
    picomatch "^2.3.1"

mime-db@^1.54.0:
  version "1.54.0"
  resolved "https://registry.npmjs.org/mime-db/-/mime-db-1.54.0.tgz"
  integrity sha512-aU5EJuIN2WDemCcAp2vFBfp/m4EAhWJnUNSSw0ixs7/kXbd6Pg64EmwJkNdFhB8aWt1sH2CTXrLxo/iAGV3oPQ==

mime-db@1.52.0:
  version "1.52.0"
  resolved "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz"
  integrity sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==

mime-types@^2.1.12:
  version "2.1.35"
  resolved "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz"
  integrity sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==
  dependencies:
    mime-db "1.52.0"

mime-types@^3.0.0, mime-types@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/mime-types/-/mime-types-3.0.1.tgz"
  integrity sha512-xRc4oEhT6eaBpU1XF7AjpOFD+xQmXNB5OVKwp4tqCuBpHLS/ZbBDrc07mYTDqVMg6PfxUjjNp85O6Cd2Z/5HWA==
  dependencies:
    mime-db "^1.54.0"

mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/mimic-fn/-/mimic-fn-2.1.0.tgz"
  integrity sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==

min-indent@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/min-indent/-/min-indent-1.0.1.tgz"
  integrity sha512-I9jwMn07Sy/IwOj3zVkVik2JTvgpaykDZEigL6Rx6N9LbMywwUSMtxET+7lVoDLLd3O3IXwJwvuuns8UB/HeAg==

minimatch@^3.0.4, minimatch@^3.1.1, minimatch@^3.1.2:
  version "3.1.2"
  resolved "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz"
  integrity sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==
  dependencies:
    brace-expansion "^1.1.7"

minimatch@^5.0.1:
  version "5.1.6"
  resolved "https://registry.npmjs.org/minimatch/-/minimatch-5.1.6.tgz"
  integrity sha512-lKwV/1brpG6mBUFHtb7NUmtABCb2WZZmm2wNiOA5hAb8VdCS4B3dtMWyvcoViccwAW/COERjXLt0zP1zXUN26g==
  dependencies:
    brace-expansion "^2.0.1"

minimatch@^8.0.2:
  version "8.0.4"
  resolved "https://registry.npmjs.org/minimatch/-/minimatch-8.0.4.tgz"
  integrity sha512-W0Wvr9HyFXZRGIDgCicunpQ299OKXs9RgZfaukz4qAW/pJhcpUfupc9c+OObPOFueNy8VSrZgEmDtk6Kh4WzDA==
  dependencies:
    brace-expansion "^2.0.1"

minimatch@^9.0.4:
  version "9.0.5"
  resolved "https://registry.npmjs.org/minimatch/-/minimatch-9.0.5.tgz"
  integrity sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==
  dependencies:
    brace-expansion "^2.0.1"

minimist@^1.2.0, minimist@^1.2.6:
  version "1.2.8"
  resolved "https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz"
  integrity sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==

minipass@^4.2.4:
  version "4.2.8"
  resolved "https://registry.npmjs.org/minipass/-/minipass-4.2.8.tgz"
  integrity sha512-fNzuVyifolSLFL4NzpF+wEF4qrgqaaKX0haXPQEdQ7NKAN+WecoKMHV09YcuL/DHxrUsYQOK3MiuDf7Ip2OXfQ==

"minipass@^5.0.0 || ^6.0.2 || ^7.0.0", minipass@^7.0.4, minipass@^7.1.2:
  version "7.1.2"
  resolved "https://registry.npmjs.org/minipass/-/minipass-7.1.2.tgz"
  integrity sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw==

minizlib@^3.0.1:
  version "3.0.2"
  resolved "https://registry.npmjs.org/minizlib/-/minizlib-3.0.2.tgz"
  integrity sha512-oG62iEk+CYt5Xj2YqI5Xi9xWUeZhDI8jjQmC5oThVH5JGCTgIjr7ciJDzC7MBzYd//WvR1OTmP5Q38Q8ShQtVA==
  dependencies:
    minipass "^7.1.2"

mkdirp@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/mkdirp/-/mkdirp-3.0.1.tgz"
  integrity sha512-+NsyUUAZDmo6YVHzL/stxSu3t9YS1iljliy3BSDrXJ/dkn1KYdmtZODGGjLcc9XLgVVpH4KshHB8XmZgMhaBXg==

module-details-from-path@^1.0.3:
  version "1.0.4"
  resolved "https://registry.npmjs.org/module-details-from-path/-/module-details-from-path-1.0.4.tgz"
  integrity sha512-EGWKgxALGMgzvxYF1UyGTy0HXX/2vHLkw6+NvDKW2jypWbHpjQuj4UMcqQWXHERJhVGKikolT06G3bcKe4fi7w==

monaco-editor@^0.52.2:
  version "0.52.2"
  resolved "https://registry.npmjs.org/monaco-editor/-/monaco-editor-0.52.2.tgz"
  integrity sha512-GEQWEZmfkOGLdd3XK8ryrfWz3AIP8YymVXiPHEdewrUq7mh0qrKrfHLNCXcbB6sTnMLnOZ3ztSiKcciFUkIJwQ==

motion-dom@^12.9.4:
  version "12.9.4"
  resolved "https://registry.npmjs.org/motion-dom/-/motion-dom-12.9.4.tgz"
  integrity sha512-25TWkQPj5I18m+qVjXGtCsxboY11DaRC5HMjd29tHKExazW4Zf4XtAagBdLpyKsVuAxEQ6cx5/E4AB21PFpLnQ==
  dependencies:
    motion-utils "^12.9.4"

motion-utils@^12.9.4:
  version "12.9.4"
  resolved "https://registry.npmjs.org/motion-utils/-/motion-utils-12.9.4.tgz"
  integrity sha512-BW3I65zeM76CMsfh3kHid9ansEJk9Qvl+K5cu4DVHKGsI52n76OJ4z2CUJUV+Mn3uEP9k1JJA3tClG0ggSrRcg==

ms@^2.0.0, ms@^2.1.1, ms@^2.1.3:
  version "2.1.3"
  resolved "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz"
  integrity sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==

mz@^2.7.0:
  version "2.7.0"
  resolved "https://registry.npmjs.org/mz/-/mz-2.7.0.tgz"
  integrity sha512-z81GNO7nnYMEhrGh9LeymoE4+Yr0Wn5McHIZMK5cfQCl+NDX08sCZgUc9/6MHni9IWuFLm1Z3HTCXu2z9fN62Q==
  dependencies:
    any-promise "^1.0.0"
    object-assign "^4.0.1"
    thenify-all "^1.0.0"

nano-css@^5.6.2:
  version "5.6.2"
  resolved "https://registry.npmjs.org/nano-css/-/nano-css-5.6.2.tgz"
  integrity sha512-+6bHaC8dSDGALM1HJjOHVXpuastdu2xFoZlC77Jh4cg+33Zcgm+Gxd+1xsnpZK14eyHObSp82+ll5y3SX75liw==
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.4.15"
    css-tree "^1.1.2"
    csstype "^3.1.2"
    fastest-stable-stringify "^2.0.2"
    inline-style-prefixer "^7.0.1"
    rtl-css-js "^1.16.1"
    stacktrace-js "^2.0.2"
    stylis "^4.3.0"

nanoid@^3.3.6, nanoid@^3.3.8:
  version "3.3.11"
  resolved "https://registry.npmjs.org/nanoid/-/nanoid-3.3.11.tgz"
  integrity sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==

napi-postinstall@^0.2.2:
  version "0.2.3"
  resolved "https://registry.npmjs.org/napi-postinstall/-/napi-postinstall-0.2.3.tgz"
  integrity sha512-Mi7JISo/4Ij2tDZ2xBE2WH+/KvVlkhA6juEjpEeRAVPNCpN3nxJo/5FhDNKgBcdmcmhaH6JjgST4xY/23ZYK0w==

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz"
  integrity sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==

negotiator@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/negotiator/-/negotiator-1.0.0.tgz"
  integrity sha512-8Ofs/AUQh8MaEcrlq5xOX0CQ9ypTF5dl78mjlMNfOK08fzpgTHQRQPBxcPlEtIw0yRpws+Zo/3r+5WRby7u3Gg==

new-date@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/new-date/-/new-date-1.0.3.tgz"
  integrity sha512-0fsVvQPbo2I18DT2zVHpezmeeNYV2JaJSrseiHLc17GNOxJzUdx5mvSigPu8LtIfZSij5i1wXnXFspEs2CD6hA==
  dependencies:
    "@segment/isodate" "1.0.3"

next-nprogress-bar@^2.4.7:
  version "2.4.7"
  resolved "https://registry.npmjs.org/next-nprogress-bar/-/next-nprogress-bar-2.4.7.tgz"
  integrity sha512-OeveNQYFBhQhZ+RgrDnvHNUEQfHCmipymmD4AfAVE9pFV4jeWi7/nNK5f0lIk7ODRrtjyyr/n2YpkRbs5kUoMg==
  dependencies:
    nprogress-v2 "^1.0.4"

next-themes@^0.4.6:
  version "0.4.6"
  resolved "https://registry.npmjs.org/next-themes/-/next-themes-0.4.6.tgz"
  integrity sha512-pZvgD5L0IEvX5/9GWyHMf3m8BKiVQwsCMHfoFosXtXBMnaS0ZnIJ9ST4b4NqLVKDEm8QBxoNNGNaBv2JNF6XNA==

next@15.1.7:
  version "15.1.7"
  resolved "https://registry.npmjs.org/next/-/next-15.1.7.tgz"
  integrity sha512-GNeINPGS9c6OZKCvKypbL8GTsT5GhWPp4DM0fzkXJuXMilOO2EeFxuAY6JZbtk6XIl6Ws10ag3xRINDjSO5+wg==
  dependencies:
    "@next/env" "15.1.7"
    "@swc/counter" "0.1.3"
    "@swc/helpers" "0.5.15"
    busboy "1.6.0"
    caniuse-lite "^1.0.30001579"
    postcss "8.4.31"
    styled-jsx "5.1.6"
  optionalDependencies:
    "@next/swc-darwin-arm64" "15.1.7"
    "@next/swc-darwin-x64" "15.1.7"
    "@next/swc-linux-arm64-gnu" "15.1.7"
    "@next/swc-linux-arm64-musl" "15.1.7"
    "@next/swc-linux-x64-gnu" "15.1.7"
    "@next/swc-linux-x64-musl" "15.1.7"
    "@next/swc-win32-arm64-msvc" "15.1.7"
    "@next/swc-win32-x64-msvc" "15.1.7"
    sharp "^0.33.5"

node-domexception@^1.0.0, node-domexception@1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/node-domexception/-/node-domexception-1.0.0.tgz"
  integrity sha512-/jKZoMpw0F8GRwl4/eLROPA3cfcXtLApP0QzLmUT/HuPCZWyB7IY9ZrMeKw2O/nFIqPQB3PVM9aYm0F312AXDQ==

node-emoji@^2.1.3:
  version "2.2.0"
  resolved "https://registry.npmjs.org/node-emoji/-/node-emoji-2.2.0.tgz"
  integrity sha512-Z3lTE9pLaJF47NyMhd4ww1yFTAP8YhYI8SleJiHzM46Fgpm5cnNzSl9XfzFNqbaz+VlJrIj3fXQ4DeN1Rjm6cw==
  dependencies:
    "@sindresorhus/is" "^4.6.0"
    char-regex "^1.0.2"
    emojilib "^2.4.0"
    skin-tone "^2.0.0"

node-fetch@^2.6.7, node-fetch@^2.6.9:
  version "2.7.0"
  resolved "https://registry.npmjs.org/node-fetch/-/node-fetch-2.7.0.tgz"
  integrity sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==
  dependencies:
    whatwg-url "^5.0.0"

node-fetch@^3.3.2:
  version "3.3.2"
  resolved "https://registry.npmjs.org/node-fetch/-/node-fetch-3.3.2.tgz"
  integrity sha512-dRB78srN/l6gqWulah9SrxeYnxeddIG30+GOqK/9OlLVyLg3HPnr6SqOWTWOXKRwC2eGYCkZ59NNuSgvSrpgOA==
  dependencies:
    data-uri-to-buffer "^4.0.0"
    fetch-blob "^3.1.4"
    formdata-polyfill "^4.0.10"

node-int64@^0.4.0:
  version "0.4.0"
  resolved "https://registry.npmjs.org/node-int64/-/node-int64-0.4.0.tgz"
  integrity sha512-O5lz91xSOeoXP6DulyHfllpq+Eg00MWitZIbtPfoSEvqIHdl5gfcY6hYzDWnj0qD5tz52PI08u9qUvSVeUBeHw==

node-releases@^2.0.19:
  version "2.0.19"
  resolved "https://registry.npmjs.org/node-releases/-/node-releases-2.0.19.tgz"
  integrity sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw==

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz"
  integrity sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==

npm-normalize-package-bin@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/npm-normalize-package-bin/-/npm-normalize-package-bin-4.0.0.tgz"
  integrity sha512-TZKxPvItzai9kN9H/TkmCtx/ZN/hvr3vUycjlfmH0ootY9yFBzNOpiXAdIn1Iteqsvk4lQn6B5PTrt+n6h8k/w==

npm-run-path@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmjs.org/npm-run-path/-/npm-run-path-4.0.1.tgz"
  integrity sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw==
  dependencies:
    path-key "^3.0.0"

nprogress-v2@^1.0.4:
  version "1.1.10"
  resolved "https://registry.npmjs.org/nprogress-v2/-/nprogress-v2-1.1.10.tgz"
  integrity sha512-MypWLNIPIM07SS0bAc/oac0vhVFz9vAHm7d1sj//Pnf3J03LQ3CuWrlDteIu6exq0fIvkDJ6tUDRWLaifsIt5w==

nwsapi@^2.2.2:
  version "2.2.20"
  resolved "https://registry.npmjs.org/nwsapi/-/nwsapi-2.2.20.tgz"
  integrity sha512-/ieB+mDe4MrrKMT8z+mQL8klXydZWGR5Dowt4RAGKbJ3kIGEx3X4ljUo+6V73IXtUPWgfOlU5B9MlGxFO5T+cA==

obj-case@0.2.1:
  version "0.2.1"
  resolved "https://registry.npmjs.org/obj-case/-/obj-case-0.2.1.tgz"
  integrity sha512-PquYBBTy+Y6Ob/O2574XHhDtHJlV1cJHMCgW+rDRc9J5hhmRelJB3k5dTK/3cVmFVtzvAKuENeuLpoyTzMzkOg==

object-assign@^4, object-assign@^4.0.1, object-assign@^4.1.1:
  version "4.1.1"
  resolved "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz"
  integrity sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==

object-hash@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/object-hash/-/object-hash-3.0.0.tgz"
  integrity sha512-RSn9F68PjH9HqtltsSnqYC1XXoWe9Bju5+213R98cNGttag9q9yAOTzdbsqvIa7aNm5WffBZFpWYr2aWrklWAw==

object-inspect@^1.13.3:
  version "1.13.4"
  resolved "https://registry.npmjs.org/object-inspect/-/object-inspect-1.13.4.tgz"
  integrity sha512-W67iLl4J2EXEGTbfeHCffrjDfitvLANg0UlX3wFUUSTx92KXRFegMHUVgSqE+wvhAbi4WqjGg9czysTV2Epbew==

object-is@^1.1.5:
  version "1.1.6"
  resolved "https://registry.npmjs.org/object-is/-/object-is-1.1.6.tgz"
  integrity sha512-F8cZ+KfGlSGi09lJT7/Nd6KJZ9ygtvYC0/UYYLI9nmQKLMnydpB9yvbv9K1uSkEu7FU9vYPmVwLg328tX+ot3Q==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"

object-keys@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/object-keys/-/object-keys-1.1.1.tgz"
  integrity sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==

object.assign@^4.1.4, object.assign@^4.1.7:
  version "4.1.7"
  resolved "https://registry.npmjs.org/object.assign/-/object.assign-4.1.7.tgz"
  integrity sha512-nK28WOo+QIjBkDduTINE4JkF/UJJKyf2EJxvJKfblDpyg0Q+pkOHNTL0Qwy6NP6FhE/EnzV73BxxqcJaXY9anw==
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"
    has-symbols "^1.1.0"
    object-keys "^1.1.1"

object.entries@^1.1.9:
  version "1.1.9"
  resolved "https://registry.npmjs.org/object.entries/-/object.entries-1.1.9.tgz"
  integrity sha512-8u/hfXFRBD1O0hPUjioLhoWFHRmt6tKA4/vZPyckBr18l1KE9uHrFaFaUi8MDRTpi4uak2goyPTSNJLXX2k2Hw==
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.4"
    define-properties "^1.2.1"
    es-object-atoms "^1.1.1"

object.fromentries@^2.0.8:
  version "2.0.8"
  resolved "https://registry.npmjs.org/object.fromentries/-/object.fromentries-2.0.8.tgz"
  integrity sha512-k6E21FzySsSK5a21KRADBd/NGneRegFO5pLHfdQLpRDETUNJueLXs3WCzyQ3tFRDYgbq3KHGXfTbi2bs8WQ6rQ==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-object-atoms "^1.0.0"

object.groupby@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/object.groupby/-/object.groupby-1.0.3.tgz"
  integrity sha512-+Lhy3TQTuzXI5hevh8sBGqbmurHbbIjAi0Z4S63nthVLmLxfbj4T54a4CfZrXIrt9iP4mVAPYMo/v99taj3wjQ==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"

object.values@^1.1.6, object.values@^1.2.0, object.values@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/object.values/-/object.values-1.2.1.tgz"
  integrity sha512-gXah6aZrcUxjWg2zR2MwouP2eHlCBzdV4pygudehaKXSGW4v2AsRQUK+lwwXhii6KFZcunEnmSUoYp5CXibxtA==
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

obuf@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/obuf/-/obuf-1.1.2.tgz"
  integrity sha512-PX1wu0AmAdPqOL1mWhqmlOd8kOIZQwGZw6rh7uby9fTc5lhaOWFLX3I6R1hrF9k3zUY40e6igsLGkDXK92LJNg==

on-exit-leak-free@^2.1.0:
  version "2.1.2"
  resolved "https://registry.npmjs.org/on-exit-leak-free/-/on-exit-leak-free-2.1.2.tgz"
  integrity sha512-0eJJY6hXLGf1udHwfNftBqH+g73EU4B504nZeKpz1sYRKafAghwxEJunB2O7rDZkL4PGfsMVnTXZ2EjibbqcsA==

on-finished@^2.4.1:
  version "2.4.1"
  resolved "https://registry.npmjs.org/on-finished/-/on-finished-2.4.1.tgz"
  integrity sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==
  dependencies:
    ee-first "1.1.1"

once@^1.3.0, once@^1.3.1, once@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npmjs.org/once/-/once-1.4.0.tgz"
  integrity sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==
  dependencies:
    wrappy "1"

onetime@^5.1.2:
  version "5.1.2"
  resolved "https://registry.npmjs.org/onetime/-/onetime-5.1.2.tgz"
  integrity sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==
  dependencies:
    mimic-fn "^2.1.0"

openai@^4.95.1:
  version "4.97.0"
  resolved "https://registry.npmjs.org/openai/-/openai-4.97.0.tgz"
  integrity sha512-LRoiy0zvEf819ZUEJhgfV8PfsE8G5WpQi4AwA1uCV8SKvvtXQkoWUFkepD6plqyJQRghy2+AEPQ07FrJFKHZ9Q==
  dependencies:
    "@types/node" "^18.11.18"
    "@types/node-fetch" "^2.6.4"
    abort-controller "^3.0.0"
    agentkeepalive "^4.2.1"
    form-data-encoder "1.7.2"
    formdata-node "^4.3.2"
    node-fetch "^2.6.7"

optionator@^0.9.3:
  version "0.9.4"
  resolved "https://registry.npmjs.org/optionator/-/optionator-0.9.4.tgz"
  integrity sha512-6IpQ7mKUxRcZNLIObR0hz7lxsapSSIYNZJwXPGeF0mTVqGKFIXj1DQcMoT22S3ROcLyY/rz0PWaWZ9ayWmad9g==
  dependencies:
    deep-is "^0.1.3"
    fast-levenshtein "^2.0.6"
    levn "^0.4.1"
    prelude-ls "^1.2.1"
    type-check "^0.4.0"
    word-wrap "^1.2.5"

own-keys@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/own-keys/-/own-keys-1.0.1.tgz"
  integrity sha512-qFOyK5PjiWZd+QQIh+1jhdb9LpxTF0qs7Pm8o5QHYZ0M3vKqSqzsZaEB6oWlxZ+q2sJBMI/Ktgd2N5ZwQoRHfg==
  dependencies:
    get-intrinsic "^1.2.6"
    object-keys "^1.1.1"
    safe-push-apply "^1.0.0"

p-limit@^2.2.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/p-limit/-/p-limit-2.3.0.tgz"
  integrity sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==
  dependencies:
    p-try "^2.0.0"

p-limit@^3.0.2, p-limit@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/p-limit/-/p-limit-3.1.0.tgz"
  integrity sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==
  dependencies:
    yocto-queue "^0.1.0"

p-locate@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/p-locate/-/p-locate-4.1.0.tgz"
  integrity sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==
  dependencies:
    p-limit "^2.2.0"

p-locate@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/p-locate/-/p-locate-5.0.0.tgz"
  integrity sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==
  dependencies:
    p-limit "^3.0.2"

p-try@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/p-try/-/p-try-2.2.0.tgz"
  integrity sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==

package-json-from-dist@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/package-json-from-dist/-/package-json-from-dist-1.0.1.tgz"
  integrity sha512-UEZIS3/by4OC8vL3P2dTXRETpebLI2NiI5vIrjaD/5UtrkFX/tNbwjTSRAGC/+7CAo2pIcBaRgWmcBBHcsaCIw==

parent-module@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/parent-module/-/parent-module-1.0.1.tgz"
  integrity sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==
  dependencies:
    callsites "^3.0.0"

parse-entities@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/parse-entities/-/parse-entities-2.0.0.tgz"
  integrity sha512-kkywGpCcRYhqQIchaWqZ875wzpS/bMKhz5HnN3p7wveJTkTtyAB/AlnS0f8DFSqYW1T82t6yEAkEcB+A1I3MbQ==
  dependencies:
    character-entities "^1.0.0"
    character-entities-legacy "^1.0.0"
    character-reference-invalid "^1.0.0"
    is-alphanumerical "^1.0.0"
    is-decimal "^1.0.0"
    is-hexadecimal "^1.0.0"

parse-entities@^4.0.0:
  version "4.0.2"
  resolved "https://registry.npmjs.org/parse-entities/-/parse-entities-4.0.2.tgz"
  integrity sha512-GG2AQYWoLgL877gQIKeRPGO1xF9+eG1ujIb5soS5gPvLQ1y2o8FL90w2QWNdf9I361Mpp7726c+lj3U0qK1uGw==
  dependencies:
    "@types/unist" "^2.0.0"
    character-entities-legacy "^3.0.0"
    character-reference-invalid "^2.0.0"
    decode-named-character-reference "^1.0.0"
    is-alphanumerical "^2.0.0"
    is-decimal "^2.0.0"
    is-hexadecimal "^2.0.0"

parse-json@^5.2.0:
  version "5.2.0"
  resolved "https://registry.npmjs.org/parse-json/-/parse-json-5.2.0.tgz"
  integrity sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-even-better-errors "^2.3.0"
    lines-and-columns "^1.1.6"

parse5@^7.0.0, parse5@^7.1.1:
  version "7.3.0"
  resolved "https://registry.npmjs.org/parse5/-/parse5-7.3.0.tgz"
  integrity sha512-IInvU7fabl34qmi9gY8XOVxhYyMyuH2xUNpb2q8/Y+7552KlejkRvqvD19nMoUW/uQGGbqNpA6Tufu5FL5BZgw==
  dependencies:
    entities "^6.0.0"

parseurl@^1.3.3:
  version "1.3.3"
  resolved "https://registry.npmjs.org/parseurl/-/parseurl-1.3.3.tgz"
  integrity sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==

path-exists@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz"
  integrity sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz"
  integrity sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==

path-key@^3.0.0, path-key@^3.1.0:
  version "3.1.1"
  resolved "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz"
  integrity sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==

path-parse@^1.0.7:
  version "1.0.7"
  resolved "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz"
  integrity sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==

path-scurry@^1.11.1, path-scurry@^1.6.1:
  version "1.11.1"
  resolved "https://registry.npmjs.org/path-scurry/-/path-scurry-1.11.1.tgz"
  integrity sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA==
  dependencies:
    lru-cache "^10.2.0"
    minipass "^5.0.0 || ^6.0.2 || ^7.0.0"

path-to-regexp@^8.0.0:
  version "8.2.0"
  resolved "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-8.2.0.tgz"
  integrity sha512-TdrF7fW9Rphjq4RjrW0Kp2AW0Ahwu9sRGTkS6bvDi0SCwZlEZYmcfDbEsTz8RVk0EHIS/Vd1bv3JhG+1xZuAyQ==

pg-int8@1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/pg-int8/-/pg-int8-1.0.1.tgz"
  integrity sha512-WCtabS6t3c8SkpDBUlb1kjOs7l66xsGdKpIPZsg4wR+B3+u9UAum2odSsF9tnvxg80h4ZxLWMy4pRjOsFIqQpw==

pg-numeric@1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/pg-numeric/-/pg-numeric-1.0.2.tgz"
  integrity sha512-BM/Thnrw5jm2kKLE5uJkXqqExRUY/toLHda65XgFTBTFYZyopbKjBe29Ii3RbkvlsMoFwD+tHeGaCjjv0gHlyw==

pg-protocol@*:
  version "1.9.5"
  resolved "https://registry.npmjs.org/pg-protocol/-/pg-protocol-1.9.5.tgz"
  integrity sha512-DYTWtWpfd5FOro3UnAfwvhD8jh59r2ig8bPtc9H8Ds7MscE/9NYruUQWFAOuraRl29jwcT2kyMFQ3MxeaVjUhg==

pg-types@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/pg-types/-/pg-types-2.2.0.tgz"
  integrity sha512-qTAAlrEsl8s4OiEQY69wDvcMIdQN6wdz5ojQiOy6YRMuynxenON0O5oCpJI6lshc6scgAY8qvJ2On/p+CXY0GA==
  dependencies:
    pg-int8 "1.0.1"
    postgres-array "~2.0.0"
    postgres-bytea "~1.0.0"
    postgres-date "~1.0.4"
    postgres-interval "^1.1.0"

pg-types@^4.0.1:
  version "4.0.2"
  resolved "https://registry.npmjs.org/pg-types/-/pg-types-4.0.2.tgz"
  integrity sha512-cRL3JpS3lKMGsKaWndugWQoLOCoP+Cic8oseVcbr0qhPzYD5DWXK+RZ9LY9wxRf7RQia4SCwQlXk0q6FCPrVng==
  dependencies:
    pg-int8 "1.0.1"
    pg-numeric "1.0.2"
    postgres-array "~3.0.1"
    postgres-bytea "~3.0.0"
    postgres-date "~2.1.0"
    postgres-interval "^3.0.0"
    postgres-range "^1.1.1"

picocolors@^1.0.0, picocolors@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz"
  integrity sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==

picomatch@^2.0.4, picomatch@^2.2.1, picomatch@^2.2.3, picomatch@^2.3.1:
  version "2.3.1"
  resolved "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz"
  integrity sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==

picomatch@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmjs.org/picomatch/-/picomatch-4.0.2.tgz"
  integrity sha512-M7BAV6Rlcy5u+m6oPhAPFgJTzAioX/6B0DxyvDlo9l8+T3nLKbrczg2WLUyzd45L8RqfUMyGPzekbMvX2Ldkwg==

pify@^2.3.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/pify/-/pify-2.3.0.tgz"
  integrity sha512-udgsAY+fTnvv7kI7aaxbqwWNb0AHiB0qBO89PZKPkoTmGOgdbrHDKD+0B2X4uTfJ/FT1R09r9gTsjUjNJotuog==

pino-abstract-transport@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/pino-abstract-transport/-/pino-abstract-transport-2.0.0.tgz"
  integrity sha512-F63x5tizV6WCh4R6RHyi2Ml+M70DNRXt/+HANowMflpgGFMAym/VKm6G7ZOQRjqN7XbGxK1Lg9t6ZrtzOaivMw==
  dependencies:
    split2 "^4.0.0"

pino-pretty@^13.0.0:
  version "13.0.0"
  resolved "https://registry.npmjs.org/pino-pretty/-/pino-pretty-13.0.0.tgz"
  integrity sha512-cQBBIVG3YajgoUjo1FdKVRX6t9XPxwB9lcNJVD5GCnNM4Y6T12YYx8c6zEejxQsU0wrg9TwmDulcE9LR7qcJqA==
  dependencies:
    colorette "^2.0.7"
    dateformat "^4.6.3"
    fast-copy "^3.0.2"
    fast-safe-stringify "^2.1.1"
    help-me "^5.0.0"
    joycon "^3.1.1"
    minimist "^1.2.6"
    on-exit-leak-free "^2.1.0"
    pino-abstract-transport "^2.0.0"
    pump "^3.0.0"
    secure-json-parse "^2.4.0"
    sonic-boom "^4.0.1"
    strip-json-comments "^3.1.1"

pino-std-serializers@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmjs.org/pino-std-serializers/-/pino-std-serializers-7.0.0.tgz"
  integrity sha512-e906FRY0+tV27iq4juKzSYPbUj2do2X2JX4EzSca1631EB2QJQUqGbDuERal7LCtOpxl6x3+nvo9NPZcmjkiFA==

pino@^9.6.0:
  version "9.6.0"
  resolved "https://registry.npmjs.org/pino/-/pino-9.6.0.tgz"
  integrity sha512-i85pKRCt4qMjZ1+L7sy2Ag4t1atFcdbEt76+7iRJn1g2BvsnRMGu9p8pivl9fs63M2kF/A0OacFZhTub+m/qMg==
  dependencies:
    atomic-sleep "^1.0.0"
    fast-redact "^3.1.1"
    on-exit-leak-free "^2.1.0"
    pino-abstract-transport "^2.0.0"
    pino-std-serializers "^7.0.0"
    process-warning "^4.0.0"
    quick-format-unescaped "^4.0.3"
    real-require "^0.2.0"
    safe-stable-stringify "^2.3.1"
    sonic-boom "^4.0.1"
    thread-stream "^3.0.0"

pirates@^4.0.1, pirates@^4.0.4:
  version "4.0.7"
  resolved "https://registry.npmjs.org/pirates/-/pirates-4.0.7.tgz"
  integrity sha512-TfySrs/5nm8fQJDcBDuUng3VOUKsd7S+zqvbOTiGXHfxX4wK31ard+hoNuvkicM/2YFzlpDgABOevKSsB4G/FA==

pkce-challenge@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/pkce-challenge/-/pkce-challenge-5.0.0.tgz"
  integrity sha512-ueGLflrrnvwB3xuo/uGob5pd5FN7l0MsLf0Z87o/UQmRtwjvfylfc9MurIxRAWywCYTgrvpXBcqjV4OfCYGCIQ==

pkg-dir@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/pkg-dir/-/pkg-dir-4.2.0.tgz"
  integrity sha512-HRDzbaKjC+AOWVXxAU/x54COGeIv9eb+6CkDSQoNTt4XyWoIJvuPsXizxu/Fr23EiekbtZwmh1IcIG/l/a10GQ==
  dependencies:
    find-up "^4.0.0"

possible-typed-array-names@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/possible-typed-array-names/-/possible-typed-array-names-1.1.0.tgz"
  integrity sha512-/+5VFTchJDoVj3bhoqi6UeymcD00DAwb1nJwamzPvHEszJ4FpF6SNNbUbOS8yI56qHzdV8eK0qEfOSiodkTdxg==

postcss-import@^15.1.0:
  version "15.1.0"
  resolved "https://registry.npmjs.org/postcss-import/-/postcss-import-15.1.0.tgz"
  integrity sha512-hpr+J05B2FVYUAXHeK1YyI267J/dDDhMU6B6civm8hSY1jYJnBXxzKDKDswzJmtLHryrjhnDjqqp/49t8FALew==
  dependencies:
    postcss-value-parser "^4.0.0"
    read-cache "^1.0.0"
    resolve "^1.1.7"

postcss-js@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmjs.org/postcss-js/-/postcss-js-4.0.1.tgz"
  integrity sha512-dDLF8pEO191hJMtlHFPRa8xsizHaM82MLfNkUHdUtVEV3tgTp5oj+8qbEqYM57SLfc74KSbw//4SeJma2LRVIw==
  dependencies:
    camelcase-css "^2.0.1"

postcss-load-config@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-4.0.2.tgz"
  integrity sha512-bSVhyJGL00wMVoPUzAVAnbEoWyqRxkjv64tUl427SKnPrENtq6hJwUojroMz2VB+Q1edmi4IfrAPpami5VVgMQ==
  dependencies:
    lilconfig "^3.0.0"
    yaml "^2.3.4"

postcss-nested@^6.2.0:
  version "6.2.0"
  resolved "https://registry.npmjs.org/postcss-nested/-/postcss-nested-6.2.0.tgz"
  integrity sha512-HQbt28KulC5AJzG+cZtj9kvKB93CFCdLvog1WFLf1D+xmMvPGlBstkpTEZfK5+AN9hfJocyBFCNiqyS48bpgzQ==
  dependencies:
    postcss-selector-parser "^6.1.1"

postcss-selector-parser@^6.1.1, postcss-selector-parser@^6.1.2:
  version "6.1.2"
  resolved "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.1.2.tgz"
  integrity sha512-Q8qQfPiZ+THO/3ZrOrO0cJJKfpYCagtMUkXbnEfmgUjwXg6z/WBeOyS9APBBPCTSiDV+s4SwQGu8yFsiMRIudg==
  dependencies:
    cssesc "^3.0.0"
    util-deprecate "^1.0.2"

postcss-value-parser@^4.0.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz"
  integrity sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==

postcss@^8, postcss@^8.4.47:
  version "8.5.3"
  resolved "https://registry.npmjs.org/postcss/-/postcss-8.5.3.tgz"
  integrity sha512-dle9A3yYxlBSrt8Fu+IpjGT8SY8hN0mlaA6GY8t0P5PjIOZemULz/E2Bnm/2dcUOena75OTNkHI76uZBNUUq3A==
  dependencies:
    nanoid "^3.3.8"
    picocolors "^1.1.1"
    source-map-js "^1.2.1"

postcss@8.4.31:
  version "8.4.31"
  resolved "https://registry.npmjs.org/postcss/-/postcss-8.4.31.tgz"
  integrity sha512-PS08Iboia9mts/2ygV3eLpY5ghnUcfLV/EXTOW1E2qYxJKGGBUtNjN76FYHnMs36RmARn41bC0AZmn+rR0OVpQ==
  dependencies:
    nanoid "^3.3.6"
    picocolors "^1.0.0"
    source-map-js "^1.0.2"

postgres-array@~2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/postgres-array/-/postgres-array-2.0.0.tgz"
  integrity sha512-VpZrUqU5A69eQyW2c5CA1jtLecCsN2U/bD6VilrFDWq5+5UIEVO7nazS3TEcHf1zuPYO/sqGvUvW62g86RXZuA==

postgres-array@~3.0.1:
  version "3.0.4"
  resolved "https://registry.npmjs.org/postgres-array/-/postgres-array-3.0.4.tgz"
  integrity sha512-nAUSGfSDGOaOAEGwqsRY27GPOea7CNipJPOA7lPbdEpx5Kg3qzdP0AaWC5MlhTWV9s4hFX39nomVZ+C4tnGOJQ==

postgres-bytea@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/postgres-bytea/-/postgres-bytea-1.0.0.tgz"
  integrity sha512-xy3pmLuQqRBZBXDULy7KbaitYqLcmxigw14Q5sj8QBVLqEwXfeybIKVWiqAXTlcvdvb0+xkOtDbfQMOf4lST1w==

postgres-bytea@~3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/postgres-bytea/-/postgres-bytea-3.0.0.tgz"
  integrity sha512-CNd4jim9RFPkObHSjVHlVrxoVQXz7quwNFpz7RY1okNNme49+sVyiTvTRobiLV548Hx/hb1BG+iE7h9493WzFw==
  dependencies:
    obuf "~1.1.2"

postgres-date@~1.0.4:
  version "1.0.7"
  resolved "https://registry.npmjs.org/postgres-date/-/postgres-date-1.0.7.tgz"
  integrity sha512-suDmjLVQg78nMK2UZ454hAG+OAW+HQPZ6n++TNDUX+L0+uUlLywnoxJKDou51Zm+zTCjrCl0Nq6J9C5hP9vK/Q==

postgres-date@~2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/postgres-date/-/postgres-date-2.1.0.tgz"
  integrity sha512-K7Juri8gtgXVcDfZttFKVmhglp7epKb1K4pgrkLxehjqkrgPhfG6OO8LHLkfaqkbpjNRnra018XwAr1yQFWGcA==

postgres-interval@^1.1.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/postgres-interval/-/postgres-interval-1.2.0.tgz"
  integrity sha512-9ZhXKM/rw350N1ovuWHbGxnGh/SNJ4cnxHiM0rxE4VN41wsg8P8zWn9hv/buK00RP4WvlOyr/RBDiptyxVbkZQ==
  dependencies:
    xtend "^4.0.0"

postgres-interval@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/postgres-interval/-/postgres-interval-3.0.0.tgz"
  integrity sha512-BSNDnbyZCXSxgA+1f5UU2GmwhoI0aU5yMxRGO8CdFEcY2BQF9xm/7MqKnYoM1nJDk8nONNWDk9WeSmePFhQdlw==

postgres-range@^1.1.1:
  version "1.1.4"
  resolved "https://registry.npmjs.org/postgres-range/-/postgres-range-1.1.4.tgz"
  integrity sha512-i/hbxIE9803Alj/6ytL7UHQxRvZkI9O4Sy+J3HGc4F4oo/2eQAjTSNJ0bfxyse3bH0nuVesCk+3IRLaMtG3H6w==

prelude-ls@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.2.1.tgz"
  integrity sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==

prettier@^3.5.3:
  version "3.5.3"
  resolved "https://registry.npmjs.org/prettier/-/prettier-3.5.3.tgz"
  integrity sha512-QQtaxnoDJeAkDvDKWCLiwIXkTgRhwYDEQCghU9Z6q03iyek/rxRh/2lC3HB7P8sWT2xC/y5JDctPLBIGzHKbhw==

pretty-format@^27.0.2:
  version "27.5.1"
  resolved "https://registry.npmjs.org/pretty-format/-/pretty-format-27.5.1.tgz"
  integrity sha512-Qb1gy5OrP5+zDf2Bvnzdl3jsTf1qXVMazbvCoKhtKqVs4/YK4ozX4gKQJJVyNe+cajNPn0KoC0MC3FUmaHWEmQ==
  dependencies:
    ansi-regex "^5.0.1"
    ansi-styles "^5.0.0"
    react-is "^17.0.1"

pretty-format@^29.0.0, pretty-format@^29.7.0:
  version "29.7.0"
  resolved "https://registry.npmjs.org/pretty-format/-/pretty-format-29.7.0.tgz"
  integrity sha512-Pdlw/oPxN+aXdmM9R00JVC9WVFoCLTKJvDVLgmJ+qAffBMxsV85l/Lu7sNx4zSzPyoL2euImuEwHhOXdEgNFZQ==
  dependencies:
    "@jest/schemas" "^29.6.3"
    ansi-styles "^5.0.0"
    react-is "^18.0.0"

prismjs@^1.27.0:
  version "1.30.0"
  resolved "https://registry.npmjs.org/prismjs/-/prismjs-1.30.0.tgz"
  integrity sha512-DEvV2ZF2r2/63V+tK8hQvrR2ZGn10srHbXviTlcv7Kpzw8jWiNTqbVgjO3IY8RxrrOUF8VPMQQFysYYYv0YZxw==

prismjs@~1.27.0:
  version "1.27.0"
  resolved "https://registry.npmjs.org/prismjs/-/prismjs-1.27.0.tgz"
  integrity sha512-t13BGPUlFDR7wRB5kQDG4jjl7XeuH6jbJGt11JHPL96qwsEHNX2+68tFXqc1/k+/jALsbSWJKUOT/hcYAZ5LkA==

proc-log@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/proc-log/-/proc-log-5.0.0.tgz"
  integrity sha512-Azwzvl90HaF0aCz1JrDdXQykFakSSNPaPoiZ9fm5qJIMHioDZEi7OAdRwSm6rSoPtY3Qutnm3L7ogmg3dc+wbQ==

process-warning@^4.0.0:
  version "4.0.1"
  resolved "https://registry.npmjs.org/process-warning/-/process-warning-4.0.1.tgz"
  integrity sha512-3c2LzQ3rY9d0hc1emcsHhfT9Jwz0cChib/QN89oME2R451w5fy3f0afAhERFZAwrbDU43wk12d0ORBpDVME50Q==

progress@^2.0.3:
  version "2.0.3"
  resolved "https://registry.npmjs.org/progress/-/progress-2.0.3.tgz"
  integrity sha512-7PiHtLll5LdnKIMw100I+8xJXR5gW2QwWYkT6iJva0bXitZKa/XMrSbdmg3r2Xnaidz9Qumd0VPaMrZlF9V9sA==

prompts@^2.0.1:
  version "2.4.2"
  resolved "https://registry.npmjs.org/prompts/-/prompts-2.4.2.tgz"
  integrity sha512-NxNv/kLguCA7p3jE8oL2aEBsrJWgAakBpgmgK6lpPWV+WuOmY6r2/zbAVnP+T8bQlA0nzHXSJSJW0Hq7ylaD2Q==
  dependencies:
    kleur "^3.0.3"
    sisteransi "^1.0.5"

prop-types@^15.5.4, prop-types@^15.7.2, prop-types@^15.8.1:
  version "15.8.1"
  resolved "https://registry.npmjs.org/prop-types/-/prop-types-15.8.1.tgz"
  integrity sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==
  dependencies:
    loose-envify "^1.4.0"
    object-assign "^4.1.1"
    react-is "^16.13.1"

property-information@^5.0.0:
  version "5.6.0"
  resolved "https://registry.npmjs.org/property-information/-/property-information-5.6.0.tgz"
  integrity sha512-YUHSPk+A30YPv+0Qf8i9Mbfe/C0hdPXk1s1jPVToV8pk8BQtpw10ct89Eo7OWkutrwqvT0eicAxlOg3dOAu8JA==
  dependencies:
    xtend "^4.0.0"

property-information@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmjs.org/property-information/-/property-information-7.0.0.tgz"
  integrity sha512-7D/qOz/+Y4X/rzSB6jKxKUsQnphO046ei8qxG59mtM3RG3DHgTK81HrxrmoDVINJb8NKT5ZsRbwHvQ6B68Iyhg==

protobufjs@^7.2.5, protobufjs@^7.3.0:
  version "7.5.3"
  resolved "https://registry.npmjs.org/protobufjs/-/protobufjs-7.5.3.tgz"
  integrity sha512-sildjKwVqOI2kmFDiXQ6aEB0fjYTafpEvIBs8tOR8qI4spuL9OPROLVu2qZqi/xgCfsHIwVqlaF8JBjWFHnKbw==
  dependencies:
    "@protobufjs/aspromise" "^1.1.2"
    "@protobufjs/base64" "^1.1.2"
    "@protobufjs/codegen" "^2.0.4"
    "@protobufjs/eventemitter" "^1.1.0"
    "@protobufjs/fetch" "^1.1.0"
    "@protobufjs/float" "^1.0.2"
    "@protobufjs/inquire" "^1.1.0"
    "@protobufjs/path" "^1.1.2"
    "@protobufjs/pool" "^1.1.0"
    "@protobufjs/utf8" "^1.1.0"
    "@types/node" ">=13.7.0"
    long "^5.0.0"

proxy-addr@^2.0.7:
  version "2.0.7"
  resolved "https://registry.npmjs.org/proxy-addr/-/proxy-addr-2.0.7.tgz"
  integrity sha512-llQsMLSUDUPT44jdrU/O37qlnifitDP+ZwrmmZcoSKyLKvtZxpyV0n2/bD/N4tBAAZ/gJEdZU7KMraoK1+XYAg==
  dependencies:
    forwarded "0.2.0"
    ipaddr.js "1.9.1"

proxy-from-env@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/proxy-from-env/-/proxy-from-env-1.1.0.tgz"
  integrity sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==

psl@^1.1.33:
  version "1.15.0"
  resolved "https://registry.npmjs.org/psl/-/psl-1.15.0.tgz"
  integrity sha512-JZd3gMVBAVQkSs6HdNZo9Sdo0LNcQeMNP3CozBJb3JYC/QUYZTnKxP+f8oWRX4rHP5EurWxqAHTSwUCjlNKa1w==
  dependencies:
    punycode "^2.3.1"

pump@^3.0.0:
  version "3.0.2"
  resolved "https://registry.npmjs.org/pump/-/pump-3.0.2.tgz"
  integrity sha512-tUPXtzlGM8FE3P0ZL6DVs/3P58k9nk8/jZeQCurTJylQA8qFYzHFfhBJkuqyE0FifOsQ0uKWekiZ5g8wtr28cw==
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

punycode@^2.1.0, punycode@^2.1.1, punycode@^2.3.1:
  version "2.3.1"
  resolved "https://registry.npmjs.org/punycode/-/punycode-2.3.1.tgz"
  integrity sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==

pure-rand@^6.0.0:
  version "6.1.0"
  resolved "https://registry.npmjs.org/pure-rand/-/pure-rand-6.1.0.tgz"
  integrity sha512-bVWawvoZoBYpp6yIoQtQXHZjmz35RSVHnUOTefl8Vcjr8snTPY1wnpSPMWekcFwbxI6gtmT7rSYPFvz71ldiOA==

qs@^6.11.0, qs@^6.14.0:
  version "6.14.0"
  resolved "https://registry.npmjs.org/qs/-/qs-6.14.0.tgz"
  integrity sha512-YWWTjgABSKcvs/nWBi9PycY/JiPJqOD4JA6o9Sej2AtvSGarXxKC3OQSk4pAarbdQlKAh5D4FCQkJNkW+GAn3w==
  dependencies:
    side-channel "^1.1.0"

querystringify@^2.1.1:
  version "2.2.0"
  resolved "https://registry.npmjs.org/querystringify/-/querystringify-2.2.0.tgz"
  integrity sha512-FIqgj2EUvTa7R50u0rGsyTftzjYmv/a3hO345bZNrqabNqjtgiDMgmo4mkUjd+nzU5oF3dClKqFIPUKybUyqoQ==

queue-microtask@^1.2.2:
  version "1.2.3"
  resolved "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.3.tgz"
  integrity sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==

quick-format-unescaped@^4.0.3:
  version "4.0.4"
  resolved "https://registry.npmjs.org/quick-format-unescaped/-/quick-format-unescaped-4.0.4.tgz"
  integrity sha512-tYC1Q1hgyRuHgloV/YXs2w15unPVh8qfu/qCTfhTYamaw7fyhumKa2yGpdSo87vY32rIclj+4fWYQXUMs9EHvg==

range-parser@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/range-parser/-/range-parser-1.2.1.tgz"
  integrity sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==

raw-body@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/raw-body/-/raw-body-3.0.0.tgz"
  integrity sha512-RmkhL8CAyCRPXCE28MMH0z2PNWQBNk2Q09ZdxM9IOOXwxwZbN+qbWaatPkdkWIKL2ZVDImrN/pK5HTRz2PcS4g==
  dependencies:
    bytes "3.1.2"
    http-errors "2.0.0"
    iconv-lite "0.6.3"
    unpipe "1.0.0"

react-device-detect@^2.2.3:
  version "2.2.3"
  resolved "https://registry.npmjs.org/react-device-detect/-/react-device-detect-2.2.3.tgz"
  integrity sha512-buYY3qrCnQVlIFHrC5UcUoAj7iANs/+srdkwsnNjI7anr3Tt7UY6MqNxtMLlr0tMBied0O49UZVK8XKs3ZIiPw==
  dependencies:
    ua-parser-js "^1.0.33"

react-dom@^19.0.0:
  version "19.1.0"
  resolved "https://registry.npmjs.org/react-dom/-/react-dom-19.1.0.tgz"
  integrity sha512-Xs1hdnE+DyKgeHJeJznQmYMIBG3TKIHJJT95Q58nHLSrElKlGQqDTR2HQ9fx5CN/Gk6Vh/kupBTDLU11/nDk/g==
  dependencies:
    scheduler "^0.26.0"

react-hook-form@^7.56.3:
  version "7.56.3"
  resolved "https://registry.npmjs.org/react-hook-form/-/react-hook-form-7.56.3.tgz"
  integrity sha512-IK18V6GVbab4TAo1/cz3kqajxbDPGofdF0w7VHdCo0Nt8PrPlOZcuuDq9YYIV1BtjcX78x0XsldbQRQnQXWXmw==

react-is@^16.13.1, react-is@^16.7.0:
  version "16.13.1"
  resolved "https://registry.npmjs.org/react-is/-/react-is-16.13.1.tgz"
  integrity sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==

react-is@^17.0.1:
  version "17.0.2"
  resolved "https://registry.npmjs.org/react-is/-/react-is-17.0.2.tgz"
  integrity sha512-w2GsyukL62IJnlaff/nRegPQR94C/XXamvMWmSHRJ4y7Ts/4ocGRmTHvOs8PSE6pB3dWOrD/nueuU5sduBsQ4w==

react-is@^18.0.0:
  version "18.3.1"
  resolved "https://registry.npmjs.org/react-is/-/react-is-18.3.1.tgz"
  integrity sha512-/LLMVyas0ljjAtoYiPqYiL8VWXzUUdThrmU5+n20DZv+a+ClRoevUzw5JxU+Ieh5/c87ytoTBV9G1FiKfNJdmg==

react-lifecycles-compat@^3.0.4:
  version "3.0.4"
  resolved "https://registry.npmjs.org/react-lifecycles-compat/-/react-lifecycles-compat-3.0.4.tgz"
  integrity sha512-fBASbA6LnOU9dOU2eW7aQ8xmYBSXUIWr+UmF9b1efZBazGNO+rcXT/icdKnYm2pTwcRylVUYwW7H1PHfLekVzA==

react-markdown@^10.0.0:
  version "10.1.0"
  resolved "https://registry.npmjs.org/react-markdown/-/react-markdown-10.1.0.tgz"
  integrity sha512-qKxVopLT/TyA6BX3Ue5NwabOsAzm0Q7kAPwq6L+wWDwisYs7R8vZ0nRXqq6rkueboxpkjvLGU9fWifiX/ZZFxQ==
  dependencies:
    "@types/hast" "^3.0.0"
    "@types/mdast" "^4.0.0"
    devlop "^1.0.0"
    hast-util-to-jsx-runtime "^2.0.0"
    html-url-attributes "^3.0.0"
    mdast-util-to-hast "^13.0.0"
    remark-parse "^11.0.0"
    remark-rehype "^11.0.0"
    unified "^11.0.0"
    unist-util-visit "^5.0.0"
    vfile "^6.0.0"

react-remove-scroll-bar@^2.3.7:
  version "2.3.8"
  resolved "https://registry.npmjs.org/react-remove-scroll-bar/-/react-remove-scroll-bar-2.3.8.tgz"
  integrity sha512-9r+yi9+mgU33AKcj6IbT9oRCO78WriSj6t/cF8DWBZJ9aOGPOTEDvdUDz1FwKim7QXWwmHqtdHnRJfhAxEG46Q==
  dependencies:
    react-style-singleton "^2.2.2"
    tslib "^2.0.0"

react-remove-scroll@^2.6.3:
  version "2.6.3"
  resolved "https://registry.npmjs.org/react-remove-scroll/-/react-remove-scroll-2.6.3.tgz"
  integrity sha512-pnAi91oOk8g8ABQKGF5/M9qxmmOPxaAnopyTHYfqYEwJhyFrbbBtHuSgtKEoH0jpcxx5o3hXqH1mNd9/Oi+8iQ==
  dependencies:
    react-remove-scroll-bar "^2.3.7"
    react-style-singleton "^2.2.3"
    tslib "^2.1.0"
    use-callback-ref "^1.3.3"
    use-sidecar "^1.1.3"

react-split-pane@^0.1.92:
  version "0.1.92"
  resolved "https://registry.npmjs.org/react-split-pane/-/react-split-pane-0.1.92.tgz"
  integrity sha512-GfXP1xSzLMcLJI5BM36Vh7GgZBpy+U/X0no+VM3fxayv+p1Jly5HpMofZJraeaMl73b3hvlr+N9zJKvLB/uz9w==
  dependencies:
    prop-types "^15.7.2"
    react-lifecycles-compat "^3.0.4"
    react-style-proptype "^3.2.2"

react-style-proptype@^3.2.2:
  version "3.2.2"
  resolved "https://registry.npmjs.org/react-style-proptype/-/react-style-proptype-3.2.2.tgz"
  integrity sha512-ywYLSjNkxKHiZOqNlso9PZByNEY+FTyh3C+7uuziK0xFXu9xzdyfHwg4S9iyiRRoPCR4k2LqaBBsWVmSBwCWYQ==
  dependencies:
    prop-types "^15.5.4"

react-style-singleton@^2.2.2, react-style-singleton@^2.2.3:
  version "2.2.3"
  resolved "https://registry.npmjs.org/react-style-singleton/-/react-style-singleton-2.2.3.tgz"
  integrity sha512-b6jSvxvVnyptAiLjbkWLE/lOnR4lfTtDAl+eUC7RZy+QQWc6wRzIV2CE6xBuMmDxc2qIihtDCZD5NPOFl7fRBQ==
  dependencies:
    get-nonce "^1.0.0"
    tslib "^2.0.0"

react-syntax-highlighter@^15.6.1:
  version "15.6.1"
  resolved "https://registry.npmjs.org/react-syntax-highlighter/-/react-syntax-highlighter-15.6.1.tgz"
  integrity sha512-OqJ2/vL7lEeV5zTJyG7kmARppUjiB9h9udl4qHQjjgEos66z00Ia0OckwYfRxCSFrW8RJIBnsBwQsHZbVPspqg==
  dependencies:
    "@babel/runtime" "^7.3.1"
    highlight.js "^10.4.1"
    highlightjs-vue "^1.0.0"
    lowlight "^1.17.0"
    prismjs "^1.27.0"
    refractor "^3.6.0"

react-universal-interface@^0.6.2:
  version "0.6.2"
  resolved "https://registry.npmjs.org/react-universal-interface/-/react-universal-interface-0.6.2.tgz"
  integrity sha512-dg8yXdcQmvgR13RIlZbTRQOoUrDciFVoSBZILwjE2LFISxZZ8loVJKAkuzswl5js8BHda79bIb2b84ehU8IjXw==

react-use@^17.6.0:
  version "17.6.0"
  resolved "https://registry.npmjs.org/react-use/-/react-use-17.6.0.tgz"
  integrity sha512-OmedEScUMKFfzn1Ir8dBxiLLSOzhKe/dPZwVxcujweSj45aNM7BEGPb9BEVIgVEqEXx6f3/TsXzwIktNgUR02g==
  dependencies:
    "@types/js-cookie" "^2.2.6"
    "@xobotyi/scrollbar-width" "^1.9.5"
    copy-to-clipboard "^3.3.1"
    fast-deep-equal "^3.1.3"
    fast-shallow-equal "^1.0.0"
    js-cookie "^2.2.1"
    nano-css "^5.6.2"
    react-universal-interface "^0.6.2"
    resize-observer-polyfill "^1.5.1"
    screenfull "^5.1.0"
    set-harmonic-interval "^1.0.1"
    throttle-debounce "^3.0.1"
    ts-easing "^0.2.0"
    tslib "^2.1.0"

react@^19.0.0:
  version "19.1.0"
  resolved "https://registry.npmjs.org/react/-/react-19.1.0.tgz"
  integrity sha512-FS+XFBNvn3GTAWq26joslQgWNoFu08F4kl0J4CgdNKADkdSGXQyTCnKteIAJy96Br6YbpEU1LSzV5dYtjMkMDg==

read-cache@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/read-cache/-/read-cache-1.0.0.tgz"
  integrity sha512-Owdv/Ft7IjOgm/i0xvNDZ1LrRANRfew4b2prF3OWMQLxLfu3bS8FVhCsrSCMK4lR56Y9ya+AThoTpDCTxCmpRA==
  dependencies:
    pify "^2.3.0"

read-cmd-shim@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/read-cmd-shim/-/read-cmd-shim-5.0.0.tgz"
  integrity sha512-SEbJV7tohp3DAAILbEMPXavBjAnMN0tVnh4+9G8ihV4Pq3HYF9h8QNez9zkJ1ILkv9G2BjdzwctznGZXgu/HGw==

readdirp@~3.6.0:
  version "3.6.0"
  resolved "https://registry.npmjs.org/readdirp/-/readdirp-3.6.0.tgz"
  integrity sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==
  dependencies:
    picomatch "^2.2.1"

real-require@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/real-require/-/real-require-0.2.0.tgz"
  integrity sha512-57frrGM/OCTLqLOAh0mhVA9VBMHd+9U7Zb2THMGdBUoZVOtGbJzjxsYGDJ3A9AYYCP4hn6y1TVbaOfzWtm5GFg==

redent@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/redent/-/redent-3.0.0.tgz"
  integrity sha512-6tDA8g98We0zd0GvVeMT9arEOnTw9qM03L9cJXaCjrip1OO764RDBLBfrB4cwzNGDj5OA5ioymC9GkizgWJDUg==
  dependencies:
    indent-string "^4.0.0"
    strip-indent "^3.0.0"

reflect.getprototypeof@^1.0.6, reflect.getprototypeof@^1.0.9:
  version "1.0.10"
  resolved "https://registry.npmjs.org/reflect.getprototypeof/-/reflect.getprototypeof-1.0.10.tgz"
  integrity sha512-00o4I+DVrefhv+nX0ulyi3biSHCPDe+yLv5o/p6d/UVlirijB8E16FtfwSAi4g3tcqrQ4lRAqQSoFEZJehYEcw==
  dependencies:
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-abstract "^1.23.9"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    get-intrinsic "^1.2.7"
    get-proto "^1.0.1"
    which-builtin-type "^1.2.1"

refractor@^3.6.0:
  version "3.6.0"
  resolved "https://registry.npmjs.org/refractor/-/refractor-3.6.0.tgz"
  integrity sha512-MY9W41IOWxxk31o+YvFCNyNzdkc9M20NoZK5vq6jkv4I/uh2zkWcfudj0Q1fovjUQJrNewS9NMzeTtqPf+n5EA==
  dependencies:
    hastscript "^6.0.0"
    parse-entities "^2.0.0"
    prismjs "~1.27.0"

regenerate-unicode-properties@^10.2.0:
  version "10.2.0"
  resolved "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-10.2.0.tgz"
  integrity sha512-DqHn3DwbmmPVzeKj9woBadqmXxLvQoQIwu7nopMc72ztvxVmVk2SBhSnx67zuye5TP+lJsb/TBQsjLKhnDf3MA==
  dependencies:
    regenerate "^1.4.2"

regenerate@^1.4.2:
  version "1.4.2"
  resolved "https://registry.npmjs.org/regenerate/-/regenerate-1.4.2.tgz"
  integrity sha512-zrceR/XhGYU/d/opr2EKO7aRHUeiBI8qjtfHqADTwZd6Szfy16la6kqD0MIUs5z5hx6AaKa+PixpPrR289+I0A==

regexp.prototype.flags@^1.5.1, regexp.prototype.flags@^1.5.3:
  version "1.5.4"
  resolved "https://registry.npmjs.org/regexp.prototype.flags/-/regexp.prototype.flags-1.5.4.tgz"
  integrity sha512-dYqgNSZbDwkaJ2ceRd9ojCGjBq+mOm9LmtXnAnEGyHhN/5R7iDW2TRw3h+o/jCFxus3P2LfWIIiwowAjANm7IA==
  dependencies:
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-errors "^1.3.0"
    get-proto "^1.0.1"
    gopd "^1.2.0"
    set-function-name "^2.0.2"

regexpu-core@^6.2.0:
  version "6.2.0"
  resolved "https://registry.npmjs.org/regexpu-core/-/regexpu-core-6.2.0.tgz"
  integrity sha512-H66BPQMrv+V16t8xtmq+UC0CBpiTBA60V8ibS1QVReIp8T1z8hwFxqcGzm9K6lgsN7sB5edVH8a+ze6Fqm4weA==
  dependencies:
    regenerate "^1.4.2"
    regenerate-unicode-properties "^10.2.0"
    regjsgen "^0.8.0"
    regjsparser "^0.12.0"
    unicode-match-property-ecmascript "^2.0.0"
    unicode-match-property-value-ecmascript "^2.1.0"

regjsgen@^0.8.0:
  version "0.8.0"
  resolved "https://registry.npmjs.org/regjsgen/-/regjsgen-0.8.0.tgz"
  integrity sha512-RvwtGe3d7LvWiDQXeQw8p5asZUmfU1G/l6WbUXeHta7Y2PEIvBTwH6E2EfmYUK8pxcxEdEmaomqyp0vZZ7C+3Q==

regjsparser@^0.12.0:
  version "0.12.0"
  resolved "https://registry.npmjs.org/regjsparser/-/regjsparser-0.12.0.tgz"
  integrity sha512-cnE+y8bz4NhMjISKbgeVJtqNbtf5QpjZP+Bslo+UqkIt9QPnX9q095eiRRASJG1/tz6dlNr6Z5NsBiWYokp6EQ==
  dependencies:
    jsesc "~3.0.2"

remark-gfm@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmjs.org/remark-gfm/-/remark-gfm-4.0.1.tgz"
  integrity sha512-1quofZ2RQ9EWdeN34S79+KExV1764+wCUGop5CPL1WGdD0ocPpu91lzPGbwWMECpEpd42kJGQwzRfyov9j4yNg==
  dependencies:
    "@types/mdast" "^4.0.0"
    mdast-util-gfm "^3.0.0"
    micromark-extension-gfm "^3.0.0"
    remark-parse "^11.0.0"
    remark-stringify "^11.0.0"
    unified "^11.0.0"

remark-parse@^11.0.0:
  version "11.0.0"
  resolved "https://registry.npmjs.org/remark-parse/-/remark-parse-11.0.0.tgz"
  integrity sha512-FCxlKLNGknS5ba/1lmpYijMUzX2esxW5xQqjWxw2eHFfS2MSdaHVINFmhjo+qN1WhZhNimq0dZATN9pH0IDrpA==
  dependencies:
    "@types/mdast" "^4.0.0"
    mdast-util-from-markdown "^2.0.0"
    micromark-util-types "^2.0.0"
    unified "^11.0.0"

remark-rehype@^11.0.0:
  version "11.1.2"
  resolved "https://registry.npmjs.org/remark-rehype/-/remark-rehype-11.1.2.tgz"
  integrity sha512-Dh7l57ianaEoIpzbp0PC9UKAdCSVklD8E5Rpw7ETfbTl3FqcOOgq5q2LVDhgGCkaBv7p24JXikPdvhhmHvKMsw==
  dependencies:
    "@types/hast" "^3.0.0"
    "@types/mdast" "^4.0.0"
    mdast-util-to-hast "^13.0.0"
    unified "^11.0.0"
    vfile "^6.0.0"

remark-stringify@^11.0.0:
  version "11.0.0"
  resolved "https://registry.npmjs.org/remark-stringify/-/remark-stringify-11.0.0.tgz"
  integrity sha512-1OSmLd3awB/t8qdoEOMazZkNsfVTeY4fTsgzcQFdXNq8ToTN4ZGwrMnlda4K6smTFKD+GRV6O48i6Z4iKgPPpw==
  dependencies:
    "@types/mdast" "^4.0.0"
    mdast-util-to-markdown "^2.0.0"
    unified "^11.0.0"

require-directory@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz"
  integrity sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==

require-in-the-middle@^7.1.1:
  version "7.5.2"
  resolved "https://registry.npmjs.org/require-in-the-middle/-/require-in-the-middle-7.5.2.tgz"
  integrity sha512-gAZ+kLqBdHarXB64XpAe2VCjB7rIRv+mU8tfRWziHRJ5umKsIHN2tLLv6EtMw7WCdP19S0ERVMldNvxYCHnhSQ==
  dependencies:
    debug "^4.3.5"
    module-details-from-path "^1.0.3"
    resolve "^1.22.8"

requires-port@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/requires-port/-/requires-port-1.0.0.tgz"
  integrity sha512-KigOCHcocU3XODJxsu8i/j8T9tzT4adHiecwORRQ0ZZFcp7ahwXuRU1m+yuO90C5ZUyGeGfocHDI14M3L3yDAQ==

resize-observer-polyfill@^1.5.1:
  version "1.5.1"
  resolved "https://registry.npmjs.org/resize-observer-polyfill/-/resize-observer-polyfill-1.5.1.tgz"
  integrity sha512-LwZrotdHOo12nQuZlHEmtuXdqGoOD0OhaxopaNFxWzInpEgaLWoVuAMbTzixuosCx2nEG58ngzW3vxdWoxIgdg==

resolve-cwd@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/resolve-cwd/-/resolve-cwd-3.0.0.tgz"
  integrity sha512-OrZaX2Mb+rJCpH/6CpSqt9xFVpN++x01XnN2ie9g6P5/3xelLAkXWVADpdz1IHD/KFfEXyE6V0U01OQ3UO2rEg==
  dependencies:
    resolve-from "^5.0.0"

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/resolve-from/-/resolve-from-4.0.0.tgz"
  integrity sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==

resolve-from@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/resolve-from/-/resolve-from-5.0.0.tgz"
  integrity sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw==

resolve-pkg-maps@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/resolve-pkg-maps/-/resolve-pkg-maps-1.0.0.tgz"
  integrity sha512-seS2Tj26TBVOC2NIc2rOe2y2ZO7efxITtLZcGSOnHHNOQ7CkiUBfw0Iw2ck6xkIhPwLhKNLS8BO+hEpngQlqzw==

resolve.exports@^2.0.0:
  version "2.0.3"
  resolved "https://registry.npmjs.org/resolve.exports/-/resolve.exports-2.0.3.tgz"
  integrity sha512-OcXjMsGdhL4XnbShKpAcSqPMzQoYkYyhbEaeSko47MjRP9NfEQMhZkXL1DoFlt9LWQn4YttrdnV6X2OiyzBi+A==

resolve@^1.1.7, resolve@^1.14.2, resolve@^1.20.0, resolve@^1.22.4, resolve@^1.22.8:
  version "1.22.10"
  resolved "https://registry.npmjs.org/resolve/-/resolve-1.22.10.tgz"
  integrity sha512-NPRy+/ncIMeDlTAsuqwKIiferiawhefFJtkNSW0qZJEqMEb+qBt/77B/jGeeek+F0uOeN05CDa6HXbbIgtVX4w==
  dependencies:
    is-core-module "^2.16.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

resolve@^2.0.0-next.5:
  version "2.0.0-next.5"
  resolved "https://registry.npmjs.org/resolve/-/resolve-2.0.0-next.5.tgz"
  integrity sha512-U7WjGVG9sH8tvjW5SmGbQuui75FiyjAX72HX15DwBBwF9dNiQZRQAg9nnPhYy+TUnE0+VcrttuvNI8oSxZcocA==
  dependencies:
    is-core-module "^2.13.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

resolve@1.22.8:
  version "1.22.8"
  resolved "https://registry.npmjs.org/resolve/-/resolve-1.22.8.tgz"
  integrity sha512-oKWePCxqpd6FlLvGV1VU0x7bkPmmCNolxzjMf4NczoDnQcIWrAF+cPtZn5i6n+RfD2d9i0tzpKnG6Yk168yIyw==
  dependencies:
    is-core-module "^2.13.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

reusify@^1.0.4:
  version "1.1.0"
  resolved "https://registry.npmjs.org/reusify/-/reusify-1.1.0.tgz"
  integrity sha512-g6QUff04oZpHs0eG5p83rFLhHeV00ug/Yf9nZM6fLeUrPguBTkTQOdpAWWspMh55TZfVQDPaN3NQJfbVRAxdIw==

rollup@4.35.0:
  version "4.35.0"
  resolved "https://registry.npmjs.org/rollup/-/rollup-4.35.0.tgz"
  integrity sha512-kg6oI4g+vc41vePJyO6dHt/yl0Rz3Thv0kJeVQ3D1kS3E5XSuKbPc29G4IpT/Kv1KQwgHVcN+HtyS+HYLNSvQg==
  dependencies:
    "@types/estree" "1.0.6"
  optionalDependencies:
    "@rollup/rollup-android-arm-eabi" "4.35.0"
    "@rollup/rollup-android-arm64" "4.35.0"
    "@rollup/rollup-darwin-arm64" "4.35.0"
    "@rollup/rollup-darwin-x64" "4.35.0"
    "@rollup/rollup-freebsd-arm64" "4.35.0"
    "@rollup/rollup-freebsd-x64" "4.35.0"
    "@rollup/rollup-linux-arm-gnueabihf" "4.35.0"
    "@rollup/rollup-linux-arm-musleabihf" "4.35.0"
    "@rollup/rollup-linux-arm64-gnu" "4.35.0"
    "@rollup/rollup-linux-arm64-musl" "4.35.0"
    "@rollup/rollup-linux-loongarch64-gnu" "4.35.0"
    "@rollup/rollup-linux-powerpc64le-gnu" "4.35.0"
    "@rollup/rollup-linux-riscv64-gnu" "4.35.0"
    "@rollup/rollup-linux-s390x-gnu" "4.35.0"
    "@rollup/rollup-linux-x64-gnu" "4.35.0"
    "@rollup/rollup-linux-x64-musl" "4.35.0"
    "@rollup/rollup-win32-arm64-msvc" "4.35.0"
    "@rollup/rollup-win32-ia32-msvc" "4.35.0"
    "@rollup/rollup-win32-x64-msvc" "4.35.0"
    fsevents "~2.3.2"

router@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/router/-/router-2.2.0.tgz"
  integrity sha512-nLTrUKm2UyiL7rlhapu/Zl45FwNgkZGaCpZbIHajDYgwlJCOzLSk+cIPAnsEqV955GjILJnKbdQC1nVPz+gAYQ==
  dependencies:
    debug "^4.4.0"
    depd "^2.0.0"
    is-promise "^4.0.0"
    parseurl "^1.3.3"
    path-to-regexp "^8.0.0"

rtl-css-js@^1.16.1:
  version "1.16.1"
  resolved "https://registry.npmjs.org/rtl-css-js/-/rtl-css-js-1.16.1.tgz"
  integrity sha512-lRQgou1mu19e+Ya0LsTvKrVJ5TYUbqCVPAiImX3UfLTenarvPUl1QFdvu5Z3PYmHT9RCcwIfbjRQBntExyj3Zg==
  dependencies:
    "@babel/runtime" "^7.1.2"

run-parallel@^1.1.9:
  version "1.2.0"
  resolved "https://registry.npmjs.org/run-parallel/-/run-parallel-1.2.0.tgz"
  integrity sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==
  dependencies:
    queue-microtask "^1.2.2"

safe-array-concat@^1.1.3:
  version "1.1.3"
  resolved "https://registry.npmjs.org/safe-array-concat/-/safe-array-concat-1.1.3.tgz"
  integrity sha512-AURm5f0jYEOydBj7VQlVvDrjeFgthDdEF5H1dP+6mNpoXOMo1quQqJ4wvJDyRZ9+pO3kGWoOdmV08cSv2aJV6Q==
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.2"
    get-intrinsic "^1.2.6"
    has-symbols "^1.1.0"
    isarray "^2.0.5"

safe-buffer@5.2.1:
  version "5.2.1"
  resolved "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz"
  integrity sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==

safe-push-apply@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/safe-push-apply/-/safe-push-apply-1.0.0.tgz"
  integrity sha512-iKE9w/Z7xCzUMIZqdBsp6pEQvwuEebH4vdpjcDWnyzaI6yl6O9FHvVpmGelvEHNsoY6wGblkxR6Zty/h00WiSA==
  dependencies:
    es-errors "^1.3.0"
    isarray "^2.0.5"

safe-regex-test@^1.0.3, safe-regex-test@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/safe-regex-test/-/safe-regex-test-1.1.0.tgz"
  integrity sha512-x/+Cz4YrimQxQccJf5mKEbIa1NzeCRNI5Ecl/ekmlYaampdNLPalVyIcCZNNH3MvmqBugV5TMYZXv0ljslUlaw==
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    is-regex "^1.2.1"

safe-stable-stringify@^2.3.1:
  version "2.5.0"
  resolved "https://registry.npmjs.org/safe-stable-stringify/-/safe-stable-stringify-2.5.0.tgz"
  integrity sha512-b3rppTKm9T+PsVCBEOUR46GWI7fdOs00VKZ1+9c1EWDaDMvjQc6tUwuFyIprgGgTcWoVHSKrU8H31ZHA2e0RHA==

"safer-buffer@>= 2.1.2 < 3.0.0":
  version "2.1.2"
  resolved "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz"
  integrity sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==

saxes@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmjs.org/saxes/-/saxes-6.0.0.tgz"
  integrity sha512-xAg7SOnEhrm5zI3puOOKyy1OMcMlIJZYNJY7xLBwSze0UjhPLnWfj2GF2EpT0jmzaJKIWKHLsaSSajf35bcYnA==
  dependencies:
    xmlchars "^2.2.0"

scheduler@^0.26.0:
  version "0.26.0"
  resolved "https://registry.npmjs.org/scheduler/-/scheduler-0.26.0.tgz"
  integrity sha512-NlHwttCI/l5gCPR3D1nNXtWABUmBwvZpEQiD4IXSbIDq8BzLIK/7Ir5gTFSGZDUu37K5cMNp0hFtzO38sC7gWA==

screenfull@^5.1.0:
  version "5.2.0"
  resolved "https://registry.npmjs.org/screenfull/-/screenfull-5.2.0.tgz"
  integrity sha512-9BakfsO2aUQN2K9Fdbj87RJIEZ82Q9IGim7FqM5OsebfoFC6ZHXgDq/KvniuLTPdeM8wY2o6Dj3WQ7KeQCj3cA==

secure-json-parse@^2.4.0, secure-json-parse@^2.7.0:
  version "2.7.0"
  resolved "https://registry.npmjs.org/secure-json-parse/-/secure-json-parse-2.7.0.tgz"
  integrity sha512-6aU+Rwsezw7VR8/nyvKTx8QpWH9FrcYiXXlqC4z5d5XQBDRqtbfsRjnwGyqbi3gddNtWHuEk9OANUotL26qKUw==

semver@^6.3.0, semver@^6.3.1:
  version "6.3.1"
  resolved "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz"
  integrity sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==

semver@^7.5.2:
  version "7.7.1"
  resolved "https://registry.npmjs.org/semver/-/semver-7.7.1.tgz"
  integrity sha512-hlq8tAfn0m/61p4BVRcPzIGr6LKiMwo4VM6dGi6pt4qcRkmNzTcWq6eCEjEh+qXjkMDvPlOFFSGwQjoEa6gyMA==

semver@^7.5.3:
  version "7.7.1"
  resolved "https://registry.npmjs.org/semver/-/semver-7.7.1.tgz"
  integrity sha512-hlq8tAfn0m/61p4BVRcPzIGr6LKiMwo4VM6dGi6pt4qcRkmNzTcWq6eCEjEh+qXjkMDvPlOFFSGwQjoEa6gyMA==

semver@^7.5.4:
  version "7.7.1"
  resolved "https://registry.npmjs.org/semver/-/semver-7.7.1.tgz"
  integrity sha512-hlq8tAfn0m/61p4BVRcPzIGr6LKiMwo4VM6dGi6pt4qcRkmNzTcWq6eCEjEh+qXjkMDvPlOFFSGwQjoEa6gyMA==

semver@^7.6.0:
  version "7.7.1"
  resolved "https://registry.npmjs.org/semver/-/semver-7.7.1.tgz"
  integrity sha512-hlq8tAfn0m/61p4BVRcPzIGr6LKiMwo4VM6dGi6pt4qcRkmNzTcWq6eCEjEh+qXjkMDvPlOFFSGwQjoEa6gyMA==

semver@^7.6.3:
  version "7.7.1"
  resolved "https://registry.npmjs.org/semver/-/semver-7.7.1.tgz"
  integrity sha512-hlq8tAfn0m/61p4BVRcPzIGr6LKiMwo4VM6dGi6pt4qcRkmNzTcWq6eCEjEh+qXjkMDvPlOFFSGwQjoEa6gyMA==

semver@^7.7.1:
  version "7.7.1"
  resolved "https://registry.npmjs.org/semver/-/semver-7.7.1.tgz"
  integrity sha512-hlq8tAfn0m/61p4BVRcPzIGr6LKiMwo4VM6dGi6pt4qcRkmNzTcWq6eCEjEh+qXjkMDvPlOFFSGwQjoEa6gyMA==

send@^1.1.0, send@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/send/-/send-1.2.0.tgz"
  integrity sha512-uaW0WwXKpL9blXE2o0bRhoL2EGXIrZxQ2ZQ4mgcfoBxdFmQold+qWsD2jLrfZ0trjKL6vOw0j//eAwcALFjKSw==
  dependencies:
    debug "^4.3.5"
    encodeurl "^2.0.0"
    escape-html "^1.0.3"
    etag "^1.8.1"
    fresh "^2.0.0"
    http-errors "^2.0.0"
    mime-types "^3.0.1"
    ms "^2.1.3"
    on-finished "^2.4.1"
    range-parser "^1.2.1"
    statuses "^2.0.1"

serve-static@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/serve-static/-/serve-static-2.2.0.tgz"
  integrity sha512-61g9pCh0Vnh7IutZjtLGGpTA355+OPn2TyDv/6ivP2h/AdAVX9azsoxmg2/M6nZeQZNYBEwIcsne1mJd9oQItQ==
  dependencies:
    encodeurl "^2.0.0"
    escape-html "^1.0.3"
    parseurl "^1.3.3"
    send "^1.2.0"

set-cookie-parser@^2.6.0:
  version "2.7.1"
  resolved "https://registry.npmjs.org/set-cookie-parser/-/set-cookie-parser-2.7.1.tgz"
  integrity sha512-IOc8uWeOZgnb3ptbCURJWNjWUPcO3ZnTTdzsurqERrP6nPyv+paC55vJM0LpOlT2ne+Ix+9+CRG1MNLlyZ4GjQ==

set-function-length@^1.2.2:
  version "1.2.2"
  resolved "https://registry.npmjs.org/set-function-length/-/set-function-length-1.2.2.tgz"
  integrity sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.2"

set-function-name@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/set-function-name/-/set-function-name-2.0.2.tgz"
  integrity sha512-7PGFlmtwsEADb0WYyvCMa1t+yke6daIG4Wirafur5kcf+MhUnPms1UeR0CKQdTZD81yESwMHbtn+TR+dMviakQ==
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    functions-have-names "^1.2.3"
    has-property-descriptors "^1.0.2"

set-harmonic-interval@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/set-harmonic-interval/-/set-harmonic-interval-1.0.1.tgz"
  integrity sha512-AhICkFV84tBP1aWqPwLZqFvAwqEoVA9kxNMniGEUvzOlm4vLmOFLiTT3UZ6bziJTy4bOVpzWGTfSCbmaayGx8g==

set-proto@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/set-proto/-/set-proto-1.0.0.tgz"
  integrity sha512-RJRdvCo6IAnPdsvP/7m6bsQqNnn1FCBX5ZNtFL98MmFF/4xAIJTIg1YbHW5DC2W5SKZanrC6i4HsJqlajw/dZw==
  dependencies:
    dunder-proto "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"

setprototypeof@1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.2.0.tgz"
  integrity sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw==

sharp@^0.33.5:
  version "0.33.5"
  resolved "https://registry.npmjs.org/sharp/-/sharp-0.33.5.tgz"
  integrity sha512-haPVm1EkS9pgvHrQ/F3Xy+hgcuMV0Wm9vfIBSiwZ05k+xgb0PkBQpGsAA/oWdDobNaZTH5ppvHtzCFbnSEwHVw==
  dependencies:
    color "^4.2.3"
    detect-libc "^2.0.3"
    semver "^7.6.3"
  optionalDependencies:
    "@img/sharp-darwin-arm64" "0.33.5"
    "@img/sharp-darwin-x64" "0.33.5"
    "@img/sharp-libvips-darwin-arm64" "1.0.4"
    "@img/sharp-libvips-darwin-x64" "1.0.4"
    "@img/sharp-libvips-linux-arm" "1.0.5"
    "@img/sharp-libvips-linux-arm64" "1.0.4"
    "@img/sharp-libvips-linux-s390x" "1.0.4"
    "@img/sharp-libvips-linux-x64" "1.0.4"
    "@img/sharp-libvips-linuxmusl-arm64" "1.0.4"
    "@img/sharp-libvips-linuxmusl-x64" "1.0.4"
    "@img/sharp-linux-arm" "0.33.5"
    "@img/sharp-linux-arm64" "0.33.5"
    "@img/sharp-linux-s390x" "0.33.5"
    "@img/sharp-linux-x64" "0.33.5"
    "@img/sharp-linuxmusl-arm64" "0.33.5"
    "@img/sharp-linuxmusl-x64" "0.33.5"
    "@img/sharp-wasm32" "0.33.5"
    "@img/sharp-win32-ia32" "0.33.5"
    "@img/sharp-win32-x64" "0.33.5"

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz"
  integrity sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz"
  integrity sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==

shimmer@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/shimmer/-/shimmer-1.2.1.tgz"
  integrity sha512-sQTKC1Re/rM6XyFM6fIAGHRPVGvyXfgzIDvzoq608vM+jeyVD0Tu1E6Np0Kc2zAIFWIj963V2800iF/9LPieQw==

side-channel-list@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/side-channel-list/-/side-channel-list-1.0.0.tgz"
  integrity sha512-FCLHtRD/gnpCiCHEiJLOwdmFP+wzCmDEkc9y7NsYxeF4u7Btsn1ZuwgwJGxImImHicJArLP4R0yX4c2KCrMrTA==
  dependencies:
    es-errors "^1.3.0"
    object-inspect "^1.13.3"

side-channel-map@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/side-channel-map/-/side-channel-map-1.0.1.tgz"
  integrity sha512-VCjCNfgMsby3tTdo02nbjtM/ewra6jPHmpThenkTYh8pG9ucZ/1P8So4u4FGBek/BjpOVsDCMoLA/iuBKIFXRA==
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.5"
    object-inspect "^1.13.3"

side-channel-weakmap@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/side-channel-weakmap/-/side-channel-weakmap-1.0.2.tgz"
  integrity sha512-WPS/HvHQTYnHisLo9McqBHOJk2FkHO/tlpvldyrnem4aeQp4hai3gythswg6p01oSoTl58rcpiFAjF2br2Ak2A==
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.5"
    object-inspect "^1.13.3"
    side-channel-map "^1.0.1"

side-channel@^1.0.4, side-channel@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/side-channel/-/side-channel-1.1.0.tgz"
  integrity sha512-ZX99e6tRweoUXqR+VBrslhda51Nh5MTQwou5tnUDgbtyM0dBgmhEDtWGP/xbKn6hqfPRHujUNwz5fy/wbbhnpw==
  dependencies:
    es-errors "^1.3.0"
    object-inspect "^1.13.3"
    side-channel-list "^1.0.0"
    side-channel-map "^1.0.1"
    side-channel-weakmap "^1.0.2"

signal-exit@^3.0.3, signal-exit@^3.0.7:
  version "3.0.7"
  resolved "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz"
  integrity sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==

signal-exit@^4.0.1:
  version "4.1.0"
  resolved "https://registry.npmjs.org/signal-exit/-/signal-exit-4.1.0.tgz"
  integrity sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==

simple-swizzle@^0.2.2:
  version "0.2.2"
  resolved "https://registry.npmjs.org/simple-swizzle/-/simple-swizzle-0.2.2.tgz"
  integrity sha512-JA//kQgZtbuY83m+xT+tXJkmJncGMTFT+C+g2h2R9uxkYIrE2yy9sgmcLhCnw57/WSD+Eh3J97FPEDFnbXnDUg==
  dependencies:
    is-arrayish "^0.3.1"

sisteransi@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmjs.org/sisteransi/-/sisteransi-1.0.5.tgz"
  integrity sha512-bLGGlR1QxBcynn2d5YmDX4MGjlZvy2MRBDRNHLJ8VI6l6+9FUiyTFNJ0IveOSP0bcXgVDPRcfGqA0pjaqUpfVg==

skin-tone@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/skin-tone/-/skin-tone-2.0.0.tgz"
  integrity sha512-kUMbT1oBJCpgrnKoSr0o6wPtvRWT9W9UKvGLwfJYO2WuahZRHOpEyL1ckyMGgMWh0UdpmaoFqKKD29WTomNEGA==
  dependencies:
    unicode-emoji-modifier-base "^1.0.0"

slash@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/slash/-/slash-3.0.0.tgz"
  integrity sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==

sonic-boom@^4.0.1:
  version "4.2.0"
  resolved "https://registry.npmjs.org/sonic-boom/-/sonic-boom-4.2.0.tgz"
  integrity sha512-INb7TM37/mAcsGmc9hyyI6+QR3rR1zVRu36B0NeGXKnOOLiZOfER5SA+N7X7k3yUYRzLWafduTDvJAfDswwEww==
  dependencies:
    atomic-sleep "^1.0.0"

sonner@^2.0.1:
  version "2.0.3"
  resolved "https://registry.npmjs.org/sonner/-/sonner-2.0.3.tgz"
  integrity sha512-njQ4Hht92m0sMqqHVDL32V2Oun9W1+PHO9NDv9FHfJjT3JT22IG4Jpo3FPQy+mouRKCXFWO+r67v6MrHX2zeIA==

source-map-js@^1.0.2, source-map-js@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/source-map-js/-/source-map-js-1.2.1.tgz"
  integrity sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==

source-map-support@0.5.13:
  version "0.5.13"
  resolved "https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.13.tgz"
  integrity sha512-SHSKFHadjVA5oR4PPqhtAVdcBWwRYVd6g6cAXnIbRiIwc2EhPrTuKUBdSLvlEKyIP3GCf89fltvcZiP9MMFA1w==
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map@^0.6.0, source-map@^0.6.1, source-map@~0.6.1:
  version "0.6.1"
  resolved "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz"
  integrity sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==

source-map@0.5.6:
  version "0.5.6"
  resolved "https://registry.npmjs.org/source-map/-/source-map-0.5.6.tgz"
  integrity sha512-MjZkVp0NHr5+TPihLcadqnlVoGIoWo4IBHptutGh9wI3ttUYvCG26HkSuDi+K6lsZ25syXJXcctwgyVCt//xqA==

space-separated-tokens@^1.0.0:
  version "1.1.5"
  resolved "https://registry.npmjs.org/space-separated-tokens/-/space-separated-tokens-1.1.5.tgz"
  integrity sha512-q/JSVd1Lptzhf5bkYm4ob4iWPjx0KiRe3sRFBNrVqbJkFaBm5vbbowy1mymoPNLRa52+oadOhJ+K49wsSeSjTA==

space-separated-tokens@^2.0.0:
  version "2.0.2"
  resolved "https://registry.npmjs.org/space-separated-tokens/-/space-separated-tokens-2.0.2.tgz"
  integrity sha512-PEGlAwrG8yXGXRjW32fGbg66JAlOAwbObuqVoJpv/mRgoWDQfgH1wDPvtzWyUSNAXBGSk8h755YDbbcEy3SH2Q==

spark-md5@^3.0.1:
  version "3.0.2"
  resolved "https://registry.npmjs.org/spark-md5/-/spark-md5-3.0.2.tgz"
  integrity sha512-wcFzz9cDfbuqe0FZzfi2or1sgyIrsDwmPwfZC4hiNidPdPINjeUwNfv5kldczoEAcjl9Y1L3SM7Uz2PUEQzxQw==

split2@^4.0.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/split2/-/split2-4.2.0.tgz"
  integrity sha512-UcjcJOWknrNkF6PLX83qcHM6KHgVKNkV62Y8a5uYDVv9ydGQVwAHMKqHdJje1VTWpljG0WYpCDhrCdAOYH4TWg==

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.0.3.tgz"
  integrity sha512-D9cPgkvLlV3t3IzL0D0YLvGA9Ahk4PcvVwUbN0dSGr1aP0Nrt4AEnTUbuGvquEC0mA64Gqt1fzirlRs5ibXx8g==

stable-hash@^0.0.5:
  version "0.0.5"
  resolved "https://registry.npmjs.org/stable-hash/-/stable-hash-0.0.5.tgz"
  integrity sha512-+L3ccpzibovGXFK+Ap/f8LOS0ahMrHTf3xu7mMLSpEGU0EO9ucaysSylKo9eRDFNhWve/y275iPmIZ4z39a9iA==

stack-generator@^2.0.3, stack-generator@^2.0.5:
  version "2.0.10"
  resolved "https://registry.npmjs.org/stack-generator/-/stack-generator-2.0.10.tgz"
  integrity sha512-mwnua/hkqM6pF4k8SnmZ2zfETsRUpWXREfA/goT8SLCV4iOFa4bzOX2nDipWAZFPTjLvQB82f5yaodMVhK0yJQ==
  dependencies:
    stackframe "^1.3.4"

stack-utils@^2.0.3:
  version "2.0.6"
  resolved "https://registry.npmjs.org/stack-utils/-/stack-utils-2.0.6.tgz"
  integrity sha512-XlkWvfIm6RmsWtNJx+uqtKLS8eqFbxUg0ZzLXqY0caEy9l7hruX8IpiDnjsLavoBgqCCR71TqWO8MaXYheJ3RQ==
  dependencies:
    escape-string-regexp "^2.0.0"

stackframe@^1.3.4:
  version "1.3.4"
  resolved "https://registry.npmjs.org/stackframe/-/stackframe-1.3.4.tgz"
  integrity sha512-oeVtt7eWQS+Na6F//S4kJ2K2VbRlS9D43mAlMyVpVWovy9o+jfgH8O9agzANzaiLjclA0oYzUXEM4PurhSUChw==

stacktrace-gps@^3.0.4:
  version "3.1.2"
  resolved "https://registry.npmjs.org/stacktrace-gps/-/stacktrace-gps-3.1.2.tgz"
  integrity sha512-GcUgbO4Jsqqg6RxfyTHFiPxdPqF+3LFmQhm7MgCuYQOYuWyqxo5pwRPz5d/u6/WYJdEnWfK4r+jGbyD8TSggXQ==
  dependencies:
    source-map "0.5.6"
    stackframe "^1.3.4"

stacktrace-js@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/stacktrace-js/-/stacktrace-js-2.0.2.tgz"
  integrity sha512-Je5vBeY4S1r/RnLydLl0TBTi3F2qdfWmYsGvtfZgEI+SCprPppaIhQf5nGcal4gI4cGpCV/duLcAzT1np6sQqg==
  dependencies:
    error-stack-parser "^2.0.6"
    stack-generator "^2.0.5"
    stacktrace-gps "^3.0.4"

stacktrace-parser@^0.1.10:
  version "0.1.11"
  resolved "https://registry.npmjs.org/stacktrace-parser/-/stacktrace-parser-0.1.11.tgz"
  integrity sha512-WjlahMgHmCJpqzU8bIBy4qtsZdU9lRlcZE3Lvyej6t4tuOuv1vk57OW3MBrj6hXBFx/nNoC9MPMTcr5YA7NQbg==
  dependencies:
    type-fest "^0.7.1"

state-local@^1.0.6:
  version "1.0.7"
  resolved "https://registry.npmjs.org/state-local/-/state-local-1.0.7.tgz"
  integrity sha512-HTEHMNieakEnoe33shBYcZ7NX83ACUjCu8c40iOGEZsngj9zRnkqS9j1pqQPXwobB0ZcVTk27REb7COQ0UR59w==

statuses@^2.0.1, statuses@2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/statuses/-/statuses-2.0.1.tgz"
  integrity sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==

stop-iteration-iterator@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/stop-iteration-iterator/-/stop-iteration-iterator-1.1.0.tgz"
  integrity sha512-eLoXW/DHyl62zxY4SCaIgnRhuMr6ri4juEYARS8E6sCEqzKpOiE521Ucofdx+KnDZl5xmvGYaaKCk5FEOxJCoQ==
  dependencies:
    es-errors "^1.3.0"
    internal-slot "^1.1.0"

streamsearch@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/streamsearch/-/streamsearch-1.1.0.tgz"
  integrity sha512-Mcc5wHehp9aXz1ax6bZUyY5afg9u2rv5cqQI3mRrYkGC8rW2hM02jWuwjtL++LS5qinSyhj2QfLyNsuc+VsExg==

string-length@^4.0.1:
  version "4.0.2"
  resolved "https://registry.npmjs.org/string-length/-/string-length-4.0.2.tgz"
  integrity sha512-+l6rNN5fYHNhZZy41RXsYptCjA2Igmq4EG7kZAYFQI1E1VTXarr6ZPXBg6eq7Y6eK4FEhY6AJlyuFIb/v/S0VQ==
  dependencies:
    char-regex "^1.0.2"
    strip-ansi "^6.0.0"

"string-width-cjs@npm:string-width@^4.2.0":
  version "4.2.3"
  resolved "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz"
  integrity sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^4.1.0, string-width@^4.2.0, string-width@^4.2.3:
  version "4.2.3"
  resolved "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz"
  integrity sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^5.0.1, string-width@^5.1.2:
  version "5.1.2"
  resolved "https://registry.npmjs.org/string-width/-/string-width-5.1.2.tgz"
  integrity sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==
  dependencies:
    eastasianwidth "^0.2.0"
    emoji-regex "^9.2.2"
    strip-ansi "^7.0.1"

string.prototype.includes@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/string.prototype.includes/-/string.prototype.includes-2.0.1.tgz"
  integrity sha512-o7+c9bW6zpAdJHTtujeePODAhkuicdAryFsfVKwA+wGw89wJ4GTY484WTucM9hLtDEOpOvI+aHnzqnC5lHp4Rg==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.3"

string.prototype.matchall@^4.0.12:
  version "4.0.12"
  resolved "https://registry.npmjs.org/string.prototype.matchall/-/string.prototype.matchall-4.0.12.tgz"
  integrity sha512-6CC9uyBL+/48dYizRf7H7VAYCMCNTBeM78x/VTUe9bFEaxBepPJDa1Ow99LqI/1yF7kuy7Q3cQsYMrcjGUcskA==
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    es-abstract "^1.23.6"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    get-intrinsic "^1.2.6"
    gopd "^1.2.0"
    has-symbols "^1.1.0"
    internal-slot "^1.1.0"
    regexp.prototype.flags "^1.5.3"
    set-function-name "^2.0.2"
    side-channel "^1.1.0"

string.prototype.repeat@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/string.prototype.repeat/-/string.prototype.repeat-1.0.0.tgz"
  integrity sha512-0u/TldDbKD8bFCQ/4f5+mNRrXwZ8hg2w7ZR8wa16e8z9XpePWl3eGEcUD0OXpEH/VJH/2G3gjUtR3ZOiBe2S/w==
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.5"

string.prototype.trim@^1.2.10:
  version "1.2.10"
  resolved "https://registry.npmjs.org/string.prototype.trim/-/string.prototype.trim-1.2.10.tgz"
  integrity sha512-Rs66F0P/1kedk5lyYyH9uBzuiI/kNRmwJAR9quK6VOtIpZ2G+hMZd+HQbbv25MgCA6gEffoMZYxlTod4WcdrKA==
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.2"
    define-data-property "^1.1.4"
    define-properties "^1.2.1"
    es-abstract "^1.23.5"
    es-object-atoms "^1.0.0"
    has-property-descriptors "^1.0.2"

string.prototype.trimend@^1.0.8, string.prototype.trimend@^1.0.9:
  version "1.0.9"
  resolved "https://registry.npmjs.org/string.prototype.trimend/-/string.prototype.trimend-1.0.9.tgz"
  integrity sha512-G7Ok5C6E/j4SGfyLCloXTrngQIQU3PWtXGst3yM7Bea9FRURf1S42ZHlZZtsNque2FN2PoUhfZXYLNWwEr4dLQ==
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.2"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

string.prototype.trimstart@^1.0.8:
  version "1.0.8"
  resolved "https://registry.npmjs.org/string.prototype.trimstart/-/string.prototype.trimstart-1.0.8.tgz"
  integrity sha512-UXSH262CSZY1tfu3G3Secr6uGLCFVPMhIqHjlgCUtCCcgihYc/xKs9djMTMUOb2j1mVSeU8EU6NWc/iQKU6Gfg==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

stringify-entities@^4.0.0:
  version "4.0.4"
  resolved "https://registry.npmjs.org/stringify-entities/-/stringify-entities-4.0.4.tgz"
  integrity sha512-IwfBptatlO+QCJUo19AqvrPNqlVMpW9YEL2LIVY+Rpv2qsjCGxaDLNRgeGsQWJhfItebuJhsGSLjaBbNSQ+ieg==
  dependencies:
    character-entities-html4 "^2.0.0"
    character-entities-legacy "^3.0.0"

"strip-ansi-cjs@npm:strip-ansi@^6.0.1":
  version "6.0.1"
  resolved "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz"
  integrity sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz"
  integrity sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^7.0.1:
  version "7.1.0"
  resolved "https://registry.npmjs.org/strip-ansi/-/strip-ansi-7.1.0.tgz"
  integrity sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==
  dependencies:
    ansi-regex "^6.0.1"

strip-bom@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/strip-bom/-/strip-bom-3.0.0.tgz"
  integrity sha512-vavAMRXOgBVNF6nyEEmL3DBK19iRpDcoIwW+swQ+CbGiu7lju6t+JklA1MHweoWtadgt4ISVUsXLyDq34ddcwA==

strip-bom@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/strip-bom/-/strip-bom-4.0.0.tgz"
  integrity sha512-3xurFv5tEgii33Zi8Jtp55wEIILR9eh34FAW00PZf+JnSsTmV/ioewSgQl97JHvgjoRGwPShsWm+IdrxB35d0w==

strip-final-newline@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/strip-final-newline/-/strip-final-newline-2.0.0.tgz"
  integrity sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA==

strip-indent@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/strip-indent/-/strip-indent-3.0.0.tgz"
  integrity sha512-laJTa3Jb+VQpaC6DseHhF7dXVqHTfJPCRDaEbid/drOhgitgYku/letMUqOXFoWV0zIIUbjpdH2t+tYj4bQMRQ==
  dependencies:
    min-indent "^1.0.0"

strip-json-comments@^3.1.1:
  version "3.1.1"
  resolved "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.1.tgz"
  integrity sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==

stripe@^18.0.0:
  version "18.1.0"
  resolved "https://registry.npmjs.org/stripe/-/stripe-18.1.0.tgz"
  integrity sha512-MLDiniPTHqcfIT3anyBPmOEcaiDhYa7/jRaNypQ3Rt2SJnayQZBvVbFghIziUCZdltGAndm/ZxVOSw6uuSCDig==
  dependencies:
    qs "^6.11.0"

style-to-js@^1.0.0:
  version "1.1.16"
  resolved "https://registry.npmjs.org/style-to-js/-/style-to-js-1.1.16.tgz"
  integrity sha512-/Q6ld50hKYPH3d/r6nr117TZkHR0w0kGGIVfpG9N6D8NymRPM9RqCUv4pRpJ62E5DqOYx2AFpbZMyCPnjQCnOw==
  dependencies:
    style-to-object "1.0.8"

style-to-object@1.0.8:
  version "1.0.8"
  resolved "https://registry.npmjs.org/style-to-object/-/style-to-object-1.0.8.tgz"
  integrity sha512-xT47I/Eo0rwJmaXC4oilDGDWLohVhR6o/xAQcPQN8q6QBuZVL8qMYL85kLmST5cPjAorwvqIA4qXTRQoYHaL6g==
  dependencies:
    inline-style-parser "0.2.4"

styled-jsx@5.1.6:
  version "5.1.6"
  resolved "https://registry.npmjs.org/styled-jsx/-/styled-jsx-5.1.6.tgz"
  integrity sha512-qSVyDTeMotdvQYoHWLNGwRFJHC+i+ZvdBRYosOFgC+Wg1vx4frN2/RG/NA7SYqqvKNLf39P2LSRA2pu6n0XYZA==
  dependencies:
    client-only "0.0.1"

stylis@^4.3.0:
  version "4.3.6"
  resolved "https://registry.npmjs.org/stylis/-/stylis-4.3.6.tgz"
  integrity sha512-yQ3rwFWRfwNUY7H5vpU0wfdkNSnvnJinhF9830Swlaxl03zsOjCfmX0ugac+3LtK0lYSgwL/KXc8oYL3mG4YFQ==

sucrase@^3.35.0:
  version "3.35.0"
  resolved "https://registry.npmjs.org/sucrase/-/sucrase-3.35.0.tgz"
  integrity sha512-8EbVDiu9iN/nESwxeSxDKe0dunta1GOlHufmSSXxMD2z2/tMZpDMpvXQGsc+ajGo8y2uYUmixaSRUc/QPoQ0GA==
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.2"
    commander "^4.0.0"
    glob "^10.3.10"
    lines-and-columns "^1.1.6"
    mz "^2.7.0"
    pirates "^4.0.1"
    ts-interface-checker "^0.1.9"

supabase@^1.149.0:
  version "1.226.4"
  resolved "https://registry.npmjs.org/supabase/-/supabase-1.226.4.tgz"
  integrity sha512-qEzoagrqZs5T7sAlfZzehX3PJ13cSBrJVs2vrh6xC+B0VI0wgOBw2gCNRcsOMJMpSr0V1l0XueCiFBWPm2U03w==
  dependencies:
    bin-links "^5.0.0"
    https-proxy-agent "^7.0.2"
    node-fetch "^3.3.2"
    tar "7.4.3"

supports-color@^7.1.0:
  version "7.2.0"
  resolved "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz"
  integrity sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==
  dependencies:
    has-flag "^4.0.0"

supports-color@^8.0.0:
  version "8.1.1"
  resolved "https://registry.npmjs.org/supports-color/-/supports-color-8.1.1.tgz"
  integrity sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q==
  dependencies:
    has-flag "^4.0.0"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz"
  integrity sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==

swr@^2.2.5:
  version "2.3.3"
  resolved "https://registry.npmjs.org/swr/-/swr-2.3.3.tgz"
  integrity sha512-dshNvs3ExOqtZ6kJBaAsabhPdHyeY4P2cKwRCniDVifBMoG/SVI7tfLWqPXriVspf2Rg4tPzXJTnwaihIeFw2A==
  dependencies:
    dequal "^2.0.3"
    use-sync-external-store "^1.4.0"

symbol-tree@^3.2.4:
  version "3.2.4"
  resolved "https://registry.npmjs.org/symbol-tree/-/symbol-tree-3.2.4.tgz"
  integrity sha512-9QNk5KwDF+Bvz+PyObkmSYjI5ksVUYtjW7AU22r2NKcfLJcXp96hkDWU3+XndOsUb+AQ9QhfzfCT2O+CNWT5Tw==

tailwind-merge@^2.6.0:
  version "2.6.0"
  resolved "https://registry.npmjs.org/tailwind-merge/-/tailwind-merge-2.6.0.tgz"
  integrity sha512-P+Vu1qXfzediirmHOC3xKGAYeZtPcV9g76X+xg2FD4tYgR71ewMA35Y3sCz3zhiN/dwefRpJX0yBcgwi1fXNQA==

tailwind-merge@^3.0.1:
  version "3.2.0"
  resolved "https://registry.npmjs.org/tailwind-merge/-/tailwind-merge-3.2.0.tgz"
  integrity sha512-FQT/OVqCD+7edmmJpsgCsY820RTD5AkBryuG5IUqR5YQZSdj5xlH5nLgH7YPths7WsLPSpSBNneJdM8aS8aeFA==

tailwindcss-animate@^1.0.7:
  version "1.0.7"
  resolved "https://registry.npmjs.org/tailwindcss-animate/-/tailwindcss-animate-1.0.7.tgz"
  integrity sha512-bl6mpH3T7I3UFxuvDEXLxy/VuFxBk5bbzplh7tXI68mwMokNYd1t9qPBHlnyTwfa4JGC4zP516I1hYYtQ/vspA==

tailwindcss@^3.4.1:
  version "3.4.17"
  resolved "https://registry.npmjs.org/tailwindcss/-/tailwindcss-3.4.17.tgz"
  integrity sha512-w33E2aCvSDP0tW9RZuNXadXlkHXqFzSkQew/aIa2i/Sj8fThxwovwlXHSPXTbAHwEIhBFXAedUhP2tueAKP8Og==
  dependencies:
    "@alloc/quick-lru" "^5.2.0"
    arg "^5.0.2"
    chokidar "^3.6.0"
    didyoumean "^1.2.2"
    dlv "^1.1.3"
    fast-glob "^3.3.2"
    glob-parent "^6.0.2"
    is-glob "^4.0.3"
    jiti "^1.21.6"
    lilconfig "^3.1.3"
    micromatch "^4.0.8"
    normalize-path "^3.0.0"
    object-hash "^3.0.0"
    picocolors "^1.1.1"
    postcss "^8.4.47"
    postcss-import "^15.1.0"
    postcss-js "^4.0.1"
    postcss-load-config "^4.0.2"
    postcss-nested "^6.2.0"
    postcss-selector-parser "^6.1.2"
    resolve "^1.22.8"
    sucrase "^3.35.0"

tar@7.4.3:
  version "7.4.3"
  resolved "https://registry.npmjs.org/tar/-/tar-7.4.3.tgz"
  integrity sha512-5S7Va8hKfV7W5U6g3aYxXmlPoZVAwUMy9AOKyF2fVuZa2UD3qZjg578OrLRt8PcNN1PleVaL/5/yYATNL0ICUw==
  dependencies:
    "@isaacs/fs-minipass" "^4.0.0"
    chownr "^3.0.0"
    minipass "^7.1.2"
    minizlib "^3.0.1"
    mkdirp "^3.0.1"
    yallist "^5.0.0"

test-exclude@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmjs.org/test-exclude/-/test-exclude-6.0.0.tgz"
  integrity sha512-cAGWPIyOHU6zlmg88jwm7VRyXnMN7iV68OGAbYDk/Mh/xC/pzVPlQtY6ngoIH/5/tciuhGfvESU8GrHrcxD56w==
  dependencies:
    "@istanbuljs/schema" "^0.1.2"
    glob "^7.1.4"
    minimatch "^3.0.4"

thenify-all@^1.0.0:
  version "1.6.0"
  resolved "https://registry.npmjs.org/thenify-all/-/thenify-all-1.6.0.tgz"
  integrity sha512-RNxQH/qI8/t3thXJDwcstUO4zeqo64+Uy/+sNVRBx4Xn2OX+OZ9oP+iJnNFqplFra2ZUVeKCSa2oVWi3T4uVmA==
  dependencies:
    thenify ">= 3.1.0 < 4"

"thenify@>= 3.1.0 < 4":
  version "3.3.1"
  resolved "https://registry.npmjs.org/thenify/-/thenify-3.3.1.tgz"
  integrity sha512-RVZSIV5IG10Hk3enotrhvz0T9em6cyHBLkH/YAZuKqd8hRkKhSfCGIcP2KUY0EPxndzANBmNllzWPwak+bheSw==
  dependencies:
    any-promise "^1.0.0"

thread-stream@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/thread-stream/-/thread-stream-3.1.0.tgz"
  integrity sha512-OqyPZ9u96VohAyMfJykzmivOrY2wfMSf3C5TtFJVgN+Hm6aj+voFhlK+kZEIv2FBh1X6Xp3DlnCOfEQ3B2J86A==
  dependencies:
    real-require "^0.2.0"

throttle-debounce@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/throttle-debounce/-/throttle-debounce-3.0.1.tgz"
  integrity sha512-dTEWWNu6JmeVXY0ZYoPuH5cRIwc0MeGbJwah9KUNYSJwommQpCzTySTpEe8Gs1J23aeWEuAobe4Ag7EHVt/LOg==

throttleit@2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/throttleit/-/throttleit-2.1.0.tgz"
  integrity sha512-nt6AMGKW1p/70DF/hGBdJB57B8Tspmbp5gfJ8ilhLnt7kkr2ye7hzD6NVG8GGErk2HWF34igrL2CXmNIkzKqKw==

tiktoken@^1.0.10:
  version "1.0.21"
  resolved "https://registry.npmjs.org/tiktoken/-/tiktoken-1.0.21.tgz"
  integrity sha512-/kqtlepLMptX0OgbYD9aMYbM7EFrMZCL7EoHM8Psmg2FuhXoo/bH64KqOiZGGwa6oS9TPdSEDKBnV2LuB8+5vQ==

tinyglobby@^0.2.13:
  version "0.2.13"
  resolved "https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.2.13.tgz"
  integrity sha512-mEwzpUgrLySlveBwEVDMKk5B57bhLPYovRfPAXD5gA/98Opn0rCDj3GtLwFvCvH5RK9uPCExUROW5NjDwvqkxw==
  dependencies:
    fdir "^6.4.4"
    picomatch "^4.0.2"

tmpl@1.0.5:
  version "1.0.5"
  resolved "https://registry.npmjs.org/tmpl/-/tmpl-1.0.5.tgz"
  integrity sha512-3f0uOEAQwIqGuWW2MVzYg8fV/QNnc/IpuJNG837rLuczAaLVHslWHZQj4IGiEl5Hs3kkbhwL9Ab7Hrsmuj+Smw==

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz"
  integrity sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==
  dependencies:
    is-number "^7.0.0"

toggle-selection@^1.0.6:
  version "1.0.6"
  resolved "https://registry.npmjs.org/toggle-selection/-/toggle-selection-1.0.6.tgz"
  integrity sha512-BiZS+C1OS8g/q2RRbJmy59xpyghNBqrr6k5L/uKBGRsTfxmu3ffiRnd8mlGPUVayg8pvfi5urfnu8TU7DVOkLQ==

toidentifier@1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/toidentifier/-/toidentifier-1.0.1.tgz"
  integrity sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA==

tough-cookie@^4.1.2:
  version "4.1.4"
  resolved "https://registry.npmjs.org/tough-cookie/-/tough-cookie-4.1.4.tgz"
  integrity sha512-Loo5UUvLD9ScZ6jh8beX1T6sO1w2/MpCRpEP7V280GKMVUQ0Jzar2U3UJPsrdbziLEMMhu3Ujnq//rhiFuIeag==
  dependencies:
    psl "^1.1.33"
    punycode "^2.1.1"
    universalify "^0.2.0"
    url-parse "^1.5.3"

tr46@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/tr46/-/tr46-3.0.0.tgz"
  integrity sha512-l7FvfAHlcmulp8kr+flpQZmVwtu7nfRV7NZujtN0OqES8EL4O4e0qqzL0DC5gAvx/ZC/9lk6rhcUwYvkBnBnYA==
  dependencies:
    punycode "^2.1.1"

tr46@~0.0.3:
  version "0.0.3"
  resolved "https://registry.npmjs.org/tr46/-/tr46-0.0.3.tgz"
  integrity sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw==

tracekit@^0.4.7:
  version "0.4.7"
  resolved "https://registry.npmjs.org/tracekit/-/tracekit-0.4.7.tgz"
  integrity sha512-d3BLRambfZ0VOYLIh7FRSElugsOMrNgxzT4jXyOXC7lnhoeEJIHNg8VAcpKVVmu9kREGUAVg8Eh3SlRfkU/ksQ==

trim-lines@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmjs.org/trim-lines/-/trim-lines-3.0.1.tgz"
  integrity sha512-kRj8B+YHZCc9kQYdWfJB2/oUl9rA99qbowYYBtr4ui4mZyAQ2JpvVBd/6U2YloATfqBhBTSMhTpgBHtU0Mf3Rg==

trough@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/trough/-/trough-2.2.0.tgz"
  integrity sha512-tmMpK00BjZiUyVyvrBK7knerNgmgvcV/KLVyuma/SC+TQN167GrMRciANTz09+k3zW8L8t60jWO1GpfkZdjTaw==

ts-api-utils@^2.0.1:
  version "2.1.0"
  resolved "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-2.1.0.tgz"
  integrity sha512-CUgTZL1irw8u29bzrOD/nH85jqyc74D6SshFgujOIA7osm2Rz7dYH77agkx7H4FBNxDq7Cjf+IjaX/8zwFW+ZQ==

ts-easing@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/ts-easing/-/ts-easing-0.2.0.tgz"
  integrity sha512-Z86EW+fFFh/IFB1fqQ3/+7Zpf9t2ebOAxNI/V6Wo7r5gqiqtxmgTlQ1qbqQcjLKYeSHPTsEmvlJUDg/EuL0uHQ==

ts-interface-checker@^0.1.9:
  version "0.1.13"
  resolved "https://registry.npmjs.org/ts-interface-checker/-/ts-interface-checker-0.1.13.tgz"
  integrity sha512-Y/arvbn+rrz3JCKl9C4kVNfTfSm2/mEp5FSz5EsZSANGPSlQrpRI5M4PKF+mJnE52jOO90PnPSc3Ur3bTQw0gA==

ts-jest@^29.1.2:
  version "29.3.2"
  resolved "https://registry.npmjs.org/ts-jest/-/ts-jest-29.3.2.tgz"
  integrity sha512-bJJkrWc6PjFVz5g2DGCNUo8z7oFEYaz1xP1NpeDU7KNLMWPpEyV8Chbpkn8xjzgRDpQhnGMyvyldoL7h8JXyug==
  dependencies:
    bs-logger "^0.2.6"
    ejs "^3.1.10"
    fast-json-stable-stringify "^2.1.0"
    jest-util "^29.0.0"
    json5 "^2.2.3"
    lodash.memoize "^4.1.2"
    make-error "^1.3.6"
    semver "^7.7.1"
    type-fest "^4.39.1"
    yargs-parser "^21.1.1"

ts-node@^10.9.2:
  version "10.9.2"
  resolved "https://registry.npmjs.org/ts-node/-/ts-node-10.9.2.tgz"
  integrity sha512-f0FFpIdcHgn8zcPSbf1dRevwt047YMnaiJM3u2w2RewrB+fob/zePZcrOyQoLMMO7aBIddLcQIEK5dYjkLnGrQ==
  dependencies:
    "@cspotcode/source-map-support" "^0.8.0"
    "@tsconfig/node10" "^1.0.7"
    "@tsconfig/node12" "^1.0.7"
    "@tsconfig/node14" "^1.0.0"
    "@tsconfig/node16" "^1.0.2"
    acorn "^8.4.1"
    acorn-walk "^8.1.1"
    arg "^4.1.0"
    create-require "^1.1.0"
    diff "^4.0.1"
    make-error "^1.1.1"
    v8-compile-cache-lib "^3.0.1"
    yn "3.1.1"

tsconfig-paths@^3.15.0:
  version "3.15.0"
  resolved "https://registry.npmjs.org/tsconfig-paths/-/tsconfig-paths-3.15.0.tgz"
  integrity sha512-2Ac2RgzDe/cn48GvOe3M+o82pEFewD3UPbyoUHHdKasHwJKjds4fLXWf/Ux5kATBKN20oaFGu+jbElp1pos0mg==
  dependencies:
    "@types/json5" "^0.0.29"
    json5 "^1.0.2"
    minimist "^1.2.6"
    strip-bom "^3.0.0"

tslib@^2.0.0, tslib@^2.1.0, tslib@^2.4.0, tslib@^2.4.1, tslib@^2.8.0:
  version "2.8.1"
  resolved "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz"
  integrity sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==

type-check@^0.4.0, type-check@~0.4.0:
  version "0.4.0"
  resolved "https://registry.npmjs.org/type-check/-/type-check-0.4.0.tgz"
  integrity sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==
  dependencies:
    prelude-ls "^1.2.1"

type-detect@4.0.8:
  version "4.0.8"
  resolved "https://registry.npmjs.org/type-detect/-/type-detect-4.0.8.tgz"
  integrity sha512-0fr/mIH1dlO+x7TlcMy+bIDqKPsw/70tVyeHW787goQjhmqaZe10uwLujubK9q9Lg6Fiho1KUKDYz0Z7k7g5/g==

type-fest@^0.21.3:
  version "0.21.3"
  resolved "https://registry.npmjs.org/type-fest/-/type-fest-0.21.3.tgz"
  integrity sha512-t0rzBq87m3fVcduHDUFhKmyyX+9eo6WQjZvf51Ea/M0Q7+T374Jp1aUiyUl0GKxp8M/OETVHSDvmkyPgvX+X2w==

type-fest@^0.7.1:
  version "0.7.1"
  resolved "https://registry.npmjs.org/type-fest/-/type-fest-0.7.1.tgz"
  integrity sha512-Ne2YiiGN8bmrmJJEuTWTLJR32nh/JdL1+PSicowtNb0WFpn59GK8/lfD61bVtzguz7b3PBt74nxpv/Pw5po5Rg==

type-fest@^4.39.1:
  version "4.40.1"
  resolved "https://registry.npmjs.org/type-fest/-/type-fest-4.40.1.tgz"
  integrity sha512-9YvLNnORDpI+vghLU/Nf+zSv0kL47KbVJ1o3sKgoTefl6i+zebxbiDQWoe/oWWqPhIgQdRZRT1KA9sCPL810SA==

type-is@^2.0.0, type-is@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/type-is/-/type-is-2.0.1.tgz"
  integrity sha512-OZs6gsjF4vMp32qrCbiVSkrFmXtG/AZhY3t0iAMrMBiAZyV9oALtXO8hsrHbMXF9x6L3grlFuwW2oAz7cav+Gw==
  dependencies:
    content-type "^1.0.5"
    media-typer "^1.1.0"
    mime-types "^3.0.0"

typed-array-buffer@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/typed-array-buffer/-/typed-array-buffer-1.0.3.tgz"
  integrity sha512-nAYYwfY3qnzX30IkA6AQZjVbtK6duGontcQm1WSG1MD94YLqK0515GNApXkoxKOWMusVssAHWLh9SeaoefYFGw==
  dependencies:
    call-bound "^1.0.3"
    es-errors "^1.3.0"
    is-typed-array "^1.1.14"

typed-array-byte-length@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/typed-array-byte-length/-/typed-array-byte-length-1.0.3.tgz"
  integrity sha512-BaXgOuIxz8n8pIq3e7Atg/7s+DpiYrxn4vdot3w9KbnBhcRQq6o3xemQdIfynqSeXeDrF32x+WvfzmOjPiY9lg==
  dependencies:
    call-bind "^1.0.8"
    for-each "^0.3.3"
    gopd "^1.2.0"
    has-proto "^1.2.0"
    is-typed-array "^1.1.14"

typed-array-byte-offset@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmjs.org/typed-array-byte-offset/-/typed-array-byte-offset-1.0.4.tgz"
  integrity sha512-bTlAFB/FBYMcuX81gbL4OcpH5PmlFHqlCCpAl8AlEzMz5k53oNDvN8p1PNOWLEmI2x4orp3raOFB51tv9X+MFQ==
  dependencies:
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.8"
    for-each "^0.3.3"
    gopd "^1.2.0"
    has-proto "^1.2.0"
    is-typed-array "^1.1.15"
    reflect.getprototypeof "^1.0.9"

typed-array-length@^1.0.7:
  version "1.0.7"
  resolved "https://registry.npmjs.org/typed-array-length/-/typed-array-length-1.0.7.tgz"
  integrity sha512-3KS2b+kL7fsuk/eJZ7EQdnEmQoaho/r6KUef7hxvltNA5DR8NAUM+8wJMbJyZ4G9/7i3v5zPBIMN5aybAh2/Jg==
  dependencies:
    call-bind "^1.0.7"
    for-each "^0.3.3"
    gopd "^1.0.1"
    is-typed-array "^1.1.13"
    possible-typed-array-names "^1.0.0"
    reflect.getprototypeof "^1.0.6"

typescript-eslint@^8.26.0:
  version "8.31.1"
  resolved "https://registry.npmjs.org/typescript-eslint/-/typescript-eslint-8.31.1.tgz"
  integrity sha512-j6DsEotD/fH39qKzXTQRwYYWlt7D+0HmfpOK+DVhwJOFLcdmn92hq3mBb7HlKJHbjjI/gTOqEcc9d6JfpFf/VA==
  dependencies:
    "@typescript-eslint/eslint-plugin" "8.31.1"
    "@typescript-eslint/parser" "8.31.1"
    "@typescript-eslint/utils" "8.31.1"

typescript@^4.9.5:
  version "4.9.5"
  resolved "https://registry.npmjs.org/typescript/-/typescript-4.9.5.tgz"
  integrity sha512-1FXk9E2Hm+QzZQ7z+McJiHL4NW1F2EzMu9Nq9i3zAaGqibafqYwCVU6WyWAuyQRRzOlxou8xZSyXLEN8oKj24g==

typescript@^5:
  version "5.8.3"
  resolved "https://registry.npmjs.org/typescript/-/typescript-5.8.3.tgz"
  integrity sha512-p1diW6TqL9L07nNxvRMM7hMMw4c5XOo/1ibL4aAIGmSAt9slTE1Xgw5KWuof2uTOvCg9BY7ZRi+GaF+7sfgPeQ==

ua-parser-js@^1.0.33, ua-parser-js@^1.0.37:
  version "1.0.40"
  resolved "https://registry.npmjs.org/ua-parser-js/-/ua-parser-js-1.0.40.tgz"
  integrity sha512-z6PJ8Lml+v3ichVojCiB8toQJBuwR42ySM4ezjXIqXK3M0HczmKQ3LF4rhU55PfD99KEEXQG6yb7iOMyvYuHew==

unbox-primitive@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/unbox-primitive/-/unbox-primitive-1.1.0.tgz"
  integrity sha512-nWJ91DjeOkej/TA8pXQ3myruKpKEYgqvpw9lz4OPHj/NWFNluYrjbz9j01CJ8yKQd2g4jFoOkINCTW2I5LEEyw==
  dependencies:
    call-bound "^1.0.3"
    has-bigints "^1.0.2"
    has-symbols "^1.1.0"
    which-boxed-primitive "^1.1.1"

undici-types@~5.26.4:
  version "5.26.5"
  resolved "https://registry.npmjs.org/undici-types/-/undici-types-5.26.5.tgz"
  integrity sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA==

undici-types@~6.19.2:
  version "6.19.8"
  resolved "https://registry.npmjs.org/undici-types/-/undici-types-6.19.8.tgz"
  integrity sha512-ve2KP6f/JnbPBFyobGHuerC9g1FYGn/F8n1LWTwNxCEzd6IfqTwUQcNXgEtmmQ6DlRrC1hrSrBnCZPokRrDHjw==

undici-types@~6.21.0:
  version "6.21.0"
  resolved "https://registry.npmjs.org/undici-types/-/undici-types-6.21.0.tgz"
  integrity sha512-iwDZqg0QAGrg9Rav5H4n0M64c3mkR59cJ6wQp+7C4nI0gsmExaedaYLNO44eT4AtBBwjbTiGPMlt2Md0T9H9JQ==

undici-types@~7.8.0:
  version "7.8.0"
  resolved "https://registry.npmjs.org/undici-types/-/undici-types-7.8.0.tgz"
  integrity sha512-9UJ2xGDvQ43tYyVMpuHlsgApydB8ZKfVYTsLDhXkFL/6gfkp+U8xTGdh8pMJv1SpZna0zxG1DwsKZsreLbXBxw==

unfetch@^3.1.1:
  version "3.1.2"
  resolved "https://registry.npmjs.org/unfetch/-/unfetch-3.1.2.tgz"
  integrity sha512-L0qrK7ZeAudGiKYw6nzFjnJ2D5WHblUBwmHIqtPS6oKUd+Hcpk7/hKsSmcHsTlpd1TbTNsiRBUKRq3bHLNIqIw==

unfetch@^4.1.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/unfetch/-/unfetch-4.2.0.tgz"
  integrity sha512-F9p7yYCn6cIW9El1zi0HI6vqpeIvBsr3dSuRO6Xuppb1u5rXpCPmMvLSyECLhybr9isec8Ohl0hPekMVrEinDA==

unicode-canonical-property-names-ecmascript@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-2.0.1.tgz"
  integrity sha512-dA8WbNeb2a6oQzAQ55YlT5vQAWGV9WXOsi3SskE3bcCdM0P4SDd+24zS/OCacdRq5BkdsRj9q3Pg6YyQoxIGqg==

unicode-emoji-json@^0.8.0:
  version "0.8.0"
  resolved "https://registry.npmjs.org/unicode-emoji-json/-/unicode-emoji-json-0.8.0.tgz"
  integrity sha512-3wDXXvp6YGoKGhS2O2H7+V+bYduOBydN1lnI0uVfr1cIdY02uFFiEH1i3kE5CCE4l6UqbLKVmEFW9USxTAMD1g==

unicode-emoji-modifier-base@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/unicode-emoji-modifier-base/-/unicode-emoji-modifier-base-1.0.0.tgz"
  integrity sha512-yLSH4py7oFH3oG/9K+XWrz1pSi3dfUrWEnInbxMfArOfc1+33BlGPQtLsOYwvdMy11AwUBetYuaRxSPqgkq+8g==

unicode-match-property-ecmascript@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-2.0.0.tgz"
  integrity sha512-5kaZCrbp5mmbz5ulBkDkbY0SsPOjKqVS35VpL9ulMPfSl0J0Xsm+9Evphv9CoIZFwre7aJoa94AY6seMKGVN5Q==
  dependencies:
    unicode-canonical-property-names-ecmascript "^2.0.0"
    unicode-property-aliases-ecmascript "^2.0.0"

unicode-match-property-value-ecmascript@^2.1.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/unicode-match-property-value-ecmascript/-/unicode-match-property-value-ecmascript-2.2.0.tgz"
  integrity sha512-4IehN3V/+kkr5YeSSDDQG8QLqO26XpL2XP3GQtqwlT/QYSECAwFztxVHjlbh0+gjJ3XmNLS0zDsbgs9jWKExLg==

unicode-property-aliases-ecmascript@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-2.1.0.tgz"
  integrity sha512-6t3foTQI9qne+OZoVQB/8x8rk2k1eVy1gRXhV3oFQ5T6R1dqQ1xtin3XqSlx3+ATBkliTaR/hHyJBm+LVPNM8w==

unified@^11.0.0:
  version "11.0.5"
  resolved "https://registry.npmjs.org/unified/-/unified-11.0.5.tgz"
  integrity sha512-xKvGhPWw3k84Qjh8bI3ZeJjqnyadK+GEFtazSfZv/rKeTkTjOJho6mFqh2SM96iIcZokxiOpg78GazTSg8+KHA==
  dependencies:
    "@types/unist" "^3.0.0"
    bail "^2.0.0"
    devlop "^1.0.0"
    extend "^3.0.0"
    is-plain-obj "^4.0.0"
    trough "^2.0.0"
    vfile "^6.0.0"

unist-util-is@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmjs.org/unist-util-is/-/unist-util-is-6.0.0.tgz"
  integrity sha512-2qCTHimwdxLfz+YzdGfkqNlH0tLi9xjTnHddPmJwtIG9MGsdbutfTc4P+haPD7l7Cjxf/WZj+we5qfVPvvxfYw==
  dependencies:
    "@types/unist" "^3.0.0"

unist-util-position@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/unist-util-position/-/unist-util-position-5.0.0.tgz"
  integrity sha512-fucsC7HjXvkB5R3kTCO7kUjRdrS0BJt3M/FPxmHMBOm8JQi2BsHAHFsy27E0EolP8rp0NzXsJ+jNPyDWvOJZPA==
  dependencies:
    "@types/unist" "^3.0.0"

unist-util-stringify-position@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/unist-util-stringify-position/-/unist-util-stringify-position-4.0.0.tgz"
  integrity sha512-0ASV06AAoKCDkS2+xw5RXJywruurpbC4JZSm7nr7MOt1ojAzvyyaO+UxZf18j8FCF6kmzCZKcAgN/yu2gm2XgQ==
  dependencies:
    "@types/unist" "^3.0.0"

unist-util-visit-parents@^6.0.0:
  version "6.0.1"
  resolved "https://registry.npmjs.org/unist-util-visit-parents/-/unist-util-visit-parents-6.0.1.tgz"
  integrity sha512-L/PqWzfTP9lzzEa6CKs0k2nARxTdZduw3zyh8d2NVBnsyvHjSX4TWse388YrrQKbvI8w20fGjGlhgT96WwKykw==
  dependencies:
    "@types/unist" "^3.0.0"
    unist-util-is "^6.0.0"

unist-util-visit@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/unist-util-visit/-/unist-util-visit-5.0.0.tgz"
  integrity sha512-MR04uvD+07cwl/yhVuVWAtw+3GOR/knlL55Nd/wAdblk27GCVt3lqpTivy/tkJcZoNPzTwS1Y+KMojlLDhoTzg==
  dependencies:
    "@types/unist" "^3.0.0"
    unist-util-is "^6.0.0"
    unist-util-visit-parents "^6.0.0"

universalify@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/universalify/-/universalify-0.2.0.tgz"
  integrity sha512-CJ1QgKmNg3CwvAv/kOFmtnEN05f0D/cn9QntgNOQlQF9dgvVTHj3t+8JPdjqawCHk7V/KA+fbUqzZ9XWhcqPUg==

unpipe@1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz"
  integrity sha512-pjy2bYhSsufwWlKwPc+l3cN7+wuJlK6uz0YdJEOlQDbl6jo/YlPi4mb8agUkVC8BF7V8NuzeyPNqRksA3hztKQ==

unplugin@1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/unplugin/-/unplugin-1.0.1.tgz"
  integrity sha512-aqrHaVBWW1JVKBHmGo33T5TxeL0qWzfvjWokObHA9bYmN7eNDkwOxmLjhioHl9878qDFMAaT51XNroRyuz7WxA==
  dependencies:
    acorn "^8.8.1"
    chokidar "^3.5.3"
    webpack-sources "^3.2.3"
    webpack-virtual-modules "^0.5.0"

unrs-resolver@^1.6.2:
  version "1.7.2"
  resolved "https://registry.npmjs.org/unrs-resolver/-/unrs-resolver-1.7.2.tgz"
  integrity sha512-BBKpaylOW8KbHsu378Zky/dGh4ckT/4NW/0SHRABdqRLcQJ2dAOjDo9g97p04sWflm0kqPqpUatxReNV/dqI5A==
  dependencies:
    napi-postinstall "^0.2.2"
  optionalDependencies:
    "@unrs/resolver-binding-darwin-arm64" "1.7.2"
    "@unrs/resolver-binding-darwin-x64" "1.7.2"
    "@unrs/resolver-binding-freebsd-x64" "1.7.2"
    "@unrs/resolver-binding-linux-arm-gnueabihf" "1.7.2"
    "@unrs/resolver-binding-linux-arm-musleabihf" "1.7.2"
    "@unrs/resolver-binding-linux-arm64-gnu" "1.7.2"
    "@unrs/resolver-binding-linux-arm64-musl" "1.7.2"
    "@unrs/resolver-binding-linux-ppc64-gnu" "1.7.2"
    "@unrs/resolver-binding-linux-riscv64-gnu" "1.7.2"
    "@unrs/resolver-binding-linux-riscv64-musl" "1.7.2"
    "@unrs/resolver-binding-linux-s390x-gnu" "1.7.2"
    "@unrs/resolver-binding-linux-x64-gnu" "1.7.2"
    "@unrs/resolver-binding-linux-x64-musl" "1.7.2"
    "@unrs/resolver-binding-wasm32-wasi" "1.7.2"
    "@unrs/resolver-binding-win32-arm64-msvc" "1.7.2"
    "@unrs/resolver-binding-win32-ia32-msvc" "1.7.2"
    "@unrs/resolver-binding-win32-x64-msvc" "1.7.2"

update-browserslist-db@^1.1.3:
  version "1.1.3"
  resolved "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz"
  integrity sha512-UxhIZQ+QInVdunkDAaiazvvT/+fXL5Osr0JZlJulepYu6Jd7qJtDZjlur0emRlT71EN3ScPoE7gvsuIKKNavKw==
  dependencies:
    escalade "^3.2.0"
    picocolors "^1.1.1"

uri-js@^4.2.2:
  version "4.4.1"
  resolved "https://registry.npmjs.org/uri-js/-/uri-js-4.4.1.tgz"
  integrity sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==
  dependencies:
    punycode "^2.1.0"

url-parse@^1.5.3:
  version "1.5.10"
  resolved "https://registry.npmjs.org/url-parse/-/url-parse-1.5.10.tgz"
  integrity sha512-WypcfiRhfeUP9vvF0j6rw0J3hrWrw6iZv3+22h6iRMJ/8z1Tj6XfLP4DsUix5MhMPnXpiHDoKyoZ/bdCkwBCiQ==
  dependencies:
    querystringify "^2.1.1"
    requires-port "^1.0.0"

use-callback-ref@^1.3.3:
  version "1.3.3"
  resolved "https://registry.npmjs.org/use-callback-ref/-/use-callback-ref-1.3.3.tgz"
  integrity sha512-jQL3lRnocaFtu3V00JToYz/4QkNWswxijDaCVNZRiRTO3HQDLsdu1ZtmIUvV4yPp+rvWm5j0y0TG/S61cuijTg==
  dependencies:
    tslib "^2.0.0"

use-sidecar@^1.1.3:
  version "1.1.3"
  resolved "https://registry.npmjs.org/use-sidecar/-/use-sidecar-1.1.3.tgz"
  integrity sha512-Fedw0aZvkhynoPYlA5WXrMCAMm+nSWdZt6lzJQ7Ok8S6Q+VsHmHpRWndVRJ8Be0ZbkfPc5LRYH+5XrzXcEeLRQ==
  dependencies:
    detect-node-es "^1.1.0"
    tslib "^2.0.0"

use-sync-external-store@^1.4.0, use-sync-external-store@^1.5.0:
  version "1.5.0"
  resolved "https://registry.npmjs.org/use-sync-external-store/-/use-sync-external-store-1.5.0.tgz"
  integrity sha512-Rb46I4cGGVBmjamjphe8L/UnvJD+uPPtTkNvX5mZgqdbavhI4EbgIWJiIHXJ8bc/i9EQGPRh4DwEURJ552Do0A==

usehooks-ts@^3.1.1:
  version "3.1.1"
  resolved "https://registry.npmjs.org/usehooks-ts/-/usehooks-ts-3.1.1.tgz"
  integrity sha512-I4diPp9Cq6ieSUH2wu+fDAVQO43xwtulo+fKEidHUwZPnYImbtkTjzIJYcDcJqxgmX31GVqNFURodvcgHcW0pA==
  dependencies:
    lodash.debounce "^4.0.8"

util-deprecate@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz"
  integrity sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==

uuid@^11.1.0:
  version "11.1.0"
  resolved "https://registry.npmjs.org/uuid/-/uuid-11.1.0.tgz"
  integrity sha512-0/A9rDy9P7cJ+8w1c9WD9V//9Wj15Ce2MPz8Ri6032usz+NfePxx5AcN3bN+r6ZL6jEo066/yNYB3tn4pQEx+A==

uuid@^9.0.0:
  version "9.0.1"
  resolved "https://registry.npmjs.org/uuid/-/uuid-9.0.1.tgz"
  integrity sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==

uuid@^9.0.1:
  version "9.0.1"
  resolved "https://registry.npmjs.org/uuid/-/uuid-9.0.1.tgz"
  integrity sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==

v8-compile-cache-lib@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/v8-compile-cache-lib/-/v8-compile-cache-lib-3.0.1.tgz"
  integrity sha512-wa7YjyUGfNZngI/vtK0UHAN+lgDCxBPCylVXGp0zu59Fz5aiGtNXaq3DhIov063MorB+VfufLh3JlF2KdTK3xg==

v8-to-istanbul@^9.0.1:
  version "9.3.0"
  resolved "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-9.3.0.tgz"
  integrity sha512-kiGUalWN+rgBJ/1OHZsBtU4rXZOfj/7rKQxULKlIzwzQSvMJUUNgPwJEEh7gU6xEVxC0ahoOBvN2YI8GH6FNgA==
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.12"
    "@types/istanbul-lib-coverage" "^2.0.1"
    convert-source-map "^2.0.0"

vary@^1, vary@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/vary/-/vary-1.1.2.tgz"
  integrity sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg==

vfile-message@^4.0.0:
  version "4.0.2"
  resolved "https://registry.npmjs.org/vfile-message/-/vfile-message-4.0.2.tgz"
  integrity sha512-jRDZ1IMLttGj41KcZvlrYAaI3CfqpLpfpf+Mfig13viT6NKvRzWZ+lXz0Y5D60w6uJIBAOGq9mSHf0gktF0duw==
  dependencies:
    "@types/unist" "^3.0.0"
    unist-util-stringify-position "^4.0.0"

vfile@^6.0.0:
  version "6.0.3"
  resolved "https://registry.npmjs.org/vfile/-/vfile-6.0.3.tgz"
  integrity sha512-KzIbH/9tXat2u30jf+smMwFCsno4wHVdNmzFyL+T/L3UGqqk6JKfVqOFOZEpZSHADH1k40ab6NUIXZq422ov3Q==
  dependencies:
    "@types/unist" "^3.0.0"
    vfile-message "^4.0.0"

w3c-xmlserializer@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/w3c-xmlserializer/-/w3c-xmlserializer-4.0.0.tgz"
  integrity sha512-d+BFHzbiCx6zGfz0HyQ6Rg69w9k19nviJspaj4yNscGjrHu94sVP+aRm75yEbCh+r2/yR+7q6hux9LVtbuTGBw==
  dependencies:
    xml-name-validator "^4.0.0"

walker@^1.0.8:
  version "1.0.8"
  resolved "https://registry.npmjs.org/walker/-/walker-1.0.8.tgz"
  integrity sha512-ts/8E8l5b7kY0vlWLewOkDXMmPdLcVV4GmOQLyxuSswIJsweeFZtAsMF7k1Nszz+TYBQrlYRmzOnr398y1JemQ==
  dependencies:
    makeerror "1.0.12"

web-streams-polyfill@^3.0.3:
  version "3.3.3"
  resolved "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-3.3.3.tgz"
  integrity sha512-d2JWLCivmZYTSIoge9MsgFCZrt571BikcWGYkjC1khllbTeDlGqZ2D8vD8E/lJa8WGWbb7Plm8/XJYV7IJHZZw==

web-streams-polyfill@4.0.0-beta.3:
  version "4.0.0-beta.3"
  resolved "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-4.0.0-beta.3.tgz"
  integrity sha512-QW95TCTaHmsYfHDybGMwO5IJIM93I/6vTRk+daHTWFPhwh+C8Cg7j7XyKrwrj8Ib6vYXe0ocYNrmzY4xAAN6ug==

web-vitals@^5.0.0:
  version "5.0.3"
  resolved "https://registry.npmjs.org/web-vitals/-/web-vitals-5.0.3.tgz"
  integrity sha512-4KmOFYxj7qT6RAdCH0SWwq8eKeXNhAFXR4PmgF6nrWFmrJ41n7lq3UCA6UK0GebQ4uu+XP8e8zGjaDO3wZlqTg==

webidl-conversions@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-3.0.1.tgz"
  integrity sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ==

webidl-conversions@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-7.0.0.tgz"
  integrity sha512-VwddBukDzu71offAQR975unBIGqfKZpM+8ZX6ySk8nYhVoo5CYaZyzt3YBvYtRtO+aoGlqxPg/B87NGVZ/fu6g==

webpack-sources@^3.2.3:
  version "3.2.3"
  resolved "https://registry.npmjs.org/webpack-sources/-/webpack-sources-3.2.3.tgz"
  integrity sha512-/DyMEOrDgLKKIG0fmvtz+4dUX/3Ghozwgm6iPp8KRhvn+eQf9+Q7GWxVNMk3+uCPWfdXYC4ExGBckIXdFEfH1w==

webpack-virtual-modules@^0.5.0:
  version "0.5.0"
  resolved "https://registry.npmjs.org/webpack-virtual-modules/-/webpack-virtual-modules-0.5.0.tgz"
  integrity sha512-kyDivFZ7ZM0BVOUteVbDFhlRt7Ah/CSPwJdi8hBpkK7QLumUqdLtVfm/PX/hkcnrvr0i77fO5+TjZ94Pe+C9iw==

whatwg-encoding@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/whatwg-encoding/-/whatwg-encoding-2.0.0.tgz"
  integrity sha512-p41ogyeMUrw3jWclHWTQg1k05DSVXPLcVxRTYsXUk+ZooOCZLcoYgPZ/HL/D/N+uQPOtcp1me1WhBEaX02mhWg==
  dependencies:
    iconv-lite "0.6.3"

whatwg-mimetype@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/whatwg-mimetype/-/whatwg-mimetype-3.0.0.tgz"
  integrity sha512-nt+N2dzIutVRxARx1nghPKGv1xHikU7HKdfafKkLNLindmPU/ch3U31NOCGGA/dmPcmb1VlofO0vnKAcsm0o/Q==

whatwg-url@^11.0.0:
  version "11.0.0"
  resolved "https://registry.npmjs.org/whatwg-url/-/whatwg-url-11.0.0.tgz"
  integrity sha512-RKT8HExMpoYx4igMiVMY83lN6UeITKJlBQ+vR/8ZJ8OCdSiN3RwCq+9gH0+Xzj0+5IrM6i4j/6LuvzbZIQgEcQ==
  dependencies:
    tr46 "^3.0.0"
    webidl-conversions "^7.0.0"

whatwg-url@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/whatwg-url/-/whatwg-url-5.0.0.tgz"
  integrity sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==
  dependencies:
    tr46 "~0.0.3"
    webidl-conversions "^3.0.0"

which-boxed-primitive@^1.0.2, which-boxed-primitive@^1.1.0, which-boxed-primitive@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/which-boxed-primitive/-/which-boxed-primitive-1.1.1.tgz"
  integrity sha512-TbX3mj8n0odCBFVlY8AxkqcHASw3L60jIuF8jFP78az3C2YhmGvqbHBpAjTRH2/xqYunrJ9g1jSyjCjpoWzIAA==
  dependencies:
    is-bigint "^1.1.0"
    is-boolean-object "^1.2.1"
    is-number-object "^1.1.1"
    is-string "^1.1.1"
    is-symbol "^1.1.1"

which-builtin-type@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/which-builtin-type/-/which-builtin-type-1.2.1.tgz"
  integrity sha512-6iBczoX+kDQ7a3+YJBnh3T+KZRxM/iYNPXicqk66/Qfm1b93iu+yOImkg0zHbj5LNOcNv1TEADiZ0xa34B4q6Q==
  dependencies:
    call-bound "^1.0.2"
    function.prototype.name "^1.1.6"
    has-tostringtag "^1.0.2"
    is-async-function "^2.0.0"
    is-date-object "^1.1.0"
    is-finalizationregistry "^1.1.0"
    is-generator-function "^1.0.10"
    is-regex "^1.2.1"
    is-weakref "^1.0.2"
    isarray "^2.0.5"
    which-boxed-primitive "^1.1.0"
    which-collection "^1.0.2"
    which-typed-array "^1.1.16"

which-collection@^1.0.1, which-collection@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/which-collection/-/which-collection-1.0.2.tgz"
  integrity sha512-K4jVyjnBdgvc86Y6BkaLZEN933SwYOuBFkdmBu9ZfkcAbdVbpITnDmjvZ/aQjRXQrv5EPkTnD1s39GiiqbngCw==
  dependencies:
    is-map "^2.0.3"
    is-set "^2.0.3"
    is-weakmap "^2.0.2"
    is-weakset "^2.0.3"

which-typed-array@^1.1.13, which-typed-array@^1.1.16, which-typed-array@^1.1.18:
  version "1.1.19"
  resolved "https://registry.npmjs.org/which-typed-array/-/which-typed-array-1.1.19.tgz"
  integrity sha512-rEvr90Bck4WZt9HHFC4DJMsjvu7x+r6bImz0/BrbWb7A2djJ8hnZMrWnHo9F8ssv0OMErasDhftrfROTyqSDrw==
  dependencies:
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.8"
    call-bound "^1.0.4"
    for-each "^0.3.5"
    get-proto "^1.0.1"
    gopd "^1.2.0"
    has-tostringtag "^1.0.2"

which@^2.0.1, which@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/which/-/which-2.0.2.tgz"
  integrity sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==
  dependencies:
    isexe "^2.0.0"

word-wrap@^1.2.5:
  version "1.2.5"
  resolved "https://registry.npmjs.org/word-wrap/-/word-wrap-1.2.5.tgz"
  integrity sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0":
  version "7.0.0"
  resolved "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz"
  integrity sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz"
  integrity sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^8.1.0:
  version "8.1.0"
  resolved "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-8.1.0.tgz"
  integrity sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==
  dependencies:
    ansi-styles "^6.1.0"
    string-width "^5.0.1"
    strip-ansi "^7.0.1"

wrappy@1:
  version "1.0.2"
  resolved "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz"
  integrity sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==

write-file-atomic@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-4.0.2.tgz"
  integrity sha512-7KxauUdBmSdWnmpaGFg+ppNjKF8uNLry8LyzjauQDOVONfFLNKrKvQOxZ/VuTIcS/gge/YNahf5RIIQWTSarlg==
  dependencies:
    imurmurhash "^0.1.4"
    signal-exit "^3.0.7"

write-file-atomic@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-6.0.0.tgz"
  integrity sha512-GmqrO8WJ1NuzJ2DrziEI2o57jKAVIQNf8a18W3nCYU3H7PNWqCCVTeH6/NQE93CIllIgQS98rrmVkYgTX9fFJQ==
  dependencies:
    imurmurhash "^0.1.4"
    signal-exit "^4.0.1"

ws@^8.11.0, ws@^8.18.0:
  version "8.18.2"
  resolved "https://registry.npmjs.org/ws/-/ws-8.18.2.tgz"
  integrity sha512-DMricUmwGZUVr++AEAe2uiVM7UoO9MAVZMDu05UQOaUII0lp+zOzLLU4Xqh/JvTqklB1T4uELaaPBKyjE1r4fQ==

xml-name-validator@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/xml-name-validator/-/xml-name-validator-4.0.0.tgz"
  integrity sha512-ICP2e+jsHvAj2E2lIHxa5tjXRlKDJo4IdvPvCXbXQGdzSfmSpNVyIKMvoZHjDY9DP0zV17iI85o90vRFXNccRw==

xmlchars@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/xmlchars/-/xmlchars-2.2.0.tgz"
  integrity sha512-JZnDKK8B0RCDw84FNdDAIpZK+JuJw+s7Lz8nksI7SIuU3UXJJslUthsi+uWBUYOwPFwW7W7PRLRfUKpxjtjFCw==

xtend@^4.0.0:
  version "4.0.2"
  resolved "https://registry.npmjs.org/xtend/-/xtend-4.0.2.tgz"
  integrity sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ==

y18n@^5.0.5:
  version "5.0.8"
  resolved "https://registry.npmjs.org/y18n/-/y18n-5.0.8.tgz"
  integrity sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==

yallist@^3.0.2:
  version "3.1.1"
  resolved "https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz"
  integrity sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==

yallist@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/yallist/-/yallist-5.0.0.tgz"
  integrity sha512-YgvUTfwqyc7UXVMrB+SImsVYSmTS8X/tSrtdNZMImM+n7+QTriRXyXim0mBrTXNeqzVF0KWGgHPeiyViFFrNDw==

yaml@^2.3.4:
  version "2.7.1"
  resolved "https://registry.npmjs.org/yaml/-/yaml-2.7.1.tgz"
  integrity sha512-10ULxpnOCQXxJvBgxsn9ptjq6uviG/htZKk9veJGhlqn3w/DxQ631zFF+nlQXLwmImeS5amR2dl2U8sg6U9jsQ==

yargs-parser@^21.1.1:
  version "21.1.1"
  resolved "https://registry.npmjs.org/yargs-parser/-/yargs-parser-21.1.1.tgz"
  integrity sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==

yargs@^17.3.1, yargs@^17.7.2:
  version "17.7.2"
  resolved "https://registry.npmjs.org/yargs/-/yargs-17.7.2.tgz"
  integrity sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w==
  dependencies:
    cliui "^8.0.1"
    escalade "^3.1.1"
    get-caller-file "^2.0.5"
    require-directory "^2.1.1"
    string-width "^4.2.3"
    y18n "^5.0.5"
    yargs-parser "^21.1.1"

yn@3.1.1:
  version "3.1.1"
  resolved "https://registry.npmjs.org/yn/-/yn-3.1.1.tgz"
  integrity sha512-Ux4ygGWsu2c7isFWe8Yu1YluJmqVhxqK2cLXNQA5AcC3QfbGNpM7fu0Y8b/z16pXLnFxZYvWhd3fhBY9DLmC6Q==

yocto-queue@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npmjs.org/yocto-queue/-/yocto-queue-0.1.0.tgz"
  integrity sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==

zod-to-json-schema@^3.24.1:
  version "3.24.5"
  resolved "https://registry.npmjs.org/zod-to-json-schema/-/zod-to-json-schema-3.24.5.tgz"
  integrity sha512-/AuWwMP+YqiPbsJx5D6TfgRTc4kTLjsh5SOcd4bLsfUg2RcEXrFMJl1DGgdHy2aCfsIA/cr/1JM0xcB2GZji8g==

zod@^3.23.8, zod@^3.24.2, zod@^3.24.4:
  version "3.24.4"
  resolved "https://registry.npmjs.org/zod/-/zod-3.24.4.tgz"
  integrity sha512-OdqJE9UDRPwWsrHjLN2F8bPxvwJBK22EHLWtanu0LSYr5YqzsaaW3RMgmjwr8Rypg5k+meEJdSPXJZXE/yqOMg==

zustand@^5.0.3:
  version "5.0.4"
  resolved "https://registry.npmjs.org/zustand/-/zustand-5.0.4.tgz"
  integrity sha512-39VFTN5InDtMd28ZhjLyuTnlytDr9HfwO512Ai4I8ZABCoyAj4F1+sr7sD1jP/+p7k77Iko0Pb5NhgBFDCX0kQ==

zwitch@^2.0.0:
  version "2.0.4"
  resolved "https://registry.npmjs.org/zwitch/-/zwitch-2.0.4.tgz"
  integrity sha512-bXE4cR/kVZhKZX/RjPEflHaKVhUVl85noU3v6b8apfQEc1x4A+zBxjZ4lN8LqGd6WZ3dl98pY4o717VFmoPp+A==
