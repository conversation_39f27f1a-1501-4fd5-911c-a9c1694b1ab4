{"name": "sabic<PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "eslint src/", "lint:changed": "scripts/lint-changed.sh", "lint:staged": "scripts/lint-changed.sh --staged", "lint:fix": "scripts/lint-changed.sh --fix", "validate": "tsc --noEmit && yarn lint", "test": "jest", "test:watch": "jest --watch", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,md}\"", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx,json,md}\""}, "dependencies": {"@anthropic-ai/sdk": "0.39.0", "@anthropic-ai/tokenizer": "^0.0.4", "@bugsnag/js": "^8.4.0", "@bugsnag/node": "^8.4.0", "@ferrucc-io/emoji-picker": "^0.0.44", "@google/generative-ai": "^0.24.0", "@hookform/resolvers": "^5.0.1", "@june-so/analytics-next": "^4.0.0", "@monaco-editor/react": "^4.7.0", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.8", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.6", "@sentry/nextjs": "^9", "@stripe/react-stripe-js": "^3.6.0", "@stripe/stripe-js": "^7.0.0", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.48.1", "@tanstack/react-virtual": "^3.13.8", "@types/lodash": "^4.17.16", "ai": "^4.1.45", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.4.10", "fuse.js": "^7.1.0", "js-tiktoken": "^1.0.19", "lodash": "^4.17.21", "lucide-react": "^0.510.0", "monaco-editor": "^0.52.2", "next": "15.1.7", "next-nprogress-bar": "^2.4.7", "next-themes": "^0.4.6", "openai": "^4.95.1", "react": "^19.0.0", "react-device-detect": "^2.2.3", "react-dom": "^19.0.0", "react-hook-form": "^7.56.3", "react-markdown": "^10.0.0", "react-split-pane": "^0.1.92", "react-syntax-highlighter": "^15.6.1", "react-use": "^17.6.0", "remark-gfm": "^4.0.1", "sonner": "^2.0.1", "stripe": "^18.0.0", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "usehooks-ts": "^3.1.1", "uuid": "^11.1.0", "zod": "^3.24.4", "zustand": "^5.0.3"}, "devDependencies": {"@babel/core": "^7.27.1", "@babel/preset-env": "^7.27.1", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.24.1", "@eslint/eslintrc": "^3", "@eslint/js": "^9.22.0", "@honeycombio/opentelemetry-web": "^0.20.0", "@next/eslint-plugin-next": "^15.2.1", "@opentelemetry/auto-instrumentations-node": "^0.60.1", "@opentelemetry/auto-instrumentations-web": "^0.48.0", "@opentelemetry/exporter-trace-otlp-http": "^0.202.0", "@testing-library/jest-dom": "^6.4.6", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.12", "@types/node": "^20", "@types/prop-types": "^15.7.14", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-syntax-highlighter": "^15.5.13", "eslint": "^9.22.0", "eslint-config-next": "15.1.7", "eslint-plugin-react": "^7.37.4", "globals": "^16.0.0", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "pino": "^9.6.0", "pino-pretty": "^13.0.0", "postcss": "^8", "prettier": "^3.5.3", "supabase": "^1.149.0", "tailwindcss": "^3.4.1", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "typescript": "^5", "typescript-eslint": "^8.26.0"}, "engines": {"node": ">=21.1.0"}, "repository": "**************:neddinn/sabichat.git", "author": "ne<PERSON>n <<EMAIL>>", "license": "MIT"}